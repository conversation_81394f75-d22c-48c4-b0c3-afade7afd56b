package models


type Store struct {
	Id          int    `xorm:"not null pk autoincr comment('自增id') INT(10)"`
	Name        string `xorm:"default '''' comment('门店名称') VARCHAR(50)"`
	FinanceCode string `xorm:"default 'NULL' comment('财务编码') VARCHAR(50)"`
	ZilongId    string `xorm:"default '''' comment('子龙门店id，systemid') VARCHAR(50)"`
	Shortname   string `xorm:"default '''' comment('门店简称') VARCHAR(56)"`
	StoreCode   string `xorm:"default '''' comment('店铺编码') VARCHAR(56) 'StoreCode'"`
	PointX      string `xorm:"default 'NULL' comment('定位信息经度') float 'PointX'"`
	PointY      string `xorm:"default 'NULL' comment('定位信息纬度') float 'PointY'"`
	Province    string `xorm:"default 'NULL' comment('省份') VARCHAR(56)"`
	City        string `xorm:"default 'NULL' comment('市') VARCHAR(56)"`
	County      string `xorm:"default 'NULL' comment('县区') VARCHAR(56)"`
	Address     string `xorm:"default 'NULL' comment('店铺地址') VARCHAR(128)"`
	Desc        string `xorm:"default 'NULL' comment('店铺简介') VARCHAR(256)"`
	CustomCode  string `xorm:"default 'NULL' comment('全渠道往来单位，A8') VARCHAR(50)"`
	ElmDelivery int    `xorm:"default 'NULL' comment('饿了么商户配送方式 9：蜂鸟快送，11：星火众包') int(3)"`
	Bigregion   string `xorm:"default 'NULL' comment('大区') VARCHAR(56)"`
	AppChannel  int32  `xorm:"default 1 comment('1.阿闻自有,2.TP代运营') INT(11)"`
}