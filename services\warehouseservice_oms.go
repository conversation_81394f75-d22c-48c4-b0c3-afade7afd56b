package services

import (
	"context"
	"dispatch-center/models"
	"dispatch-center/proto/dc"
	"errors"
	"net/http"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
)

//同步oms仓库
func (w *WarehouseService) SyncOmsWarehouse(ctx context.Context, req *dc.SyncOmsWarehouseRequest) (*empty.Empty, error) {
	glog.Infof("StockService/SyncOmsWarehouse 收到同步oms仓库 %s", kit.JsonEncode(req))
	conn := w.GetConn()
	defer conn.Close()

	var warehouseList []models.Warehouse
	err := conn.Where("code = ? AND category in (4 , 5)", req.Code).Find(&warehouseList)
	if err != nil {
		glog.Errorf("StockService/SyncOmsWarehouse 查询仓库是否存在异常:%+v %s", err, req.Code)
		return nil, err
	}

	// 指定要忽略记录操作日志
	ctx = context.WithValue(ctx, SkipWarehouseLog{}, true)

	// 仓库不存在则新增
	if len(warehouseList) == 0 {
		addReq := &dc.AddWarehouseRequest{
			Code:      req.Code,
			Thirdid:   req.Thirdid,
			Name:      req.Name,
			Comefrom:  1,
			Level:     1,
			Category:  req.Category,
			Address:   req.Address,
			Contacts:  req.Contacts,
			Tel:       req.Tel,
			Subsystem: 0,
			Ratio:     0,
			Lng:       0,
			Lat:       0,
			Region:    "",
			City:      "",
		}
		warehouseResp, err := w.AddWarehouse(ctx, addReq)
		if err != nil {
			glog.Errorf("StockService/SyncOmsWarehouse 新增仓库异常:%+v %s", err, kit.JsonEncode(addReq))
			return nil, err
		}
		if warehouseResp.Code != http.StatusOK {
			glog.Errorf("StockService/SyncOmsWarehouse 新增仓库失败:%d %s %s", warehouseResp.Code, warehouseResp.Message, kit.JsonEncode(addReq))
			return nil, errors.New(warehouseResp.Message)
		}
	} else {
		targetWarehouse := warehouseList[0]
		editReq := &dc.EditWarehouseRequest{
			Id:        int32(targetWarehouse.Id),
			Code:      req.Code,
			Name:      req.Name,
			Comefrom:  1,
			Level:     int32(targetWarehouse.Level),
			Category:  int32(targetWarehouse.Category),
			Address:   req.Address,
			Contacts:  req.Contacts,
			Tel:       req.Tel,
			Subsystem: int32(targetWarehouse.Subsystem),
			Ratio:     int32(targetWarehouse.Ratio),
			Status:    int32(targetWarehouse.Status),
			Thirdid:   req.Thirdid,
			Lng:       int64(targetWarehouse.Lng),
			Lat:       int64(targetWarehouse.Lat),
			Region:    targetWarehouse.Region,
			City:      targetWarehouse.City,
		}
		warehouseResp, err := w.EditWarehouse(ctx, editReq)
		if err != nil {
			glog.Errorf("StockService/SyncOmsWarehouse 更新仓库异常:%+v %s", err, kit.JsonEncode(editReq))
			return nil, err
		}
		if warehouseResp.Code != http.StatusOK {
			glog.Errorf("StockService/SyncOmsWarehouse 更新仓库失败:%d %s %s", warehouseResp.Code, warehouseResp.Message, kit.JsonEncode(editReq))
			return nil, errors.New(warehouseResp.Message)
		}
	}
	return &empty.Empty{}, nil
}

type SkipWarehouseLog struct {
}
