package utils

import (
	"fmt"
	"strconv"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
)

//redis链接
func ConnectClusterRedis() *redis.Client {
	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})
	//_, err := client.Ping().Result()
	//if err != nil {
	//	panic(err)
	//}
	return client
}

func RedisDealKeys(Patternkeys string) {
	client := ConnectClusterRedis()
	defer client.Close()
	var keylist []string
	//dbsize := client.DBSize().Val()
	//keys, _ := client.Scan(0, Patternkeys, dbsize).Val()
	keys1 := client.Keys(Patternkeys).Val()
	fmt.Println(keys1)
	for _, v := range keys1 {

		keylist = append(keylist, v)
	}
	if len(keylist) == 0 {
		return
	}
	client.Del(keylist...)
}

//
////集群
//func ConnectClusterRedis() *redis.ClusterClient {
//	address := config.GetString("redis.cluster")
//	pwd := config.GetString("redis.cluster.pwd")
//	addrs := strings.Split(address, "|")
//	client := redis.NewClusterClient(&redis.ClusterOptions{
//		Addrs:    addrs,
//		Password: pwd,
//	})
//	_, err := client.Ping().Result()
//	if err != nil {
//		panic(err)
//	}
//	return client
//}
//
////根据key模糊获取redis集群(多主多从)的所有key
//func ClusterRedisKey(Patternkeys string, client *redis.ClusterClient) []string {
//	pwd := config.GetString("redis.cluster.pwd")
//	clusterSlots, err := client.ClusterSlots().Result()
//	if err != nil {
//		return []string{}
//	}
//	allKey := make([]string, 0)
//	for _, v := range clusterSlots {
//		vclient := redis.NewClient(&redis.Options{
//			Addr:     v.Nodes[0].Addr,
//			Password: pwd,
//		})
//		hv := vclient.Keys(Patternkeys).Val()
//		for _, v := range hv {
//			allKey = append(allKey, v)
//		}
//		vclient.Close()
//	}
//	return allKey
//}
