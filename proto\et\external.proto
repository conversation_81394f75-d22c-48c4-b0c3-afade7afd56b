syntax = "proto3";
import "google/protobuf/empty.proto";
package et;

// 物流信息查询
service DeliveryService {
  //阿里云物流详情接口
  rpc AliInfo(DeliveryAliInfoRequest)returns(DeliveryAliInfoResponse);
}

//美团门店类API
service MtStoreService {
  //获取门店品类列表
  rpc GetCategoryList (CategoryListRequest) returns (CategoryListRequestResponse);

  //批量获取门店详细信息
  rpc GetMtStoreyList (MtStoreyListRequest) returns (MtStoreyListRequestResponse);

  //保存门店信息
  rpc MtStoreySave (StoreList1) returns (MtStoreyListRequestResponse);

  //查询美团配送范围信息
  rpc MtStoreDeliveryList (DeliveryListReq) returns (MtDeliveryListResponse);
  //创建/更新门店配送范围（自配送）
  rpc MtStoreDeliveryUpdate (MtDeliveryUpdateReq) returns (MtDeliveryListResponse);
  //删除门店配送范围（自配送）
  rpc MtStoreDeliveryDelete (MtDeliveryUpdateReq) returns (MtDeliveryListResponse);

  //批量同步美团门店营业状态
  rpc SyncMtStoreStatus (SyncStoreStatusRequest) returns (SyncStoreStatusResponse);
}

service MtProductService {

  ///   retail/sellStatus 批量更新售卖（上下架）状态
  rpc RetailSellStatus (RetailSellStatusRequest) returns (RetailSellStatusResult);

  ///   retailCat/list 查询门店商品分类列表       //  OK
  rpc RetailCatList (AppPoiCodeRequest) returns (AppPoiCodeResult);

  /// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]  （20次/秒。可传商品数据限定不能超过200组）
  rpc RetailBatchinitdata (RetailBatchinitdataRequest) returns (RetailSellStatusResult);

  ///    retail/initdata 创建/更新商品[支持商品多规格,不含删除逻辑]   50次/S
  rpc RetailInitdata (RetailInitdataRequest) returns (RetailSellStatusResult);

  /// retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店）
  rpc RetailMultipoisBatchinitdata (MultipoisBatchinitdataRequest) returns (RetailSellStatusResult);
  /// retailCat/update 创建/更新商品分类 当前接口最高调用5次/秒
  rpc RetailCatUpdate (RetailCatUpdateRequest) returns (RetailSellStatusResult);
  /// retail/getSpTagIds 获取美团后台商品类目（末级类目id） 20次/秒
  rpc RetailGetSpTagIds (AppPoiCodeRequest) returns (RetailGetSpTagIdsResult); // 杜彦

  //category/attr/list 根据末级类目id获取类目属性列表
  rpc CategoryAttrList (CategoryAttrListRequest) returns (CategoryAttrListResult); // 杜彦

  //category/attr/value/list 查询特殊属性的属性值列表
  rpc CategoryAttrValueList (CategoryAttrValueListRequest) returns (CategoryAttrValueListResult); // 杜彦

  /// retail/sku/delete 删除SKU信息 50次/秒
  rpc RetailSkuDelete (RetailSkuDeleteRequest) returns (RetailSellStatusResult);

  /// retail/sku/stock 批量更新SKU库存20次/秒,可传商品数据（app_food_code维度）限定不超过200组
  rpc  RetailSkuStock(RetailSkuStockRequest) returns  (RetailSellStatusResult);

  /// task/status 查询多店同步任务的进程
  rpc TaskStatus (TaskStatusRequest) returns (TaskStatusResult);
  //删除商品分类
  //接口限流：按app维度，当前接口最高调用5次/秒。
  //接口说明：
  //1.用于删除商品分类。
  //2.当分类下存在子级分类或商品时，不允许直接删除此分类。
  rpc MtRetailCatDelete (MtRetailCatDeleteRequest) returns (ExternalResponse);

  //批量更新SKU价格
  //retail/sku/price
  rpc MtRetailSkuPrice (MtRetailSkuPriceRequest) returns (ExternalResponse);
  ///   retailCat/list 查询门店商品分类列表       //  OK
  rpc RetailGet (RetailGetRequest) returns (RetailGetResult);

  // 批量查询商品详情 接口限流：按app维度，当前接口最高调用50次/秒。
  //接口说明：
  //1.用于查询商品信息详情。
  //2.每次调用最多支持查询100个商品数据。
  rpc RetailBatchGet (RetailBatchGetRequest) returns (RetailBatchGetResult);

  // 查询门店商品列表 接口限流：按app维度，当前接口最高调用20次/秒
  // 接口说明：
  // 1.此接口分页查的逻辑，是根据公式计算得到需查询的页码，再根据每页商品数量最终得到结果。(1)【offset】字段信息根据公式计算得到需查询的页码：页码(舍弃小数)=offset/limit+1；(2)【limit】字段信息表示每页商品数量。
  // 例如：offset=23，limit=5，根据公式计算结果表示从第5页开始查询，且每页5个商品，即本次请求结果将展示门店内第21～第25条商品数据。
  // 2.如不传分页字段，一次查询最多返回30200条商品数据；建议分多次查询。
  // 2.如果商品是在二级分类下面，则category_name为其一级分类，secondary_category_name为其所属分类（二级分类）。
  // 3.如果商品是在一级分类下面，则category_name为其所属分类，secondary_category_name字段为空。
  // 4.返回的商品信息默认是按sequence值升序排序。
  rpc RetailList (RetailListRequest) returns (RetailListResponse);
  //只更新分类
  rpc RetailInitDataCategory (RetailInitDataCategoryRequest) returns (RetailSellStatusResult);
}
//retail/get 按app维度，当前接口最高调用50次/秒。
message RetailGetRequest {
  // APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 1;
  // APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

//retail/get 按app维度，当前接口最高调用50次/秒。
message RetailGetResult {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  RetailGetData data = 4;
}

message RetailGetData {
  // APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 1;
  // APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 2;
  //商品名称
  string name = 3;
  //商品名称
  string description = 4;
  //商品价格
  float price = 5;
  //售卖状态
  int32 is_sold_out = 6;
}

message RetailBatchGetResult {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  repeated RetailBatchGetData data = 4;
}

message RetailBatchGetData{
  string  app_poi_code = 1;
  string app_spu_code = 2;
  int32 operation = 3;
  string name = 4;
  string description = 5;
  float price = 6;
  string picture = 7;
  string category_name = 8;
  string product_name = 9;

}

//美团订单类API
service MtOrderService {
  //获取美团订单详细信息
  rpc GetMtOrderDetail (MtOrderDetailRequest) returns (MtOrderDetailResponse);

  //商家确认美团订单
  rpc MtOrderConfirm (MtOrderConfirmRequest) returns (ExternalResponse);

  //商家取消美团订单
  rpc MtOrderCancel (MtOrderCancelRequest) returns (ExternalResponse);

  //自配送商家同步发货状态和配送信息
  //接口说明：
  //1.此接口用于同步自配送商家自配订单（除在B2C清单中的商家）和美团跑腿（原众包）订单的配送信息，包括物流平台、骑手名称、骑手电话，且此配送信息会同步展示在用户端订单详情的“配送信息”中。
  //2.从订单状态为“商家已确认”(status=4)到“订单已完成”(status=8)后的24小时之内，均可使用此接口创建和更新配送信息。
  //3.当同一个订单已经上传了一次配送信息，如再次同步会更新配送信息，以最新的一次为准。
  //4.如订单未发货，则不支持同步配送信息，订单已完成、已取消等状态发货将失败。
  rpc MtOrderLogisticsSync (MtOrderLogisticsSyncRequest) returns (ExternalResponse);

  //自配订单配送中（不支持同步骑手信息）
  //接口说明：
  //1.门店配送方式仅为自配送的商家在接单后，如商品已开始配送，可调用此接口同步订单配送状态“商品配送中”至用户端。
  //2.如需上传的配送员姓名和手机号信息同步到用户端，请对接使用接口【ecommerce/order/logistics/sync 自配送商家同步配送信息】
  //3.调用此接口不会改变订单状态，即订单状态仍为“商家已确认”(status=4)。
  //4.商家调用此接口同步配送状态的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
  //5.不支持向商家系统推送自配送订单的配送状态变更消息。
  //6.如商家门店已接入美团配送，订单为美团配送方式，配送状态会自动流转，不支持也无需使用此接口。
  rpc MtOrderDelivering (MtOrderDeliveringRequest) returns (ExternalResponse);

  //自配订单已送达
  //接口说明：
  //1.门店配送方式仅为自配送的商家在接单后，如商品已完成配送，可调用此接口同步订单配送状态“商品已送达”至用户端。
  //2.调用此接口不会改变订单状态，即订单状态仍为“商家已确认”(status=4)；如用户在用户端操作“确认收货”，订单状态将变更“订单已完成”(status=8)。
  //注：商家自配送订单和到店自取订单因不得知订单具体的完成时间，所以如用户未确认收货，平台通常会在每日凌晨将订单流程超过4小时且状态为未完成的订单（未超过预计送达时间的预订单除外），状态统一更新为“订单已完成”(status=8)。
  //3.商家调用此接口同步配送状态的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
  //4.不支持向商家系统推送自配送订单的配送状态变更消息。
  //6.如商家门店已接入美团配送，订单为美团配送方式，配送状态会自动流转，不支持也无需使用此接口。
  rpc MtOrderArrived (MtOrderArrivedRequest) returns (ExternalResponse);

  //查询订单状态
  //1.用于查询订单当前的状态，仅支持查询近30天内的订单。目前平台的订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。
  //2.订单状态≠订单配送状态，例如当订单配送已送达时，不代表订单状态已完成。关于订单已完成状态的相关场景说明如下：
  //1）美团专、快送的订单，正常情况下在骑手操作“商品已送达”后，无需用户点击确认收货，订单状态会自动变更为“订单已完成”（status=8）；特殊情况下，若骑手已送达商品，但订单状态未变更为已完成的情况，大多数原因是骑手可能在距离用户较远时操作了“商品已送达”；而为保护用户权益，系统暂时不会更新此订单状态为完成。通常在12个小时内如没有接到用户投诉未收到货等情况，系统会更新此订单状态为“订单已完成”（status=8），继而开放平台推送订单完成消息。
  //2）商家自配送订单和到店自取订单因不得知订单具体的完成时间，系统通常会在每日凌晨将订单流程超过4小时且状态为未完成的订单（未超过预计送达时间的预订单除外），统一更新为“订单已完成”（status=8）。
  //3.若用户申请了部分退款，无论退款是否成功都不会改变订单当前的状态。
  //4.若用户正在申请全额退款，退款申请中或申请被驳回，订单状态不会变化；当全额退款成功，订单取消时，无论当前订单状态是否为“订单已完成”，此时都会更新为“订单已取消”(status=9)。
  rpc MtOrderStatus (MtOrderStatusRequest) returns (ExternalResponse);

  //拉取用户真实手机号
  rpc MtUserInfoPhoneNumber (MtUserInfoPhoneNumberRequest) returns (MtUserInfoPhoneNumberResponse);

  //拉取骑手真实手机号
  rpc MtRiderInfoPhoneNumber (MtRiderInfoPhoneNumberRequest) returns (MtRiderInfoPhoneNumberResponse);

  rpc MtReviewAfterSales (MtReviewAfterSalesRequest) returns (ExternalResponse);

  //订单确认退款请求
  //1.当商家收到用户发起的部分或全额退款申请，如商家同意退款，可调用此接口操作确认退款申请。注意：部分退款成功后不影响订单当前的订单状态；全额退款成功后，订单状态会变更为“订单已取消”(status=9)。
  //2.商家调用此接口同意用户退款申请的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
  //3.若商家调用接口同意用户申请的全额退款，平台会向商家系统推送退款消息，但不会推送取消订单消息；若商家是在商家端后台手动操作同意全额退款，平台会推送退款消息和取消订单消息。
  rpc MtOrderRefundAgree (MtOrderRefundRequest) returns (ExternalResponse);
  //驳回订单退款申请
  //1.当商家收到用户发起的部分或全额退款申请，如商家拒绝申请，可调用此接口操作驳回退款申请。
  //注意：部分退款成功后不影响订单当前的订单状态；全额退款成功后，订单状态会变更为“订单已取消”(status=9)。
  //2.商家调用此接口驳回用户退款申请的操作会记录在开发者中心->订单查询->订单脚印 里，订单脚印页面仅支持查询近30天内的订单记录。
  //3.若商家调用接口驳回用户发起的退款申请，平台会向商家系统推送退款消息。
  rpc MtOrderRefundReject (MtOrderRefundRequest) returns (ExternalResponse);
  //批量拉取异常订单:
  //1.此接口用于商家批量获取近7日内的异常订单。
  //2.异常订单定义：
  //(1)推单失败：美团向商家系统推送已支付新订单消息4次都失败，且订单未被确认，会认为是异常订单，拉取此类异常订单时，end_time字段传入的时间需不早于第4次推送失败的时间。注意：推送已取消订单消息、订单已确认消息、订单已完成消息、订单修改消息等失败，不会生成异常订单。
  //(2)超时取消：美团向商家推送已支付新订单后，如商家超时未接单，会生成异常订单，拉取此类异常订单时，end_time字段传入的时间需不早于订单取消的时间。
  rpc MtBatchFetchAbnormalOrder (MtBatchFetchAbnormalOrderRequest) returns (ExternalResponse);
  //申请货损赔付
  //1.此接口用于商家向美团主动发起货损赔付申请。
  //2.关于订单货损赔付流程的说明：
  //⑴ 货损定责标准
  //① 因用户或商家自身原因（如备货慢、出错商品、货品问题等）造成用户退款，无法申请货损赔付，需由责任方自行承担。
  //② 因美团配送原因（如配送超时、接单后30分钟无配送动作、少送/错送/丢货/倾洒破损）造成用户退款，可线上申请赔付（48小时内申请有效），审核人员会根据实际情况进行审核。
  //③ 因美团商家端系统故障造成的货损可联系美团业务经理进行统一上报，总部进行审核后处理。
  //⑵ 操作流程
  //① 商家端后台申请货损赔付
  //订单管理-历史订单-无效订单-申请赔付。审核通过后即赔付，若审核未通过，可在120小时内“申诉”。
  //② 系统对接商家调用接口申请赔付
  //ⅰ 调用接口【order/getSupportedCompensation】查询门店内目前可申请货损赔付的订单；或调用接口【order/getCompensationResult】查询某个订单是否可发起货损赔付申请，此接口也支持查询订单当前的货损赔付结果。
  //ⅱ 通过接口【order/applyCompensation】申请符合标准的货损赔付。
  //⑶ 申请时效
  //货损赔付需要在顾客退款48小时内申请，如果超时视为主动放弃。
  rpc MtApplyCompensation (MtApplyCompensationRequest) returns (ExternalResponse);
  //查询可申请货损赔付的订单
  //1.此接口用于商家查询可向美团申请货损赔付的订单号。
  //2.关于订单货损赔付流程的说明：
  //⑴ 货损定责标准
  //① 因用户或商家自身原因（如备货慢、出错商品、货品问题等）造成用户退款，无法申请货损赔付，需由责任方自行承担。
  //② 因美团配送原因（如配送超时、接单后30分钟无配送动作、少送/错送/丢货/倾洒破损）造成用户退款，可线上申请赔付（48小时内申请有效），审核人员会根据实际情况进行审核。
  //③ 因美团商家端系统故障造成的货损可联系美团业务经理进行统一上报，总部进行审核后处理。
  //⑵ 操作流程
  //① 商家端后台申请货损赔付
  //订单管理-历史订单-无效订单-申请赔付。审核通过后即赔付，若审核未通过，可在120小时内“申诉”。
  //② 系统对接商家调用接口申请赔付
  //ⅰ 调用接口【order/getSupportedCompensation】查询门店内目前可申请货损赔付的订单；或调用接口【order/getCompensationResult】查询某个订单是否可发起货损赔付申请，此接口也支持查询订单当前的货损赔付结果。
  //ⅱ 通过接口【order/applyCompensation】申请符合标准的货损赔付。
  //⑶ 申请时效
  //货损赔付需要在顾客退款48小时内申请，如果超时视为主动放弃。
  rpc MtOrderGetSupportedCompensation (MtGetSupportedCompensationRequest) returns (ExternalResponse);
  //查询货损赔付结果
  //1.此接口用于商家查询某个订单的货损赔付结果，也支持查询订单是否可发起货损赔付申请。
  //2.关于订单货损赔付流程的说明：
  //⑴ 货损定责标准
  //① 因用户或商家自身原因（如备货慢、出错商品、货品问题等）造成用户退款，无法申请货损赔付，需由责任方自行承担。
  //② 因美团配送原因（如配送超时、接单后30分钟无配送动作、少送/错送/丢货/倾洒破损）造成用户退款，可线上申请赔付（48小时内申请有效），审核人员会根据实际情况进行审核。
  //③ 因美团商家端系统故障造成的货损可联系美团业务经理进行统一上报，总部进行审核后处理。
  //⑵ 操作流程
  //① 商家端后台申请货损赔付
  //订单管理-历史订单-无效订单-申请赔付。审核通过后即赔付，若审核未通过，可在120小时内“申诉”。
  //② 系统对接商家调用接口申请赔付
  //ⅰ 调用接口【order/getSupportedCompensation】查询门店内目前可申请货损赔付的订单；或调用接口【order/getCompensationResult】查询某个订单是否可发起货损赔付申请，此接口也支持查询订单当前的货损赔付结果。
  //ⅱ 通过接口【order/applyCompensation】申请符合标准的货损赔付。
  //⑶ 申请时效
  //货损赔付需要在顾客退款48小时内申请，如果超时视为主动放弃。
  rpc MtOrderGetCompensationResult (MtGetCompensationResultRequest) returns (MtGetCompensationResultResponse);

  //批量查询客服赔付商家责任订单信息
  //1.此接口用于商家查询因商家原因，美团客服向用户进行了赔付的订单信息。注意，因商家原因美团客服向用户赔偿订单损失，需由商家承担。
  //2.如商家对赔付结果有异议，可联系美团商服(电话10105557)申诉，并提供相关凭证。如申诉成功，将由美团承担相应扣款。
  //3.详细流程和规则请点击查看订单赔付规则
  rpc MtOrderBatchCompensation (MtOrderBatchCompensationRequest) returns (MtOrderBatchCompensationResponse);

  //下发美团配送订单
  //1.此接口为已接入美团配送(专送、快送、混合送)的商家主动下发美团配送订单。
  //2.如商家门店已接入美团配送(专送、快送、混合送)，订单将会在商家接单后即时的自动发往美团配送中心；如商家开通了延迟发配送功能，美团会根据门店的延迟发配送设置，自动下发至美团配送中心。商家无需重复调用此接口发配送。
  //3.自配送商家不支持使用此接口。
  rpc MtLogisticsPush (MtOrderStatusRequest) returns (ExternalResponse);

  //取消美团配送订单
  //1.此接口为已接入美团配送(专送、快送、混合送)的商家主动取消当前订单的美团配送方式，如订单当前不满足取消美团配送单的条件，则无法取消，例如订单状态已完成。
  //当订单发单（发往配送中心）后，商家可调用此接口取消美团配送单的操作时间预计=发单时间+15分钟+压单时长（0～15分钟）。
  //注：操作时间是按照发单后限制的15分钟+压单时长计算的。其中压单时长是配送侧动态计算返回的，目前压单时长一般不超过15分钟。（压单是指配送侧因调度等原因实施的配送单压单，配送状态code=5）
  //2.商家取消订单美团配送方式后，并不会取消订单，订单仍需商家自行安排配送。
  //3.自配送商家不支持使用此接口。
  rpc MtLogisticsCancel (MtOrderStatusRequest) returns (ExternalResponse);

  //查询众包配送费
  //此接口用于已接入美团众包配送的商家，在发众包配送前，批量查询不同订单发众包配送时所需的配送费。
  rpc MtGetShippingFee (MtShippingFeeRequest) returns (MtShippingFeeResponse);

  //众包发配送
  // 1.此接口用于已接入美团众包配送的商家主动发起众包配送。如未接入众包配送则不支持调用此接口进行相关操作。
  //2.发起配送前，商家需先调用接口【order/zhongbao/shippingFee 批量查询众包配送费】查询当前订单的配送费。发起配送时，shipping_fee字段需上传查询到的配送费金额，如不同则发配送失败。
  rpc MtDispatch (MtdispatchRequest) returns (ExternalResponse);

  //order/getCancelDeliveryReason 获取取消跑腿配送原因列表
  //本接口用于支持跑腿配送的商家取消跑腿配送单之前，查询取消原因（code+描述），以及当前取消跑腿配送单的影响，例如是否会有扣款。
  rpc MtGetCancelDeliveryReason (MtCancelDeliveryRequest) returns (MtCancelDeliveryResponse);

  //order/cancelLogisticsByWmOrderId 取消跑腿配送
  //  1.本接口用于支持跑腿配送的商家操作取消订单的跑腿配送单。目前校验的取消条件是：
  //  ① 订单状态： 4 ≤ status < 8
  //  ② 订单已经下发跑腿配送单
  //  ③ 配送状态：getLogistics_status ≤ 20（骑手已取货）
  //  2.请开发者一定要先调用【order/getCancelDeliveryReason】接口获取取消跑腿配送原因列表，若确认取消配送, 选择责任方后将相应地扣款，请商家据此自行评估是否要继续操作取消跑腿配送。
  rpc MtCancelLogisticsByWmOrderId (MtCancelLogisticsByWmOrderIdRequest) returns (ExternalResponse);
  /// order/preparationMealComplete 商家确认已完成拣货
  rpc OrderPreparationMealComplete (PreparationMealComplete) returns (ExternalResponse);
}

//查询众包配送费
message MtShippingFeeRequest {
  //美团单号
  string order_ids = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}
//查询众包配送费返回数据
message MtShippingFeeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //订单号，数据库中请用bigint(20)存储此字段。
  string wm_order_id = 4;
  //当前发众包配送所需的配送费，单位是元。
  double shipping_fee = 5;
  //A订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
  string wm_order_view_id = 6;
  //配送费备注信息
  string shipping_tips = 7;
}

//获取取消跑腿配送原因列表请求参数
message MtCancelDeliveryRequest {
  //订单id
  string order_id = 1;
  //美团门店号
  string app_poi_code = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

//美团拍腿去取消原因返回参数
message MtCancelDeliveryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  ReasonData Data = 4;
}

//取消原因列表
message ReasonList {
  //取消原因code
  string code = 1;
  //取消原因
  string content = 2;
  //预取消code
  int32 pre_cancel_code = 4;
  //预取消结果，如果取消可能会有收费等的信息
  string pre_cancel_msg = 5;
}

message ReasonData {
  //请求状态码
  int32 code = 1;
  //取消配送描述
  string title = 2;
  //取消原因列表
  repeated ReasonList list = 3;
  //获取原因列表成功！
  string msg = 4;
  //订单配送状态，参考附录对应配送状态枚举
  int32 delivery_status = 5;
}

//cancelLogisticsByWmOrderId  取消跑腿配送请求参数
message MtCancelLogisticsByWmOrderIdRequest {
  //取消原因code
  string reason_code = 1;
  //取消原因
  string detail_content = 2;
  //门店号
  string app_poi_code = 3;
  //订单id
  int64 order_id = 4;
  // 店铺主体Id
  int32 store_master_id = 5;
}

//order/zhongbao/dispatch 众包发配送
message MtdispatchRequest {
  //订单号
  int64 order_id = 1;
  //配送费，单位是元。此信息传入商家通过【批量查询众包配送费】接口查询到的当前订单的配送费。
  double shipping_fee = 2;
  //众包配送小费，单位是元。此字段传入商家给配送员的小费金额。
  double tip_amount = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}

service MtReturnOrderInfo {
  /// order/getPartRefundFoods 查询可被部分退款的商品详情
  // 1.商家接单后，在需要发起部分退款前，商家可调用此接口查询订单中可被部分退款的商品信息，包括按克重退差价和按件部分退这两种情况。
  // 2.不支持调用此接口查询的情况有：订单未接单、订单已取消、超过退款时效(订单完成后24小时)、订单中当前仅有1件商品。
  // 3.如购买多件活动商品，其中部分享受优惠价，当查询部分退款商品sku信息时，refund_price字段信息为单件商品sku可退款的价格，
  // 是将此商品sku优惠金额合计后进行等比分摊计算的结果。另外，订单维度的打包袋和配送费不参与均摊。
  // 例如购买商品A共2件，一件享受折扣价格2.4元，一件是原价3元，每件所需打包盒1个(0.5元/个)；则计算退款价格时，
  // 优惠金额0.6元会等比分摊到2件商品上(每件分摊优惠0.3元)，再加上每件打包盒费用，最终单件商品sku可退价格为3.2元。
  // 4.仅在线支付的订单且按重量售卖的商品，支持商家发起按克重退差价；同一件商品仅可发起1次退差价流程。
  // 5.如商品已按克重退过差价，再查询此接口时，refund_price字段信息是商品sku已扣减退差价金额后的可退款价格。
  rpc OrderGetPartRefundFoods (MtOrderRequest) returns (OrderPartRefuFoodsResponse);

  /// order/applyPartRefund 发起部分退款
  // 1.商家接单后，如需主动发起部分退款，可调用此接口操作。目前此接口支持商家发起的退款类型有2种：按克重退差价、按件部分退。
  // 2.不支持调用此接口发起部分退款的情况有：订单未接单、订单已取消、超过退款发起时效、订单中当前仅有1件商品。
  // 特殊说明：针对拦截理由为订单中当前仅有1件商品的情况，如商品按重量售卖，则支持商家调用此接口对该商品发起按克重退差价的流程。
  // 3.此接口不支持发起全部退流程，如商家需要退订单中当前全部商品，在支持商家主动取消订单的条件下，可调用接口【order/cancel 商家取消订单】操作取消订单。
  // 4.如购买多件活动商品，其中部分享受优惠价，当发起按件部分退款时，此商品sku退款价格是优惠金额合计后进行等比分摊计算的结果。另外，订单维度的打包袋和配送费不参与均摊。
  // 例如购买商品A共2件，一件享受折扣价格2.4元，一件是原价3元，每件所需打包盒1个(0.5元/个)；则计算退款价格时，
  // 优惠金额0.6元会等比分摊到2件商品上(每件分摊优惠0.3元)，再加上每件打包盒费用，最终单件商品sku可退价格为3.2元。
  // 如发起按克重退差价，退款价格是根据商品sku优惠后价格来计算的重量差异价格。
  // 5.仅在线支付的订单且按重量售卖的商品，支持商家发起按克重退差价；同一件商品仅可发起1次退差价流程；已退过差价的商品，仍可再进行按件部分退。
  // 6.同一次调用中，不支持同时发起按件部分退和按克重退差价两种流程。
  // 关于退差价的详细规则可参考业务指南 订单退差价规则
  // 7.目前，同一订单支持发起多次按件部分退款，无论发起方是用户还是商家，部分退款发起次数合计最多不超过10次。
  // 8.按克重退差价流程仅支持商家主动发起，发起时即表示同意按重量差异给用户进行退差价。
  rpc OrderApplyPartRefund (OrderApplyPartRefundRequset) returns (ExternalResponse);

  /// ecommerce/order/getOrderRefundDetail 获取订单退款记录
  // 1.此接口用于商家查询订单退款记录，仅支持近30天内的订单。
  // 2.同一订单如有多次退款，查询时返回信息会按退款次数分别记录。
  rpc getOrderRefundDetail (OrderRefundDetailRequest) returns (OrderRefundDetailResult);
}

service MpService {
  //门店创建
  //合作方提交门店信息（注意测试推单仍然用系统默认test_0001门店）
  rpc MpShopCreate (MpShopCreateRequest) returns (ExternalResponse);
  //查询门店信息
  //查询门店基本信息
  rpc MpShopQuery (MpShopQueryRequest) returns (MpShopQueryResponse);
  //查询合作方配送范围
  //根据配送服务代码、取货门店id获取该门店支持的配送范围
  //同一AppKey每秒超过2次访问将被限流，返回{"code":11,"message":"接口流控"}。可以通过降低查询频次解决。
  rpc MpShopAreaQuery (MpShopAreaQueryRequest) returns (ExternalResponse);
  //预发单接口(门店方式)
  rpc MpOrderPreCreate (MpOrderCreateRequest) returns (ExternalResponse);

  //订单创建(门店方式)
//合作方根据已录入的门店信息 将订单发送给美团配送平台
  rpc MpOrderCreate (MpOrderCreateRequest) returns (ExternalResponse);
  //取消订单
  //订单在未完成状态下，合作方可以取消订单。
  rpc MpOrderDelete (MpOrderDeleteRequest) returns (ExternalResponse);
  //查询订单状态
  //查询订单状态及对应的骑手信息（仅支持90天内产生的订单）
  //同一AppKey每秒超过10次访问将被限流，返回{"mtcode":11,"message":"接口流控"}。可以通过降低查询频次解决。
  rpc MpOrderStatusQuery (MpOrderStatusQueryRequest) returns (MpOrderStatusQueryResponse);
  //配送能力校验
  //根据合作方提供的模拟发单参数，确定美团是否可配送。主要校验项：门店是否存在、门店配送范围、门店营业时间、门店支持的服务包。
  //同一AppKey每秒超过30次访问将被限流，返回{"mtcode":11,"message":"接口流控"}
  rpc MpOrderCheck (MpOrderCheckRequest) returns (ExternalResponse);
  //获取骑手当前位置
  //通过订单 id 获取当前订单下骑手的实时位置信息，需要注意以下几点：
  //1. 骑手位置的更新频率为 30s；
  //2. 接口返回的骑手坐标为火星坐标（高德地图和腾讯地图均采用火星坐标），而非百度坐标；
  //3. 接口返回的坐标量级为真实坐标的基础上乘以 10 的 6 次方的整数，例如：骑手真实坐标为（116.255596, 40.029185），则接口返回结果为（116255596, 40029185）；
  //4. 订单状态只有为“已接单”和“已取货”时，才会返回骑手位置信息，
  rpc MpOrderRiderLocation (MpOrderRiderLocationRequest) returns (ExternalResponse);
  //自由达订单创建(无门店方式)
  //合作方根据已录入的门店信息 将订单发送给美团配送平台
  rpc MpNotShopOrderCreate (MpNotShopOrderCreateRequest) returns (ExternalResponse);
}

//饿了么商品类API
service ElmProductService {
  //新增修改商品分类
  rpc NewElmShopCategory (NewElmShopCategoryRequest) returns (NewElmShopCategoryResponse);
  //删除商品分类
  rpc DelElmShopCategory (DelElmShopCategoryRequest) returns (DelElmShopCategoryResponse);
  //获取商品品牌列表
  rpc GetElmShopBrand (GetElmShopBrandRequest) returns (GetElmShopBrandResponse);
  //批量修改价格
  rpc UpdateElmShopSkuPrice (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);
  //批量商品上架
  rpc OnlineElmShopSku (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);
  //批量商品上架-单个
  rpc OnlineElmShopSkuOne (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);
  //批量商品下架
  rpc OfflineElmShopSku (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);
  //批量商品下架-单个
  rpc OfflineElmShopSkuOne (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);
  //更新饿了么商品信息
  rpc UpdateElmShopSku (UpdateElmShopSkuRequest) returns (ELMBaseResponse);
  // 批量更新eleme商品
  rpc BatchUpdateElmShopSku (BatchUpdateElmShopSkuRequest) returns (ELMBaseResponse);

  // 批量创建商品
  rpc BatchCreateElmShopSku (BatchCreateElmShopSkuRequest) returns (ELMBaseResponse);
  //更新饿了么商品图片
  rpc UploadPicture (UploadPictureRequest) returns (DelElmShopCategoryResponse);
  //更新饿了么商品富文本
  rpc UploadPictureRTF (UploadPictureRequest) returns (DelElmShopCategoryResponse);
  //单个修改商品价格
  rpc SkuPriceUpdateOne (SkuPriceUpdateOneRequest) returns (ELMBaseResponse);

  //获取饿了么商品列表
  rpc GetElmProductList (ElmGetProductListRequest) returns (ElmGetProductListResponse);

  //更新eleMe库存
  rpc UpdateElmSkuStock (UpdateElmSkuStockRequest) returns (UpdateElmSkuStockResponse);

  // 删除饿了me商品
  rpc DeleteElmShopSku (UpdateElmShopSkuPriceRequest) returns (ELMBaseResponse);

}

//美团批量更新SKU价格
message MtRetailSkuPriceRequest {
  //APP方门店id
  string app_poi_code = 1;
  //商品sku价格集合的json数据
  string food_data = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

message food_data_mt {
  string app_food_code = 1;
  repeated food_sku skus = 2;
}

message food_sku {
  //skus(必填项)，商品sku信息集合的json数组
  string sku_id = 1;
  //price(必填项)，为sku的价格，单位是元
  string price = 2;
}

//单个修改商品价格 请求参数
message SkuPriceUpdateOneRequest {
  //合作方门店ID
  string shopId = 1;
  //sku_id对应销售价格与市场价格
  string skuid_price = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
}

//饿了么门店类API
service ElmStoreService {
  //获取门店品类列表
  rpc GetElmCategoryList (CategoryListRequest) returns (ElmCategoryListResponse);
  //获取门店详细信息
//  rpc GetElmStoreInfo (ElmStoreInfoRequest) returns (ElmStoreInfoResponse);
  // 批量同步饿了吗门店营业状态
  rpc SyncElmStoreStatus (SyncStoreStatusRequest) returns (SyncStoreStatusResponse);
  //查看饿了么商户信息
  rpc GetElmStorey (ElmStoreyGetRequest) returns (ElmStoreyGetResponse);
  //修改饿了么商户信息
  rpc UpdateElmStorey (ElmStoreySetRequest) returns (ElmStoreySetResponse);
  //查看饿了么商户营业状态
  rpc GetElmBusStatus (ElmBusStatusGetRequest) returns (ElmBusStatusGetResponse);
  //设置饿了么商户营业状态开业
  rpc ElmOpen (ElmBusStatusSetRequest) returns (ElmBusStatusSetResponse);
  //设置饿了么商户营业状态休息
  rpc ElmClose (ElmBusStatusSetRequest) returns (ElmBusStatusSetResponse);
  //查看饿了么商户公告
  rpc GetElmAnnouncement (ElmAnnouncementGetRequest) returns (ElmAnnouncementGetResponse);
  //设置饿了么商户公告
  rpc SetElmAnnouncement (ElmAnnouncementSetRequest) returns (ElmAnnouncementSetResponse);

  //查询饿了么配送范围
  rpc GetElmDeliveryInfo (ElmStoreyGetRequest) returns (ElmDeliveryInfoRes);
  //设置饿了么配送范围
  rpc UpDateElmDelivery (ElmStoreUpdateRequest) returns (ElmDeliveryInfoRes);
}

//通用返回
message BaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
}

message CategoryListRequest {
  //搜索关键字
  string keyword = 1;
  // 父类id
  string parent_id = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
}

//获取美团门店的所有品类
message CategoryListRequestResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  //品类集合
  repeated StoreCategoryList StoreCategoryList = 4;

}
//美团门店品类
message StoreCategoryList {
  //品类id
  int32 id = 4;
  //品类名称
  string name = 5;
}

/// lei start

///   retail/sellStatus 批量更新售卖（上下架）状态 Start
// 批量更新售卖（上下架）状态 请求参数
message RetailSellStatusRequest {
  // APP方门店id
  string app_poi_code = 1;
  //商品id集合的json数据
  repeated AppFoodCode food_data = 2;
  // 商品的售卖状态，取值范围：1-下架，0-上架。
  int32 sell_status = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}

// 商品id
message AppFoodCode {
  // 商品id
  string app_food_code = 1;
}

//   批量更新售卖（上下架）状态  返回结果
message RetailSellStatusResult {
  // 返回成功与否
  string data = 1;
  // 返回信息
  string msg = 2;
  // 错误 信息
  retailSellStatusResultError Error = 3;
  // 返回码
  int32 Code = 4;
  // 返回信息
  string message = 5;

  repeated error_list_mt error_list = 6;

  // 	美团返回的：1-全部操作成功，如需获取具体成功信息可以查看success_list或者success_map字段；2-部分成功，成功的数据存储在success_list或者success_map字段，失败的数据存在error_list字段中；3-全部操作失败，失败的数据存在error_list字段中；4-请求失败，
  int32 result_code = 7;
}

message error_list_mt {
  string app_spu_code = 1;
  string msg = 2;
	// (1)blockFlag=1，即新增或修改商品失败，返回失败原因。(2)blockFlag=2，即新增或修改商品成功，但返回相关说明提示，如因类目错挂平台替换类目。必填信息缺失。商品为先审后发审核中或审核驳回，不可上架但其他字段保存成功等。 (3)blockFlag为空，一般为非阻塞型数据校验（注意优先看result_code）。
  int32 blockFlag=3;
  int32 code = 4;

}

message retailSellStatusResultError {
  // 错误代码
  int32 code = 1;
  // 错误 信息
  string msg = 2;
}
///   retail/sellStatus 批量更新售卖（上下架）状态 end

/// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]  （20次/秒。可传商品数据限定不能超过200组） start

// 普通属性列表
message ValueList {
  // 普通属性值Id
  int64 valueId = 1;
  // 普通属性值名称
  string value = 2;
}
// 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
message CommonAttrValue {
  // 普通属性Id
  int64 attrId = 1;
  // 普通属性名称
  string attrName = 2;
  // 普通属性列表
  repeated ValueList valueList = 3;
}

// 表示sku可售时间 非必镇
message AvailableTimes {
  string monday = 1;
  string tuesday = 2;
  string wednesday = 3;
  string thursday = 4;
  string friday = 5;
  string saturday = 6;
  string sunday = 7;
}

// 商品销售属性 非必镇
message OpenSaleAttrValue {
  //  销售属性id，不支持自定义
  int64 attrId = 1;
  // 销售属性值id。当属性值录入方式为文本时，该参数无需上传
  int64 valueId = 2;
  //  销售属性值，支持自定义
  string value = 3;
}

// Sku信息 非必镇
message SkuParam {
  // 是sku唯一标识码 必镇
  string sku_id = 1;
  // sku的规格名称  建时门店启用类目属性且skus中传递了销售属性则为非必填(会自动根据销售属性组合其规格)，其余情况参考字段描述里规则。 更新商品时，本参数非必填。
  string spec = 2;
  // 为sku的商品包装上的条形码编号，UPC/EAN码；字符长度8位或者13位 非必镇
  string upc = 3;
  // 为sku的价格  创建时必填
  string price = 4;
  // sku的库存量 创建时必填
  string stock = 5;
  // 商品sku的售卖单位 非必镇
  string unit = 6;
  // 一个订单中此商品的最小购买量   非必镇
  int32 min_order_count = 7;
  // 表示sku可售时间 非必镇
  AvailableTimes available_times = 8;
  // 表示sku的料位码 非必镇
  string location_code = 9;
  // 包装费老计费规则，表示：商品sku单件需使用打包盒的数量 非必镇
  string box_num = 10;
  //  包装费老计费规则，表示：商品sku单个打包盒的价格，单位是元，不能为负数 非必镇
  string box_price = 11;
  // 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的M  非必镇
  string ladder_box_num = 12;
  // 包装费阶梯计价规则，表示：每M件商品收取N元包装费中的N。 非必镇
  string ladder_box_price = 13;
  //  表示sku的重量   否，与weight_for_unit和weight_unit至多填写一个
  //int64 weight=12;
  // 表示sku的重量数值信息  创建时，如填写weight_unit，则weight_for_unit必填且与weight至多填写一个，否则非必填
  string weight_for_unit = 14;
  // 表示sku的重量数值单位，枚举值如下： 1."克(g)" 2."千克(kg)" 3."毫升(ml)" 4."升(L)" 5."磅" 6."斤" 7."两"。
  // 创建时，如填写weight_for_unit，则weight_unit必填且与weight至多填写一个，否则非必填
  string weight_unit = 15;
  // 商品销售属性 非必镇
  repeated OpenSaleAttrValue openSaleAttrValueList = 16;
}

// 属性信息 非必镇
message Propertie {
  // 属性名称，字段信息限定长度不超过10个字符。最多支持传10组属性。不允许上传emoji等表情符  若有properties参数则必须填
  string property_name = 1;
  // 属性值 若有properties参数则必须填
  string values = 2;
}

// 限购信息  非必镇
message LimitSaleInfo {
  // 是否限制购买数量 必镇
  bool limitSale = 1;
  // 限购规则： 1-限制下单顾客每X天限购数量，X为frequency，不传默认为1；2-限制整个周期内下单顾客限购数量 如限购开启则必填
  int32 type = 2;
  // 限购循环天数： 最大31，最小1。 非必镇
  int32 frequency = 3;
  // 限购开始日期 如限购开启则必填
  string begin = 4;
  // 限购数量  如限购开启则必填
  int32 count = 5;
  // 限购结束日期 如限购开启则必填
  string end = 6;
}

message RetailBatchinitdata {
  //APP方商品id，  必镇
  string app_food_code = 1;
  // 单个商品需使用的打包盒数量  非必填
  float box_num = 2;
  // 单个打包盒的价格，单位是元，需在0-100之间 非必填
  float box_price = 3;
  //  分类名称    创建时：category_code与category_name、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
  string category_name = 4;
  // 商品上下架状态，字段取值范围：0-上架，1-下架。  非必镇
  int32 is_sold_out = 5;
  // 一个订单中此商品的最小购买量   必填
  int32 min_order_count = 6;
  // 商品名称(总称：如电风机) 创建时必填
  string name = 7;
  // 商品描述  非必镇
  string description = 8;
  // 商品的售卖单位 非必镇
  string unit = 9;
  // 商品图片  非必镇
  string picture = 10;
  // 	 商品在当前分类下的排序  非必镇
  int32 sequence = 11;
  // 商品价格 创建时，如不传skus信息，则本参数必填。
  float price = 12;
  // 美团内部商品类目id 门店启用类目属性并且传递了销售或普通属性则  1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。 门店未启用类目属性或未传递销售属性/普通属性则非必传。
  int64 tag_id = 13;
  // 商品的品牌名称 非必镇
  string zh_name = 14;
  // 完整产品名称（如：非力蒲电风机）
  string product_name = 15;
  // 商品的产地 非必镇
  string origin_name = 16;
  //  功能 非必镇
  string flavour = 17;
  // 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
  repeated CommonAttrValue common_attr_value = 18;
  // APP方商品的skus信息字符串，支持同时传多个sku信息  非必镇
  repeated SkuParam skus = 19;
  // 商品的图片详情 非必镇
  string picture_contents = 20;
  // 商品属性 非必镇
  repeated Propertie properties = 21;
  // 是否为“力荐”商品，字段取值范围：0-否， 1-是。  非必镇
  int32 is_specialty = 22;
  // 视频ID 非必镇
  int64 video_id = 23;
  // 商品限购详情  非必镇
  LimitSaleInfo limit_sale_info = 24;
  //分类ID  非必镇
  int32 category_id = 25;
  // 分类的code
  string category_code = 26;
  // 商品卖点
  string sell_point = 27;
}

//  retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]    请求参数
message RetailBatchinitdataRequest {
  // APP方门店id，即商家中台系统里门店的编码 必镇
  string app_poi_code = 1;
  // 当次调用的操作类型 非必镇
  int32 operate_type = 2;
  // 多个商品数据集合的json格式数组  必镇
  repeated RetailBatchinitdata food_data = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}
/// retail/batchinitdata 批量创建/更新商品[支持商品多规格,不含删除逻辑]  （20次/秒。可传商品数据限定不能超过200组） end

///    retail/initdata 创建/更新商品[支持商品多规格,不含删除逻辑]   50次/S   strat

message RetailInitdataRequest {
  // APP方门店id  必镇
  string app_poi_code = 1;
  // 本次调用的操作类型标识：(1)字段取值范围：1-创建，2-更新；  非必镇
  int32 operate_type = 2;
  // APP方商品id，即商家中台系统里商品的编码（spu_code值）必镇
  string app_food_code = 3;
  // 商品名称：(1)此字段信息限定长度不超过30个字符； // 创建时必填
  string name = 4;
  // 商品描述，  非必镇
  string description = 5;
  //  APP方商品的skus信息字符串  非必镇
  repeated SkuParam skus = 6;
  // 商品价格，单位是元；最多支持2位小数，不能为负数；     创建时，如不传skus信息，则本参数必填。
  float price = 7;
  // 一个订单中此商品的最小购买量。创建商品时，min_order_count字段信息如不传则默认为1  非必镇
  int32 min_order_count = 8;
  //    商品的售卖单位  非必镇
  string unit = 9;
  //  单个商品需使用的打包盒数量，需在0-100之间。 非必镇
  float box_num = 10;
  //  单个打包盒的价格，单位是元，  非必镇
  float box_price = 11;
  // 分类id   创建时：category_code与category_name、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
  string category_code = 12;
  //  分类名称创建时：category_name与category_code、category_code_list、category_name_list字段必须且只能填写一个 更新时非必填
  string category_name = 13;
  //  分类id列表，用于同步商品多个分类信息。 创建时：category_code_list与category_name、category_code、category_name_list字
  // 段必须且只能填写一个 更新时非必填
  string category_code_list = 14;
  // 分类名称列表，用于同步商品多个分类信息 创建时：category_name_list与category_name、category_code、category_code_list
  // 字段必须且只能填写一个 更新时非必填
  string category_name_list = 15;
  //  商品上下架状态，字段取值范围：0-上架，1-下架。  非必镇
  int32 is_sold_out = 16;
  //  商品图片：  非必镇
  string picture = 17;
  //  商品在当前分类下的排序 非必镇
  int32 sequence = 18;
  // 美团内部商品类目id  门店启用类目属性并且传递了销售或普通属性则  1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。
  // 门店未启用类目属性或未传递销售属性/普通属性则非必传。
  int64 tag_id = 19;
  // 商品的品牌名称 非必镇
  string zh_name = 20;
  // 商品的产地  非必镇
  string origin_name = 21;
  //  商品的图片详情 非必镇
  string picture_contents = 22;
  // 商品属性 非必镇
  repeated Propertie properties = 23;
  // 是否为“力荐”商品 百必镇
  int32 is_specialty = 24;
  // 视频ID 非必镇
  int64 video_id = 25;
  // 商品普通属性  若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
  repeated CommonAttrValue common_attr_value = 26;
  // 商品限购详情  非必镇
  LimitSaleInfo limit_sale_info = 27;
  // 分类ID  非必镇
  int32 category_id = 28;
  // 店铺主体Id
  int32 store_master_id = 29;
  // 商品卖点
  string sell_point = 30;
}

///    retail/initdata 创建/更新商品[支持商品多规格,不含删除逻辑]   50次/S   end
/// retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店） start
message RetailInfo {
  // APP方商品id，即商家中台系统里商品的编码 必镇
  string app_food_code = 1;
  // 商品名称 创建时必镇
  string name = 2;
  // 商品描述  非必镇
  string description = 3;
  // APP方商品的skus信息，支持同时传多个sku信息。
  repeated SkuParam skus = 4;
  // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
  int32 min_order_count = 5;
  // 商品的售卖单位。创建商品时，unit字段信息如不传则默认为“份”。 非必镇
  string unit = 6;
  // 分类名称 创建时必填
  string category_name = 7;
  // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
  int32 is_sold_out = 8;
  // 商品图片： 非必镇
  string picture = 9;
  // 商品在当前分类下的排序：非必镇
  int32 sequence = 10;
  // 美团内部商品类目id
  // 门店启用结构化属性并且传递了销售或普通属性则
  // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
  int64 tag_id = 11;
  // 商品品牌 非必镇
  string zh_name = 12;
  // 商品的产地 非必镇
  string origin_name = 13;
  // 商品的图片详情 非必镇
  string picture_contents = 14;
  // 是否为“力荐”商品，字段取值范围：0-否， 1-是。 非必镇
  int32 is_specialty = 15;
  // 视频ID 非必镇
  int64 video_id = 16;
  //  商品普通属性的json字符串 若美团内部类目tag_id对应的标准普通类目属性有必填项，则创建时必填，否则创建/更新均非必填
  repeated CommonAttrValue common_attr_value = 17;
  // 商品限购详情 非必镇
  LimitSaleInfo limit_sale_info = 18;
  // 分类ID 非必镇
  int32 category_id = 19;
  // 分类的code
  string category_code = 20;
  // 商品卖点
  string sell_point = 21;
}

message InitData {
  // 当次调用的操作类型  1-创建，2-更新。   必镇
  int32 type = 1;
  // is_all_pois=1，代表同步商品信息至该应用app下全部门店；如此字段传其他值，则返回错误信息，本次调用全部失败。
  // is_all_pois与app_poi_codes字段必须且只能填写一个
  int32 is_all_pois = 2;
  // APP方门店id 即商家中台系统里门店的编码 is_all_pois与app_poi_codes字段必须且只能填写一个
  string app_poi_codes = 3;
  // 多个商品数据集合的json格式数组：(1)可传商品数据限定不能超过200组
  repeated RetailInfo retail_info = 4;
}

/// retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店）
// 应用级参数
message MultipoisBatchinitdataRequest {
  InitData init_data = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

message MultipoisBatchinitdataResult {
  int32 data = 1;
}
/// retail/multipois/batchinitdata 批量创建/更新商品信息至多店 （1次/5分钟，200个商品，最多可同步至3000个门店） end

/// retailCat/update 创建/更新商品分类   start
message RetailCatUpdateRequest {
  // APP方门店id 必镇
  string app_poi_code = 1;
  // 原始的商品分类id     与category_name_origin至多填写一个
  string category_code_origin = 2;
  //  原始的商品分类名称 与category_code_origin至多填写一个
  string category_name_origin = 3;
  // 商品分类id 非必镇
  string category_code = 4;
  // 商品分类名称 必镇
  string category_name = 5;
  //  二级商品分类id  非必镇
  string secondary_category_code = 6;
  // 二级商品分类名称 非必镇
  string secondary_category_name = 7;
  // 商品分类的排序 非必镇
  int32 sequence = 8;
  //  调整分类层级时的目标等级   非必镇
  int32 target_level = 9;
  //  调整为二级分类时所属的一级分类名称  仅当target_level=2时填写
  string target_parent_name = 10;
  //  是否开启置顶 非必镇
  int32 top_flag = 11;
  // 置顶周期 当top_flag=1时必填，且需要与period字段同时设置。
  string weeks_time = 12;
  // 置顶时段   当top_flag=1时必填，且需要与weeks_time字段同时设置
  string period = 13;
  // 店铺主体Id
  int32 store_master_id = 14;
}

/// retailCat/update 创建/更新商品分类   start

// retailCat/list 查询门店商品分类列表  start
// 请求参数
message AppPoiCodeRequest {
  // APP方门店id，即商家中台系统里门店的编码
  string app_poi_code = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}
// 响应参数
message AppPoiCodeResult {
  // 包含二级分类的一级分类信息
  repeated AppPoiCodeTop data = 1;
  int32 code = 2;
  string message = 3;
  string error = 4;
}
// 一级分类信息
message AppPoiCodeTop {
  // 一级分类id
  string code = 1;
  // 一级分类名称
  string name = 2;
  // 一级分类的排序
  int32 sequence = 3;
  // 是否开启置顶 1)字段取值范围：0-否，1-是；(2)仅支持一级分类设置分时置顶。
  int32 top_flag = 4;
  // 置顶周期(1)1,2,3,4,5,6,7分别表示周一至周日；(2)多个日期之间用英文逗号分隔；(3)仅支持一级分类设置分时置顶。
  string weeks_time = 5;
  // 置顶时段：(1)最多支持设置5个时间段，多个时间段之间用英文逗号分隔；(2)若设置多个时间段，则前一个时间段的结束时间不能晚于后一个时间段的开始时间；(3)仅支持一级分类设置分时置顶
  string period = 6;
  // 二级分类信息
  repeated AppPoiCodeChildren children = 7;
}
// 二级分类信息
message AppPoiCodeChildren {
  // 二级分类id
  string code = 1;
  // 二级分类名称
  string name = 2;
  // 一级分类的排序
  int32 sequence = 3;
  // 是否开启置顶 1)字段取值范围：0-否，1-是；(2)仅支持一级分类设置分时置顶。
  int32 top_flag = 4;
  // 置顶周期(1)1,2,3,4,5,6,7分别表示周一至周日；(2)多个日期之间用英文逗号分隔；(3)仅支持一级分类设置分时置顶。
  string weeks_time = 5;
  // 置顶时段：(1)最多支持设置5个时间段，多个时间段之间用英文逗号分隔；(2)若设置多个时间段，则前一个时间段的结束时间不能晚于后一个时间段的开始时间；(3)仅支持一级分类设置分时置顶
  string period = 6;
  // 后级分类信息
  string children = 7;
}
// retailCat/list 查询门店商品分类列表  end

/// retail/getSpTagIds 获取美团后台商品类目（末级类目id） 20次/秒    start
message RetailGetSpTagIdsResult {
  // 返回的数据集
  repeated RetailGetSpTagIds data = 1;
  // 接口返回码
  int32 code = 2;
  // 返回的错误信息
  string error = 3;
  // 信息
  string message = 4;
}

message RetailGetSpTagIds {
  // id：末级类目id，用于商品同步接口【tag_id】字段
  int64 id = 1;
  // name：末级类目名称。
  string name = 2;
  // level：末级类目所在层级，即此类目的全部层级数量。
  int32 level = 3;
  // namePath：类目名称路径，从左至右顺序为第一级类目到末级类目到顺序。
  string namePath = 4;
}
/// retail/getSpTagIds 获取美团后台商品类目（末级类目id） 20次/秒    end

///category/attr/value/list 查询特殊属性的属性值列表  Start
//category/attr/value/list 查询特殊属性的属性值列表
message CategoryAttrValueListRequest {
  // 属性 id  取值范围仅支持两个：1200000088-品牌, 1200000094-产地。
  int64 attr_id = 1;
  //  属性值的关键词，如果这个关键字没有对应的数据，则返回空。
  string keyword = 2;
  // 页数，传1表示第1页 =1
  int32 page_num = 3;
  // 每页大小，最大支持200   =200
  int32 page_size = 4;
}
// category/attr/value/list 查询特殊属性的属性值列表
message DataValueList {
  // 品牌或产地Id
  string value_id = 1;
  // 品牌或产地值
  string value = 2;
}
// category/attr/value/list 查询特殊属性的属性值列表
message CategoryAttrValueListResult {
  // 返回品牌 或产地List集
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated DataValueList data = 4;
}
////  双支持
//// category/attr/value/list 查询特殊属性的属性值列表
//message CategoryAttrValueListResult  {
//  // 返回品牌 或产地List集
//  int32 Code=1;
//  string Message=2;
//  string Error=3;
//  // 产地集
//  repeated DataValueList PlaceOfOrigin=4;
//  // 品牌集
//  repeated DataValueList BrandData=5;
//}
///category/attr/value/list 查询特殊属性的属性值列表  end

/// category/attr/list 根据末级类目id获取类目属性列表 请求参数 start
message CategoryAttrListRequest {
  // 末级类目id，取自【retail/getSpTagIds 获取美团商品类目】接口的id字段，即末级类目id。如果这个末级类目id没有类目属性，则返回空。
  int64 tag_id = 1;
}
// 普通属性列表
message General_attrsList {
  //普通属性id
  string attr_id = 1;
  //适用节日
  //普通属性名称
  string attr_name = 2;
  //属性值类型，1-单选,2-多选,3-文本
  string attr_value_type = 3;
  //是否必传，1-必传,2-非必传
  string need = 4;
  //属性值可录入的字符类型。
  //参考值：1-中文；2-字母；3-数字；4-标点符号。
  //备注：1-标点符号不能单选，需与其他选项搭配使用；2-中文、字母、数字可单选使用；3-当单选中文、字母、数字类型不选标点符号时，均可录入“空格”。
  string character_type = 5;
  //属性值可录入的最大字符长度。
  //录入方式为“文本”时，字符长度不能超过该限制；录入方式为“单选”或“多选”时，本参数无参考意义。
  string text_max_length = 6;
  //该普通属性的顺序值
  int32 sequence = 7;
  //属性值的可扩展性（自定义），目前仅适用于属性值为单选或多选时有参考意义。返回值1表示可扩展，允许自定义属性值；且属性值文本长度最大不能超过8个字符。
  string support_extend = 8;
  // 值列表，注：【产地】、【品牌】两类属性的属性值过多，不在此处返回数据。需前往接口
  repeated DataValueList value_list = 9;
}
// 销售属性列表
message Sale_attrsList {
  // 销售属性id
  string attr_id = 1;
  // 销售属性名称
  string attr_name = 2;
  // 属性值类型，1-单选,2-多选,3-文本
  string attr_value_type = 3;
  // 是否必传，1-必传,2-非必传
  string need = 4;
  // 属性值可录入的字符类型。
  //参考值：1-中文；2-字母；3-数字；4-标点符号。
  //备注：1-标点符号不能单选，需与其他选项搭配使用；2-中文、字母、数字可单选使用；3-当单选中文、字母、数字类型不选标点符号时，均可录入“空格”。4-销售属性不支持“文本”录入方式
  string character_type = 5;
  // 销售属性本参数无意义。
  string text_max_length = 6;
  //该销售属性的顺序值
  int32 sequence = 7;
  //属性值的可扩展性（自定义）；1表示可扩展，允许自定义属性值；且属性值文本长度最大不能超过8个字符。
  string support_extend = 8;
  // 值列表
  repeated DataValueList value_list = 9;
}

message CategoryAttrList {
  // 普通属性列表
  repeated General_attrsList general_attrs = 4;
  // 销售属性列表
  repeated Sale_attrsList sale_attrs = 5;
}

message CategoryAttrListResult {
  /// 请求返回码
  int32 code = 1;
  // 请求信息
  string message = 2;
  // 请求错误信息
  string error = 3;
  // 普通属性列表
  CategoryAttrList data = 4;
}

/// category/attr/list 根据末级类目id获取类目属性列表 请求参数 end

/// retail/sku/delete 删除SKU信息 50次/秒  start
message RetailSkuDeleteRequest {
  // APP方门店id   必填
  string App_poi_code = 1;
  // APP方商品id   必填
  string App_food_code = 2;
  // 商品sku唯一标识码   必填
  string Sku_id = 3;
  // 当商品目前只有一个sku时，删除此sku即会彻底此商品；而如果此商品为其分类下的最后一个商品，通过本参数选择当商品被删除的同时是否删除此分类，取值范围：1-删除分类；2-保留分类。
  //如未传本参数，则默认为1，表示删除分类。  非必填
  int32 Is_delete_retail_cat = 4;
  // 店铺主体Id
  int32 store_master_id = 5;
}

/// retail/sku/delete 删除SKU信息 50次/秒   end

/// retail/sku/stock 批量更新SKU库存20次/秒,可传商品数据（app_food_code维度）限定不超过200组 start
message RetailSkuStockRequest {
  // APP方门店id
  string app_poi_code = 1;
  // 商品sku库存集合的json数据
  repeated FoodData food_data = 2;
  // 店铺主体Id
  int32 store_master_id = 3;

}
message FoodData {
  // app_food_code(必填项),APP方商品id
  string app_food_code = 1;
  // skus(必填项)，商品sku信息集合的json数组
  repeated Skus skus = 2;
}

message Skus {
  // sku_id(必填项)，是sku唯一标识码
  string sku_id = 1;
  // stock(必填项)，为sku的库存，传非负整数，若传"*"表示库存无限
  string stock = 2;
}

/// retail/sku/stock 批量更新SKU库存20次/秒,可传商品数据（app_food_code维度）限定不超过200组 end

/// order/preparationMealComplete 商家确认已完成拣货   start
message PreparationMealComplete {
  // 订单号
  int64 order_id = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}
/// 通用返回值
message ResponseResult {
  // 返回的Code
  int32 code = 1;
  // 返回的message
  string message = 2;
  // 返回的错误 信息
  string error = 3;
  // 返回的数据信息
  string data = 4;
}

/// order/preparationMealComplete 商家确认已完成拣货   end

/// task/status 查询多店同步任务的进程  请求参数   start
message TaskStatusRequest {
  // 任务id，多个任务编号用英文逗号分隔。每次调用最多可传50个任务id。
  string task_ids = 1;
  //1.阿闻自有,2.TP代运营
  int32 app_channel = 2;
}
/// task/status 查询多店同步任务的进程    响应参数
message TaskStatusResult {
  // 错误 码
  int32 code = 1;
  // 信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 任务id，多个任务编号用英文逗号分隔。每次调用最多可传50个任务id。
  string data = 4;
}

/// task/status 查询多店同步任务的进程  请求参数   end

/// order/getPartRefundFoods 查询可被部分退款的商品详情 start
message MtOrderRequest {
  int64 order_id = 1; // 订单编号
  // 店铺主体Id
  int32 store_master_id = 2;
}
// 查询可被部分退款的商品详情 响应参数
message OrderPartRefuFoodsResponse {
  // 错误 码
  int32 code = 1;
  // 信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 任务id，多个任务编号用英文逗号分隔。每次调用最多可传50个任务id。
  repeated PartRefuFoodsList data = 4;


}
message PartRefuFoodsList {
  // APP方商品id  即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 1;
  //商品名称
  string food_name = 2;
  // 商品sku唯一标识码，字段信息限定长度不超过40个字符。
  string sku_id = 3;
  // 商品sku现价(单价)，单位是元。此字段信息为当前订单中用户实际支付单件商品sku的价格。
  float food_price = 4;
  //  可退的商品sku的数量
  float count = 5;
  //   商品sku单件需使用打包盒的数量（商品维度，在创建/更新商品时维护的信息）
  float box_num = 6;
  // 商品sku的单个打包盒的价格，单位是元。
  float box_price = 7;
  // 商品sku原价(单价)，单位是元。此字段信息为当前订单中单件商品sku的原价。
  float origin_food_price = 8;
  // 可退款价格（单价），单位是元。
  float refund_price = 9;
  // 商品当前是否支持按克重退差价，参考值：1-是，0-否。
  int32 is_refund_difference = 10;


}
/// order/getPartRefundFoods 查询可被部分退款的商品详情 end

/// order/applyPartRefund 发起部分退款  start
message OrderApplyPartRefundRequset {
  // 订单号    必镇
  int64 order_id = 1;
  //  因***原因部分退款 必镇
  string reason = 2;
  // 退款类型，目前此字段适用于商家发起按克重退差价的流程，part_refund_type=3，即按差价退款。
  // 如不传或传其他值，则默认当前退款类型是按件进行部分退款。 非必镇
  int32 part_refund_type = 3;
  // 部分退款商品sku数据集合的json格式数组
  repeated ApplyPartRefundList food_data = 4;
  // 店铺主体Id
  int32 store_master_id = 5;
}

// 部分退款商品sku数据集合的json格式数组
message ApplyPartRefundList {
  //  APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 1;
  //   商品sku唯一标识码，如为单规格商品，没有维护规格sku_id，此字段可不传。
  string sku_id = 2;

  // 本次退款的商品数量：(1)当按件部分退款时，此字段必填，传入需退款商品sku的数量，为大于0的整数。(2)当part_refund_type=3时，即按克重退差价，则此字段非必填(如传了也不会生效)。
  float count = 3;
  //  商品sku的实拣重量，单位是克/g，支持1位小数。
  double actual_weight = 4;
}

/// order/applyPartRefund 发起部分退款 end

/// ecommerce/order/getOrderRefundDetail 获取订单退款记录 start
// 请求参数
message OrderRefundDetailRequest {
  // 订单号 必镇
  int64 wm_order_id_view = 1;
  // 退款类型1为退款,2为退货退款
  int32 refund_type = 2;
  // 店铺主体Id
  int32 store_master_id = 3;

}
message OrderRefundDetailResult {
  int32 code = 1;
  string message = 2;
  string error = 3;
  repeated OrderRefundDetail data = 4;
}

message OrderRefundDetail {
  //订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
  int64 wm_order_id_view = 1;

  // 订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。
  int64 order_id = 2;

  //   退款id，每次发起部分退款的退款id不同。
  int64 refund_id = 3;

  // 退款申请发起时间，为10位秒级的时间戳。
  int32 ctime = 4;

  // 退款申请处理时间，为10位秒级的时间戳。如为商家主动发起的退款，退款申请的发起和处理时间相同；如用户申请后商家还未处理，此字段信息与ctime相同。
  int32 utime = 5;

  // 退款类型：1-全额退款；2-部分退款；3-退差价。
  int32 refund_type = 6;

  //  商品库存不足，已于用户沟通退一件。
  //  商家处理退款时答复的内容
  string res_reason = 7;
  // 答复类型：0-未处理；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；
  //4-客服帮商家同意退款；5-超过3小时自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
  int32 res_type = 8;

  //   申请类型：0-订单取消自动确认退款； 1-用户申请退款； 2-客服帮用户申请退款； 3-重复提交而自动申请；
  //4-支付成功消息在订单取消之后到达而自动申请； 5-支付成功消息在订单被置为无效之后到达而自动申请； 6-用户被商家拒绝后申诉；7-商家申请退款。
  int32 apply_type = 9;

  //  商家联系我说没货了
  //  申请退款的原因
  string apply_reason = 10;
  //   退款金额合计，单位是元，此字段信息为本次退款商品的总金额。
  //如本次退款为全额退款，则此字段信息为订单原始在线实付金额减去已部分退款的金额后，当前剩余的订单总金额。
  double money = 11;

  // 退款图片url的json格式数组，用户在申请退款时上传的退款图片，多张以英文逗号隔开，上限为9张。
  string pictures = 12;
  // 部分退款的商品明细，适用于按件部分退和按克重退差价两种类型。
  repeated WmAppRetailForOrderPartRefundList wmAppRetailForOrderPartRefundList = 13;

  //退款服务类型, 区分是否已开通退货退款售后业务。
  //未开通的场景：
  //0-退款流程或申诉流程
  //已开通场景：
  //1-仅退款流程
  //2-退款退货流程
  int32 service_type = 14;

  // 推送当前售后单的状态类型，仅适用支持退货退款业务的商家：
  //  1-已申请
  //  10-初审已同意
  //  11-初审已驳回
  //  16-初审已申诉
  //  17-初审申诉已同意
  //  18-初审申诉已驳回
  //  20-终审已发起（用户已发货）
  //  21-终审已同意
  //  22-终审已驳回
  //  26-终审已申诉
  //  27-终审申诉已同意
  //  28-终审申诉已驳回
  //  30-已取消
  int32 status = 15;

  //   推送当前退款或退货退款流程的发起方，仅适用于支持退货退款的商家。
  int32 apply_op_user_type = 16;
  // 物流信息集合，仅适用支持退货退款业务的品类；在用户提交物流信息以及商家终审的推送消息中展示。
  Logistics_info logistics_info = 17;
  //订单数据状态标记。当订单中部分字段的数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
  //-1：有数据降级
  //0：无数据降级
  int32 incmp_code = 18;

  //  有降级的数据模块的集合，参考值：
  //  0：订单商品详情
  //  1：订单优惠信息
  //  2：商品优惠详情
  //  3：订单用户会员信息
  //  4：订单纬度的商家对账信息
  //  5：订单纬度的商家对账信息(元)
  //  6：订单收货人地址
  //  7：订单配送方式
  //  8：开放平台用户id
  //9：部分退款商品信息
  //10：退货退款物流信息
  //11：部分订单基本信息(包括订单优惠信息、订单商品详情、门店信息等)
  //12：sku信息
  //13：spu信息
  //14：商品信息(可能是sku或spu等商品相关信息获取时降级)
  repeated int32 incmp_modules = 19;
  RefundPartialEstimateCharge refund_partial_estimate_charge = 20;

}
message RefundPartialEstimateCharge {
  //本次退款商品的总金额，不含包装盒费，单商品总价=商品原价+赠品原价，位元
  string total_food_amount =1;
  //本次退款商品包装盒费总价，单位元
  string box_amount =2;
  //本次退款商家活动费用总支出成本，含赠品成本，单位元
  string activity_poi_amount =3;
  //本次退款美团活动补贴总金额，单位元
  string activity_meituan_amount =4;
  //本次退款代理商活动承担金额，单位元
  string activity_agent_amount =5;
  //本次退款平台服务费总金额，单位元
  string platform_charge_fee =6;
  //结算金额，单位元
  string settle_amount =7;
  //商家活动支出分摊到商品上的优惠总金额，单位元
  string productpreferences =8;
  //商家活动支出未分摊到商品上的总金额，单位元
  string not_productpreferences =9;

}
// 部分退款的商品明细，适用于按件部分退和按克重退差价两种类型。
message WmAppRetailForOrderPartRefundList {
  //     APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
  string app_food_code = 1;
  //  商品名称
  string food_name = 2;
  //  商品sku唯一标识码，字段信息限定长度不超过40个字符。
  string sku_id = 3;
  //  商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
  string upc = 4;
  // 商品sku的规格名称
  string spec = 5;
  // 本次退款的商品数量：(1)如为按件部分退款，此字段信息为本次退款商品sku的数量。(2)如为按克重退差价，此字段信息为0。
  int32 count = 6;
  //  商品sku单件需使用打包盒的数量。（商品维度，在创建/更新商品时维护的信息）
  double box_num = 7;
  //  商品sku的单个打包盒的价格，单位是元。
  double box_price = 8;
  // 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
  double food_price = 9;
  //  商品sku优惠前原价(单价)，单位是元。此字段信息为当前订单中单件商品sku的原价。
  double origin_food_price = 10;
  //  退款价格（单价），单位是元。此字段信息为当前订单中单件此商品sku的退款价格，是单价。(1)如购买多件商品sku，仅1件享优惠价，计算时商品优惠金额会进行等比分摊。
  // (2)如商品是按克重退差价，refund_price字段信息是计算优惠分摊后，单件商品sku重量差异部分的价格。
  double refund_price = 11;

  //  商品sku的已退重量，单位是克/g。此字段仅适用于退差价类型，为商品sku标价重量与实拣重量的差异重量。
  double refunded_weight = 12;
}
// 物流信息集合，仅适用支持退货退款业务的品类；在用户提交物流信息以及商家终审的推送消息中展示
message Logistics_info {
  //  物流公司名称
  string expressCompany = 1;
  //  物流订单号
  string expressNumber = 2;
  // 退货说明，用户寄回商品时填写的退货备注信息
  string reason = 3;
}

/// ecommerce/order/getOrderRefundDetail 获取订单退款记录 end

/// lei end

/// zhang start
//获取美团订单详情请求参数
message MtOrderDetailRequest {
  //订单号，商家可根据订单号查询订单当前的详细信息
  string order_id = 1;

  //是否为美团配送，当需要查询美团配送的详细信息时，此字段需上传且取值为1。
  string is_mt_logistics = 2;

  // 店铺主体Id
  int32 store_master_id = 3;
}

//获取美团订单详情响应参数
message MtOrderDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团订单详情
  MTOrderDetail MTOrderSDetail = 4;
}

message MTOrderDetail {
  //数据因内部交互异常或网络等原因延迟生成（超时），导致开发者当前获取的订单数据不完整，此时平台对订单数据缺失情况进行标记。如不完整，建议尝试重新查询。注意，平台仅对部分模块的数据完整性进行监察标记（参考incmp_modules字段）。参考值：
  //-1：有数据降级
  //0：无数据降级
  int32 incmp_code = 1;
  //订单状态，返回订单当前的状态。目前平台的订单状态参考值有：1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消。
  int32 status = 2;
  //订单号（同订单展示ID），数据库中请用bigint(20)存储此字段。
  int64 wm_order_id_view = 3;
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 4;
  //商家门店名称，即美团平台上当前订单所属门店的名称。
  string wm_poi_name = 5;
  //订单收货人地址，此字段为用户填写的收货地址。商家可在开发者中心->基础设置->订单订阅字段 页面订阅字段“13 收货地址”；若开启订阅，则订单数据会在此字段后追加根据经纬度反查地址的结果，并用“@#”符号分隔，如：用户填写地址@#反查结果。
  string recipient_address = 6;
  //订单收货人联系电话，此字段信息可能推送真实手机号码或隐私号，即需兼容13812345678和13812345678_123456两种号码格式。
  string recipient_phone = 7;
  //订单收货人姓名，同时包含用户在用户端选择的性别标识信息。(1)若用户没有填写姓名(老版本的用户端可能支持不填写)，此字段默认为空；商家可在开发者中心开启订阅“12 用户名称”字段，则平台会用“美团客人”自动填充此字段。(2)用户填写了收货人姓名时，此字段按用户所填全部内容正常展示，若姓名中包含特殊符号(如emoji表情)，需商家自行解析。
  string recipient_name = 8;
  //门店配送费，单位是元。当前订单产生时该门店的配送费（商家自配送运费或美团配送运费），此字段数据为运费优惠前的原价。
  float shipping_fee = 9;
  //订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
  float total = 10;
  //订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
  float original_price = 11;
  //发票抬头，为用户填写的开发票的抬头。
  string invoice_title = 12;
  //纳税人识别号，此字段信息默认不返回，如商家支持订单开发票，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“27 纳税人识别号”。
  string taxpayer_id = 13;
  //订单创建时间，为10位秒级的时间戳，此字段为用户提交订单的时间。
  int64 ctime = 14;
  //页面开启订阅字段“35 订单预计送达时间”。关于订单预计送达时间字段的说明： (1)当用户选择订单“立即送达”(delivery_time=0)，estimate_arrival_time字段信息则为美团计算的该即时订单预计送达时间。 (2)当用户选择订单在某个特定时间送达，即为预订单。estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的订单预计送达时间。 (3)当订单为用户到店自取方式，estimate_arrival_time字段与delivery_time字段信息相同，均为用户选择的到店取货时间。
  int64 estimate_arrival_time = 15;
  //订单收货地址的纬度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
  float latitude = 16;
  //订单收货地址的经度，美团使用的是高德坐标系，也就是火星坐标系，商家如果使用的是百度坐标系需要自行转换，坐标需要乘以一百万。
  float longitude = 17;
  //当日订单流水号，门店每日已支付订单的流水号从1开始。
  //目前，自提订单的取货码与该订单流水号相同。
  //此字段信息默认不返回，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“15 订单流水号”。
  int32 day_seq = 18;
  //订单商品总重量（该信息默认不返回，可在开发者中心订阅），单位为克/g。
  int64 total_weight = 19;
  //订单备注信息，是用户下单时填写和选择的备注信息；同时，当隐私号服务正常且用户开启号码保护时，此字段信息中会包含收货人脱敏真实号码和隐私号码，如“收餐人隐私号 18689114387_3473，手机号 185****2032”。
  //针对鲜花绿植品类的订单，本字段中支持展示预订人联系电话，请支持接收隐私号格式。
  string caution = 20;
  //支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2。
  int32 pay_type = 21;
  //取货类型：0-普通(配送),1-用户到店自取。此字段的信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“28 取餐类型订阅字段”。
  int32 pick_type = 22;
  //订单纬度的打包袋金额，单位是元。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“39 打包袋(元)”。
  //与package_bag_money字段相比，package_bag_money_yuan字段的单位为元，其他规则相同；订阅其中一个字段即可。
  string package_bag_money_yuan = 23;
  //订单商品详情，其值为由list序列化得到的json字符串
  string detail = 24;
  //订单优惠信息，其值为由list序列化得到的json字符串。
  string extras = 25;
  //商品优惠详情
  string sku_benefit_detail = 26;
  //订单纬度的商家对账信息，json格式数据。该信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“38 商家应收款详情(元)”。
  //与poi_receive_detail字段相比，poi_receive_detail_yuan字段里的金额类字段单位为元，其他规则相同；订阅其中一个字段即可。
  string poi_receive_detail_yuan = 27;
  //预计送达时间。若为“立即送达”订单则推送0；若是“预订单”则推送用户选择的预计送达时间，为10位秒级的时间戳。
  int64 delivery_time = 28;
  //订单配送方式，该字段信息默认不推送，商家如有需求可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“22 配送方式”。商家可在开放平台的【附录】文档中对照查看logistics_code的描述，如0000-商家自配、1001-美团加盟、2002-快送、3001-混合送（即美团专送+快送）等。 如商家想了解自己门店的配送方式以及如何区分等情况，请咨询美团品牌经理。
  string logistics_code = 29;
  int64 order_cancel_time = 30;  //        订单取消时间，为10位秒级的时间戳。
  int64 order_completed_time = 31;  //    订单完成时间，为10位秒级的时间戳
}

message MtDetail {
  //APP方商品id，即商家中台系统里商品的编码(spu_code值)：(1)不同门店之间商品id可以重复，同一门店内商品id不允许重复。(2)字段信息限定长度不超过128个字符。(3)如此字段信息推送的是商品名称或信息为空，则表示商家没有维护商品编码，请商家自行维护。
  string AppFoodCode = 1;
  //商品名称
  string FoodName = 2;
  //SKU码(商家的规格编码)，是商品sku唯一标识码。
  string SkuID = 3;
  //商品的UPC码信息，即商品包装上的UPC/EAN码编号，长度一般8位或者13位。是商家同步商品信息时维护的UPC码，同一门店内，商品UPC码不允许重复。
  string Upc = 4;
  //订单中此商品sku的购买数量
  int32 Quantity = 5;
  //商品单价，单位是元。此字段信息默认返回活动折扣后价格，商家如有需求将价格替换为原价，可在开发者中心->基础设置->订单订阅字段 页面开启订阅字段“18 替换菜品折扣价格为原价”。
  float Price = 6;
  //订单中当前商品sku需使用包装盒的总数量（单件商品sku需使用包装盒数量*商品售卖数量）。单件商品sku包装盒数量是商家同步商品时维护的信息。
  float BoxNum = 7;
  //包装盒单价，单位是元。为订单中当前商品sku单个包装盒的价格，是商家同步商品时维护的信息。
  float BoxPrice = 8;
  //商品售卖单位
  string Unit = 9;
  //商品折扣系数，目前此字段默认为1，商家无需参考此字段的信息。
  float FoodDiscount = 10;
  //商品sku单件的重量，单位是克/g。
  int32 Weight = 11;
  //表示商品sku单件的重量数值信息，最多支持两位小数。
  string WeightForUnit = 12;
  //表示sku的重量数值单位。
  string WeightUnit = 13;
}

//商家确认美团订单请求参数
message MtOrderConfirmRequest {
  //订单号
  string order_id = 1;

  //0 为空，1新瑞鹏，2tp代运营
  int32 app_channel = 2;

}

//商家确认美团订单响应参数
message MtOrderConfirmResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团响应数据
  string data = 4;
}

//商家取消美团订单请求参数
message MtOrderCancelRequest {
  //订单号
  string order_id = 1;
  //商家消订单的原因
  string reason = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

//商家取消美团订单响应参数
message MtOrderCancelResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团响应数据
  string data = 4;
}

//批量获取门店详细请求参数
message MtStoreyListRequest {
  //APP方门店id，传商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  //支持传多个门店id批量查询，一次调用可上传200个门店id，多个之间以英文逗号分隔；支持部分门店查询成功。
  //仅支持返回门店id正确的门店信息。
  string app_poi_codes = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

//获取美团门店的所有品类
message MtStoreyListRequestResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  repeated StoreList storeList = 4;
}

message StoreList {
  //门店上下线状态，参考值：0-下线；1-上线；2-上单中；3-审核通过可上线。
  int32 is_online = 1;
  //门店最近一次更新时间，返回10位秒级的时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp）
  int32 utime = 2;
  //门店客服电话号码
  string phone = 3;
  //门店图片地址
  string pic_url = 4;
  //门店的营业状态，参考值：1-可配送；3-休息中。
  int32 open_level = 5;
  //门店名称
  string name = 6;
  //商家接受预订日期的最早日期，范围：0-7。最早日期是指用户最少需要提前下单的天数，如果不选“当天”（传0），则用户不能下要求今日送达的订单。例如：最早日期为“隔天”（传1），则用户今日最快只能下明天备货配送的订单。
  int32 pre_book_min_days = 7;
  //门店纬度，美团使用的是高德坐标系。
  //注：如需使用获取的纬度信息在poi/save接口中上传，需用此信息再除以一百万。例如本接口查询到的纬度值为2.9774566E7，那么在poi/save接口上传时，需转为29.774566
  float longitude = 8;
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 9;
  //门店公告信息
  string promotion_info = 10;
  //每个订单的配送费，单位是元。
  float shipping_fee = 11;
  //门店创建时间，返回10位秒级时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp）
  int32 ctime = 12;
  //是否支持营业时间范围外预下单，参考值：1-支持；0-不支持。
  int32 pre_book = 13;
  //门店是否支持发票，参考值：1-支持；0-不支持。
  int32 invoice_support = 14;
  //门店营业时间，多个时间段之间以英文逗号分隔。
  string shipping_time = 15;
  //门店地址
  string address = 16;
  //商家接受预订日期的最长日期，取值范围：0-7。最长日期是指用户可要求送达的最多天数。例如：最长日期为“隔天”（传1），则用户今日可下要求明天配送的订单，不可下要求后天配送的订单。
  int32 pre_book_max_days = 17;
  //门店支持开发票的最小订单价，单位是元。
  int32 invoice_min_price = 18;
  //是否支持营业时间范围内预下单，参考值：1-支持；0-不支持。
  int32 timeSelect = 19;
  //门店纬度，美团使用的是高德坐标系。
  //注：如需使用获取的纬度信息在poi/save接口中上传，需用此信息再除以一百万。例如本接口查询到的纬度值为2.9774566E7，那么在poi/save接口上传时，需转为29.774566。
  float latitude = 20;
  //发票相关说明
  string invoice_description = 21;
  //门店经营品类，多个品类信息以英文逗号分隔。
  string third_tag_name = 22;

  //门店的配送方式,参考值： 1003-美团跑腿（众包） 1001-专送（加盟） 1002-专送（自建） 1004-城市代理 2002-快送 2010-全城送 0000-商家自配 3001-混合送（专送+快送) 30011002-混合自建 30011001-混合加盟 30012002-混合快送 0002-趣生活美食配送 0016-达达快递 0033-E_代送
  string logistics_codes = 23;


}

message DeliveryListReq {
  string app_poi_code = 1;
}

message WeightMarkupFactor {
  float markupNum = 1;
  float weight = 2;
  float step = 3;
}

message TimeMarkupFactor {
  float markupNum = 1;
  string timeRange = 2;
}

message DistanceMarkupFactor {
  float distance = 1;
  float markupNum = 2;
}


message MtDelivery {
  string area = 1;
  string logistics_code = 2;
  int32 type = 3;
  string shipping_period_name = 4;
  string groupKey = 5;
  double shipping_fee = 6;
  int32 valid = 7;
  int64 mt_shipping_id = 8;
  double min_price = 9;
  string time_range = 10;
  string app_shipping_code = 11;
  string logistics_type = 12;
  repeated WeightMarkupFactor weight_markup_factors = 13;
  repeated TimeMarkupFactor time_markup_factors = 14;
  repeated DistanceMarkupFactor distance_markup_factors = 15;

}

message MtDeliveryListRes {
  repeated MtDelivery success_list = 1;
  int32 result_code = 2;
}

message MtDeliveryListResponse {
  repeated MtDelivery success_list = 1;
  //状态码
  int32 code = 2;
  //消息
  string message = 3;
}

message MtDeliveryUpdateReq {
  string area = 1;
  string logistics_code = 2;
  int32 type = 3;
  string shipping_period_name = 4;
  string group_key = 5;
  double shipping_fee = 6;
  int32 valid = 7;
  int64 mt_shipping_id = 8;
  double min_price = 9;
  string time_range = 10;
  string app_shipping_code = 11;
  string logistics_type = 12;
  repeated WeightMarkupFactor weight_markup_factors = 13;
  repeated TimeMarkupFactor time_markup_factors = 14;
  repeated DistanceMarkupFactor distance_markup_factors = 15;
  string app_poi_code = 16;
  int32 distance_markup_execute_type = 17;
  int32 weight_markup_execute_type = 18;
  int32 time_markup_execute_type = 19;
  //是否特殊时段  0否  1是
  int32 is_spec = 20;
}



message StoreList1 {
  //门店上下线状态，参考值：0-下线；1-上线；2-上单中；3-审核通过可上线。
  int32 is_online = 1;
  //门店最近一次更新时间，返回10位秒级的时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp）
  int32 utime = 2;
  //门店客服电话号码
  string phone = 3;
  //门店图片地址
  string pic_url = 4;
  //门店的营业状态，参考值：1-可配送；3-休息中。
  int32 open_level = 5;
  //门店名称
  string name = 6;
  //商家接受预订日期的最早日期，范围：0-7。最早日期是指用户最少需要提前下单的天数，如果不选“当天”（传0），则用户不能下要求今日送达的订单。例如：最早日期为“隔天”（传1），则用户今日最快只能下明天备货配送的订单。
  int32 pre_book_min_days = 7;
  //门店纬度，美团使用的是高德坐标系。
  //注：如需使用获取的纬度信息在poi/save接口中上传，需用此信息再除以一百万。例如本接口查询到的纬度值为2.9774566E7，那么在poi/save接口上传时，需转为29.774566
  float longitude = 8;
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 9;
  //门店公告信息
  string promotion_info = 10;
  //门店创建时间，返回10位秒级时间戳。（当前距离Epoch（1970年1月1日） 以秒计算的时间，即unix-timestamp）
  int32 ctime = 12;
  //是否支持营业时间范围外预下单，参考值：1-支持；0-不支持。
  int32 pre_book = 13;
  //门店是否支持发票，参考值：1-支持；0-不支持。
  int32 invoice_support = 14;
  //门店营业时间，多个时间段之间以英文逗号分隔。
  string shipping_time = 15;
  //门店地址
  string address = 16;
  //商家接受预订日期的最长日期，取值范围：0-7。最长日期是指用户可要求送达的最多天数。例如：最长日期为“隔天”（传1），则用户今日可下要求明天配送的订单，不可下要求后天配送的订单。
  int32 pre_book_max_days = 17;
  //门店支持开发票的最小订单价，单位是元。
  int32 invoice_min_price = 18;
  //是否支持营业时间范围内预下单，参考值：1-支持；0-不支持。
  int32 timeSelect = 19;
  //门店纬度，美团使用的是高德坐标系。
  //注：如需使用获取的纬度信息在poi/save接口中上传，需用此信息再除以一百万。例如本接口查询到的纬度值为2.9774566E7，那么在poi/save接口上传时，需转为29.774566。
  float latitude = 20;
  //发票相关说明
  string invoice_description = 21;
  //门店经营品类，多个品类信息以英文逗号分隔。
  string third_tag_name = 22;

  //门店的配送方式,参考值： 1003-美团跑腿（众包） 1001-专送（加盟） 1002-专送（自建） 1004-城市代理 2002-快送 2010-全城送 0000-商家自配 3001-混合送（专送+快送) 30011002-混合自建 30011001-混合加盟 30012002-混合快送 0002-趣生活美食配送 0016-达达快递 0033-E_代送
  string logistics_codes = 23;


}

/// zhang end


message MpShopCreateRequest {
  //取货门店id，即合作方向美团提供的门店id
  string shop_id = 1;
  //门店名称 说明：门店名称格式请按照 【XX品牌-XX店】填写，例：百果园-望京店，注：该名称需与实体门店门牌保持一致，保证骑手取货可确认门店。
  string shop_name = 2;
  //一级品类，见附件品类代码表
  int32 category = 3;
  //二级品类，见附件品类代码表
  int32 second_category = 4;
  //门店联系人姓名
  string contact_name = 5;
  //联系电话
  string contact_phone = 6;
  //门店地址
  string shop_address = 7;
  //门牌号
  string shop_address_detail = 8;
  //门店经度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419
  //说明：请提供准确坐标，便于骑手取货
  int64 shop_lng = 9;
  //门店纬度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 39985005
  //说明：请提供准确坐标，便于骑手取货
  int64 shop_lat = 10;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int32 coordinate_type = 11;
  //配送服务代码，详情见合同
  //飞速达:4002
  //快速达:4011
  //及时达:4012
  //集中送:4013
  //例如：4011,4012(多个英文逗号隔开)
  string delivery_service_codes = 12;
  //营业时间
  //例：[{"beginTime":"00:00","endTime":"24:00"}]
  //注：传入后美团根据区域可配送时间取交集时间作为门店配送时间
  string business_hours = 13;

  string contact_email = 14;

  string app_channel = 15;
}

message MpShopQueryRequest {
  //取货门店id，即合作方向美团提供的门店id
  string shop_id = 1;
  string app_channel=2;
}

message MpShopQueryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  MpShopQueryData data = 4;
  //美配错误码
  string external_code = 5;
}
message MpShopQueryData {
  //取货门店id，即合作方向美团提供的门店id
  string shop_id = 1;
  //门店名称 说明：门店名称格式请按照 【XX品牌-XX店】填写，例：百果园-望京店，注：该名称需与实体门店门牌保持一致，保证骑手取货可确认门店。
  int32 city = 2;
  //一级品类，见附件品类代码表
  int32 category = 3;
  //二级品类，见附件品类代码表
  int32 second_category = 4;
  //门店联系人姓名
  string contact_name = 5;
  //联系电话
  string contact_phone = 6;

  //门店地址
  string shop_address = 7;
  //门牌号
  string shop_address_detail = 8;
  //门店经度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419
  //说明：请提供准确坐标，便于骑手取货
  int64 shop_lng = 9;
  //门店纬度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 39985005
  //说明：请提供准确坐标，便于骑手取货
  int64 shop_lat = 10;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int32 coordinate_type = 11;
  //配送服务代码，详情见合同
  //飞速达:4002
  //快速达:4011
  //及时达:4012
  //集中送:4013
  //例如：4011,4012(多个英文逗号隔开)
  string delivery_service_codes = 12;
  //营业时间
  //例：[{"beginTime":"00:00","endTime":"24:00"}]
  //注：传入后美团根据区域可配送时间取交集时间作为门店配送时间
  string business_hours = 13;
  //是否支持预约单，0：不支持，1：支持
  int32 prebook = 14;
  //是否支持营业时间外预约单，0：不支持，1：支持
  int32 prebook_out_of_biz_time = 15;
  //预约单时间段，格式为{"start": "0", "end": "2"}，单位为天
  string prebook_period = 16;
}

message MpShopAreaQueryRequest {
  //配送服务代码
  int32 delivery_service_code = 1;
  //取货门店id
  string shop_id = 2;
  string app_channel = 3;
}


message MpOrderCreateRequest {

  //即配送活动标识，由外部系统生成，不同order_id应对应不同的delivery_id，若因美团系统故障导致发单接口失败，可利用相同的delivery_id重新发单，系统视为同一次配送活动，若更换delivery_id，则系统视为两次独立配送活动。
  int64 delivery_id = 1;
  //订单id，即该订单在合作方系统中的id，最长不超过32个字符
  //注：目前若某一订单正在配送中（状态不为取消），再次发送同一订单（order_id相同）将返回同一mt_peisong_id
  string order_id = 2;
  //取货门店id，即合作方向美团提供的门店id
  //注：测试门店的shop_id固定为 test_0001 ，仅用于对接时联调测试。
  string shop_id = 3;
  //配送服务代码，详情见合同
  //飞速达:4002
  //快速达:4011
  //及时达:4012
  //集中送:4013
  int32 delivery_service_code = 4;
  //收件人名称，最长不超过256个字符
  string receiver_name = 5;
  //收件人地址，最长不超过512个字符
  string receiver_address = 6;
  //收件人电话，最长不超过64个字符
  string receiver_phone = 7;
  //收件人经度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419
  int64 receiver_lng = 8;
  //收件人纬度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 39985005
  int64 receiver_lat = 9;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int32 coordinate_type = 10;
  //货物价格，单位为元，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-5000
  float goods_value = 11;
  //货物高度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-45
  float goods_height = 12;
  //货物宽度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
  float goods_width = 13;
  //货物长度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-65
  float goods_length = 14;
  //货物重量，单位为kg，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
  float goods_weight = 15;
  //货物详情，最长不超过10240个字符，内容为JSON格式，示例如下：
  //{
  //"goods":[
  //    {
  //      "goodCount": 1,
  //      "goodName": "货品名称",
  //      "goodPrice": 9.99,
  //      "goodUnit": "个"
  //    }
  //  ]
  //}
  //goods对应货物列表；
  //goodCount表示货物数量，int类型，必填且必须大于0；
  //goodName表示货品名称，String类型，必填且不能为空；
  //goodPrice表示货品单价，double类型，选填，数值不小于0，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数）；
  //goodUnit表示货品单位，String类型，选填，最长不超过20个字符。
  //强烈建议提供，方便骑手在取货时确认货品信息
  string goods_detail = 16;
  //货物取货信息，用于骑手到店取货，最长不超过100个字符
  string goods_pickup_info = 17;
  //货物交付信息，最长不超过100个字符
  string goods_delivery_info = 18;
  //期望取货时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp。
  int64 expected_pickup_time = 19;
  //期望送达时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp
  //即时单：以发单时间 + 服务包时效作为期望送达时间（当天送服务包需客户指定期望送达时间）
  //预约单：以客户传参数据为准（预约时间必须大于当前下单时间+服务包时效+3分钟）
  int64 expected_delivery_time = 20;
  //订单类型，默认为0
  //0: 即时单(尽快送达，限当日订单)
  //1: 预约单
  int32 order_type = 21;
  //门店订单流水号，建议提供，方便骑手门店取货，最长不超过32个字符
  string poi_seq = 22;
  //备注，最长不超过200个字符。
  string note = 23;
  //骑手应付金额，单位为元，精确到分【预留字段】
  float cash_on_delivery = 24;
  float cash_on_pickup = 25;
  string invoice_title = 26;
  string outer_order_source_no = 27;
  //标示该订单最初是来自哪一平台
  //
  //【校验规则】
  //
  //长度小于等于20个字符
  //
  //【可选值】
  //
  //101：美团（外卖&闪购）
  //
  //102：饿了么
  //
  //103：京东到家
  //
  //104：美团到餐
  //
  //105：抖音
  //
  //201：商家web网站
  //
  //202：商家小程序-微信
  //
  //203：商家小程序-支付宝
  //
  //204：商家APP
  //
  //205：商家热线
  //
  //（其他来源请直接填写来源的中文名称）
  string outer_order_source_desc=28;
  string app_channel = 29;
}

message MpOrderDeleteRequest {
  //配送活动标识
  int64 delivery_id = 1;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 2;
  //取消原因类别，默认为接入方原因
  string cancel_reason_id = 3;
  //详细取消原因，最长不超过256个字符
  string cancel_reason = 4;
  //配送服务代码，详情见合同
  //飞速达: 4002
  //快速达: 4011
  //及时达: 4012
  //集中送: 4013
  //自由达: 4014
  int32 delivery_service_code = 5;
  string app_channel = 6;
}

message MpOrderStatusQueryRequest {
  //配送活动标识
  int64 delivery_id = 1;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 2;
  string app_channel = 3;
}

message MpOrderStatusQueryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  MpOrderStatusQueryData data = 4;
  //美配错误码
  string external_code = 5;
}
message MpOrderStatusQueryData {
  //配送活动标识
  string delivery_id = 1;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 2;
  //外部订单号，最长不超过32个字符
  string order_id = 3;
  //状态代码，可选值为
  //0：待调度
  //20：已接单
  //30：已取货
  //50：已送达
  //99：已取消
  int32 status = 4;
  //订单状态变更时间，格式为unix-timestamp
  int64 operate_time = 5;
  //配送员姓名（订单已被骑手接单后会返回骑手信息）
  string courier_name = 6;
  //配送员电话（订单已被骑手接单后会返回骑手信息）
  string courier_phone = 7;
  //取消原因id，详情请参考
  int32 cancel_reason_id = 8;
  //取消原因详情，最长不超过256个字符
  string cancel_reason = 9;
  //预计送达时间(注：只有“自由达”服务的订单查询有此字段)
  //即时单：只有骑手接单后，才会确定预计送达时间，因此状态为“已接单”、“已取货”、“已送达”时，此字段为非 0 值，其它状态下此值为 0；
  //预约单：下单成功即可确定预计送达时间，并且预计送达时间就是用户下单时传入的期望送达时间；注：格式为 unix-timestamp，若预计送达时间还未确定时，字段的值默认为 0；
  int32 predict_delivery_time = 10;
}

message MpOrderCheckRequest {
  //取货门店 id，即合作方向美团提供的门店 id。
  //注：测试门店的 shop_id 固定为 test_0001，仅用于对接时联调测试。
  string shop_id = 1;
  //配送服务代码，详情见合同
  //飞速达: 4002
  //快速达: 4011
  //及时达: 4012
  //集中送: 4013
  string delivery_service_code = 2;
  //收件人地址，最长不超过 512 个字符
  string receiver_address = 3;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int64 receiver_lng = 4;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int64 receiver_lat = 5;
  //配送员姓名（订单已被骑手接单后会返回骑手信息）
  int32 coordinate_type = 6;
  //预留字段，方便以后扩充校验规则，check_type = 1
  int32 check_type = 7;
  //模拟发单时间，时区为 GMT+8，当前距离 Epoch（1970年1月1日) 以秒计算的时间，即 unix-timestamp。
  int64 mock_order_time = 8;
  string app_channel = 9;
}

message MpOrderRiderLocationRequest {
  //配送活动标识
  int64 delivery_id = 1;
  //美团配送内部订单id，最长不超过32个字符
  string mt_peisong_id = 2;
  //配送服务代码，详情见合同
  //飞速达: 4002
  //快速达: 4011
  //及时达: 4012
  //集中送: 4013
  //自由达: 4014
  int32 delivery_service_code = 3;
  string app_channel = 4;
}


message MtOrderLogisticsSyncRequest {
  //订单号
  string order_id = 1;
  //配送员姓名，此字段信息会同步展示在用户端订单详情的“配送信息”中。
  string courier_name = 2;
  //配送员手机号码，此字段信息会同步展示在用户端订单详情的“配送信息”中。
  string courier_phone = 3;
  //配送此订单商品的物流平台，此字段可以不传；如上传则系统会校验此字段信息的真实性 。参考取值： 10001-顺丰, 10002-达达, 10003-闪送, 10004-蜂鸟, 10008-点我达, 10010-生活半径, 10032-美团跑腿, 10017-其他。
  string logistics_provider_code = 4;
  //配送状态code，取值范围：0-配送单发往配送；1-已创建配送包裹；5-已分配骑手；10-骑手已接单；15-骑手已到店；20-骑手已取餐；40-骑手已送达；100-配送单已取消。
  //注意：商家如未上传此信息，则平台默认值为20。
  int32 logistics_status = 5;
  //骑手当前的纬度，美团使用的是高德坐标系。
  string latitude = 6;
  //骑手当前的经度，美团使用的是高德坐标系。
  string longitude = 7;
  // 店铺主体Id
  int32 store_master_id = 8;
  //第三方配送商物流单号
  string third_carrier_order_id = 9;
}


message ExternalResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  string data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;
}

message MtOrderDeliveringRequest {
  //订单号，门店配送方式仅为自配送的商家在接单后，如商品已开始配送，可调用此接口同步此订单配送状态“商品配送中”至用户端。
  string order_id = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

message MtOrderArrivedRequest {
  //订单号，门店配送方式仅为自配送的商家在接单后，如商品已完成配送，可调用此接口同步此订单配送状态“商品已送达”至用户端。
  string order_id = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

//拉取用户真实手机号（必接）
message MtUserInfoPhoneNumberRequest {
  //门店ID
  string app_poi_code = 1;
  //分页查询的偏移量
  int32 offset = 2;
  //每条页数，需小于等于1000
  int32 limit = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}
//拉取用户真实手机号响应参数
message MtUserInfoPhoneNumberResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团响应数据
  repeated MTUserInfoPhoneNumber MTUserInfoPhoneNumberList = 4;
}
//用户真实手机号
message MTUserInfoPhoneNumber {
  //订单号
  string order_id = 1;
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 2;
  //订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段。
  string wm_order_id_view = 3;
  //订单的流水号，门店内每日已支付订单的流水号从1开始。
  int32 day_seq = 4;
  //订单收货人的真实手机号码
  string rider_name = 5;
  //订单预订人的真实手机号码，如无则返回空。
  string rider_real_phone_number = 6;

}

//拉取骑手真实手机号请求参数（必接）
message MtRiderInfoPhoneNumberRequest {
  //状态码
  string app_poi_code = 1;
  //消息
  int32 offset = 2;
  //错误信息
  int32 limit = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}
//拉取骑手真实手机号响应参数
message MtRiderInfoPhoneNumberResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //美团响应数据
  repeated MTRiderInfoPhoneNumber MTRiderInfoPhoneNumberList = 4;
}
//骑手真实手机号
message MTRiderInfoPhoneNumber {
  //订单号
  string order_id = 1;
  //订单展示ID
  string wm_order_id_view = 2;
  //骑手名字
  string rider_name = 3;
  //骑手真实手机号
  string rider_real_phone_number = 4;
  //门店ID
  string app_poi_code = 5;
}

message MtOrderStatusRequest {
  //订单号，根据订单号查询订单当前的状态。
  string order_id = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}

message MtOrderRefundRequest {
  //订单号，当商家收到用户的退款申请，如同意/拒绝退款，商家可调用此接口操作确认此订单的退款申请。
  string order_id = 1;
  //确认/拒绝退款的原因
  string reason = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

message MtReviewAfterSalesRequest{
  //订单展示ID
  int64 wm_order_id_view = 1;
  //审核当前售后单的结果类型，取值范围：0-同意退货；1-同意退款；2-驳回退货/退款。
  int64 review_type = 2;
  //退货退款”驳回退货的原因,审核当前售后单的结果原因，当review_type为2时，此字段必填。
  int32 reject_reason_code = 3;
  //驳回原因说明, 驳回原因编码为-1时，必须传此参数，其他情况忽略
  string reject_other_reason = 4;
}

message MtBatchFetchAbnormalOrderRequest {
  //开始时间点，传10位秒级的时间戳。时间不得早于当前时间7天前，即仅支持拉取近7日内的异常订单，
  int32 start_time = 1;
  //结束时间点，传10位秒级的时间戳，不得晚于当前时间。
  int32 end_time = 2;
  //异常订单类型：1- 推单失败；2-超时取消。
  int32 type = 3;
  //分页拉取的偏移量，表示从第几条数据开始拉取，0表示第一条。
  int32 offset = 4;
  //分页每页拉取的数量，最大不超过200。
  int32 limit = 5;
}
message MtApplyCompensationRequest {
  //订单号。
  string order_id = 1;
  //商家申请赔付的原因
  string reason = 2;
  //申请状态类型：110-第一次申请；120-第二次申诉。
  int32 apply_status = 3;
  //申请赔付金额，单位是元。
  float amount = 4;
  // 店铺主体Id
  int32 store_master_id = 5;
}

message MtGetSupportedCompensationRequest {
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 1;
  //分页查询的偏移量，表示本次查询从第几条数据开始查，0是第一条。
  int32 offset = 2;
  //分页查询每页展示数量，最大不能超过200。
  int32 limit = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
}


message MtGetCompensationResultRequest {
  //订单号
  string order_id = 1;
  // 店铺主体Id
  int32 store_master_id = 2;
}
message MtGetCompensationResultResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  MtGetCompensationResultData data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;

}

message MtGetCompensationResultData {
  //订单号，数据库中请用bigint(20)存储此字段。
  string order_id = 1;
  //订单当前的赔付申请状态：0-不可发赔付申请；1-可申请；2-超过时限；3-赔付申请；4-配送同意赔付；5-配送拒绝赔付；6-申述超时限；7-赔付申述；8-申述通过；9-申述拒绝；10-取消赔付申请。
  int32 status = 2;
  //赔付金额，单位为元。
  float amount = 3;
}

message MtOrderBatchCompensationRequest {
  //订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。多个订单号以英文逗号隔开，最多支持传20个。
  string orderViewIds = 1;
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

message MtOrderBatchCompensationResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  repeated MtOrderBatchCompensationData data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;
}
message MtOrderBatchCompensationData {
  //订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。
  int64 orderViewId = 1;
  //商家责任的订单赔付金额，单位是元。
  float amount = 2;
  //赔付原因
  string reason = 3;
  //赔付时间，为10位秒级的时间戳。
  int64 executeTime = 4;
  //赔付的图片凭证，多个图片url以英文逗号隔开。
  string pictures = 5;
}
message MtRetailCatDeleteRequest {
  //APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
  string app_poi_code = 1;
  //商品分类id，如分类下存在子级分类或商品，则不允许直接删除此分类。
  string category_code = 2;
  //商品分类名称：(1)限定长度不超过8个字符。(2)如分类下存在子级分类或商品，则不允许直接删除此分类。
  string category_name = 3;
  // 店铺主体Id
  int32 store_master_id = 4;
  //是否转移商品
  int32 move_product_to_uncate = 5;
}
message MpNotShopOrderCreateRequest {
  //即配送活动标识，由外部系统生成，不同order_id应对应不同的delivery_id，若因美团系统故障导致发单接口失败，可利用相同的delivery_id重新发单，系统视为同一次配送活动，若更换delivery_id，则系统视为两次独立配送活动。
  int64 delivery_id = 1;
  //订单id，即该订单在合作方系统中的id，最长不超过32个字符
  //注：目前若某一订单正在配送中（状态不为取消），再次发送同一订单（order_id相同）将返回同一mt_peisong_id
  string order_id = 2;
  //配送服务代码，详情见合同
  //自由达:4014
  int32 delivery_service_code = 3;
  //收件人名称，最长不超过256个字符
  string receiver_name = 4;
  //收件人地址，最长不超过512个字符
  string receiver_address = 5;
  //收件人电话，最长不超过64个字符
  string receiver_phone = 6;
  //收件人经度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 116398419
  int64 receiver_lng = 7;
  //收件人纬度（火星坐标或百度坐标，和 coordinate_type 字段配合使用），坐标 * （10的六次方），如 39985005
  int64 receiver_lat = 8;
  //发件商户名称（骑手取货时，APP 看到的发件名称）
  string sender_shop_name = 9;
  //发件人名称，最长不超过 256 个字符
  string sender_name = 10;
  //发件地址，最长不超过 512 个字符
  string sender_address = 11;
  //发件人电话,最长不超过 64 个字符
  string sender_phone = 12;
  //发件经度（火星坐标或百度坐标，和 coordinate_type字段配合使用），坐标 * （10 的六次方），如 116398419
  int64 sender_lng = 13;
  //发件纬度（火星坐标或百度坐标，和 coordinate_type字段配合使用），坐标 * （10 的六次方），如 39985005
  int64 sender_lat = 14;
  //坐标类型，0：火星坐标（高德，腾讯地图均采用火星坐标） 1：百度坐标 （默认值为0）
  int32 coordinate_type = 15;
  //货物价格，单位为元，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-5000
  float goods_value = 16;
  //货物高度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-45
  float goods_height = 17;
  //货物宽度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
  float goods_width = 18;
  //货物长度，单位为cm，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-65
  float goods_length = 19;
  //货物重量，单位为kg，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数），范围为0-50
  float goods_weight = 20;
  //货物详情，最长不超过10240个字符，内容为JSON格式，示例如下：
  //{
  //"goods":[
  //    {
  //      "goodCount": 1,
  //      "goodName": "货品名称",
  //      "goodPrice": 9.99,
  //      "goodUnit": "个"
  //    }
  //  ]
  //}
  //goods对应货物列表；
  //goodCount表示货物数量，int类型，必填且必须大于0；
  //goodName表示货品名称，String类型，必填且不能为空；
  //goodPrice表示货品单价，double类型，选填，数值不小于0，精确到小数点后两位（如果小数点后位数多于两位，则四舍五入保留两位小数）；
  //goodUnit表示货品单位，String类型，选填，最长不超过20个字符。
  //强烈建议提供，方便骑手在取货时确认货品信息
  string goods_detail = 21;
  //货物取货信息，用于骑手到店取货，最长不超过100个字符
  string goods_pickup_info = 22;
  //货物交付信息，最长不超过100个字符
  string goods_delivery_info = 23;
  //期望取货时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp。
  int64 expected_pickup_time = 24;
  //期望送达时间，时区为GMT+8，当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix-timestamp
  //即时单：根据订单的实际配送距离确定不同的配送时效，美团内部根据配送时效设置期望送达时间
  //预约单：以客户传参数据为准
  int64 expected_delivery_time = 25;
  //订单类型，默认为0
  //0: 即时单(尽快送达，限当日订单)
  //1: 预约单
  int32 order_type = 26;
  //门店订单流水号，建议提供，方便骑手门店取货，最长不超过32个字符
  string poi_seq = 27;
  //备注，最长不超过200个字符。
  string note = 28;
  //骑手应付金额，单位为元，精确到分【预留字段】
  float cash_on_delivery = 29;
  //骑手应收金额，单位为元，精确到分【预留字段】
  float cash_on_pickup = 30;
  //发票抬头，最长不超过 256 个字符【预留字段】
  string invoice_title = 31;
  //订单来源：
  //101.美团（外卖&闪购）
  //102.饿了么
  //103.京东到家
  //201.商家web网站
  //202.商家小程序-微信
  //203.商家小程序-支付宝
  //204.商家APP
  //205.商家热线
  //其他，请直接填写中文字符串，最长不超过20个
  //字符
  //非「其他」需传代码
  string outer_order_source_desc = 32;
  //原平台订单号，如订单来源为美团，该字段必填，
  //且为美团平台生成的订单号，最长不超过32个字符
  string outer_order_source_no = 33;
}

//饿了么开始
//获取饿么了门店品类列表
message ElmCategoryListResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //品类集合
  ElmStoreCategoryList data = 4;
}

message ElmStoreCategoryList {
  //品类ID
  string cat_id = 1;
  //品类名称
  string cat_name = 2;
  //品类层级
  int32 depth = 3;
  //父级品类ID
  int32 parent_id = 4;
  repeated ElmStoreCategoryList child = 5;
}

//批量获取门店详细请求参数
message ElmStoreInfoRequest {
  //平台门店ID
  string baidu_shop_id = 1;
  //门店ID
  string shop_id = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;

}

message ElmStoreInfoResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //品类集合
  repeated ElmStoreInfo data = 4;
}

//批量同步门店营业状态
message SyncStoreStatusRequest {
  message reqData {
    //平台门店ID
    string baidu_shop_id = 1;
    //门店ID
    string shop_id = 2;
    // 状态，0-暂停营业，1-营业
    int32 status = 3;
    // app渠道 1.阿闻自有,2.TP代运营
    int32 app_channel = 4;
  }
  repeated reqData data = 1;
  // shop_status_task表id
  int64 task_id = 2;
}
message SyncStoreStatusResponse {
  message outData {
    //门店ID
    string shop_id = 1;
    // 状态,1-成功
    int32 status = 2;
    // 错误信息
    string msg = 3;
  }
  repeated outData data = 1;
}


message ElmStoreInfo {
  //合作方门店ID
  string shop_id = 1;
  //供应商ID
  string supplier_id = 2;
  //供应商名称
  string supplier_name = 3;
  //商户名称
  string name = 4;
  //商户图片
  string shop_logo = 5;
  //配送方式 饿了么|饿了么星选(废弃)
  //1星火计划(试用) 4星火计划 5蜂鸟专送 6蜂鸟专送,KA 9蜂鸟快送 10星火众包(试用) 11星火众包 12新零售 13e基础 14e配送 15蜂鸟混合送 16蜂鸟质选 17星火计划KA 18星火众包KA 281e配送KA 289e基础KA 0暂无
  string delivery_type = 6;
  //商户营业状态
  string status = 7;
  //平台门店ID
  string baidu_shop_id = 8;
  //商户配送方式
  //1星火计划(试用) 4星火计划 5蜂鸟专送 6蜂鸟专送,KA 9蜂鸟快送 10星火众包(试用) 11星火众包 12新零售 13e基础 14e配送 15蜂鸟混合送 16蜂鸟质选 17星火计划KA 18星火众包KA 281e配送KA 289e基础KA 0暂无
  string delivery_party = 9;
}

message NewElmShopCategoryRequest {
  //分类ID
  string category_id = 1;
  //门店ID
  string shop_id = 2;
  //上级分类ID
  string parent_category_id = 3;
  //分类名称
  string name = 4;
  //排序
  string rank = 5;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 6;
}

message NewElmShopCategoryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //分类信息
  repeated NewElmShopCategory data = 4;
}

message NewElmShopCategory {
  //分类ID
  string category_id = 1;
  //分类名称
  string category_name = 2;
}

message DelElmShopCategoryRequest {
  //门店ID
  string shop_id = 1;
  //分类ID
  string category_id = 2;
  //分类名称
  string category_name = 3;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}

message DelElmShopCategoryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
}

message UpdateElmShopSkuPriceRequest {
  //门店ID
  string shop_id = 1;
  string skuid = 2;
  string upc = 3;
  //商品自定义ID
  string custom_sku_id = 4;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 5;
}

message GetElmShopBrandRequest {
  //第三级类目ID
  string cat3_id = 1;
  //按品牌关键字查询
  string keyword = 2;
  //页码
  int32 page = 3;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}

message GetElmShopBrandResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //数据
  repeated ShopBrandData data = 4;
}

message ShopBrandData {
  //总数
  int32 count = 1;
  //当前页
  int32 page_num = 2;
  //每页记录数
  int32 page_size = 3;
  //总页数
  int32 max_page_num = 4;
  //记录集
  repeated ShopBrandList detail = 5;
}

message ShopBrandList {
  //品牌ID
  string brand_id = 1;
  //品牌名称
  string brand_name = 2;
}

message UpdateElmShopSkuRequest {
  //合作方门店ID
  string shop_id = 1;
  //商品ID
  string sku_id = 2;
  //条形码
  string upc = 3;
  //商品名称
  string name = 4;
  //商品状态 1上架，0下架
  int32 status = 5;
  //货号
  string shelf_number = 6;
  //品牌ID
  int32 brand_id = 7;
  //品牌名称
  string brand_name = 8;
  //自定义分类ID
  int64 category_id = 9;
  //自定义分类名称
  string category_name = 10;
  //后台类目第三级类目ID
  int32 cat3_id = 11;
  //后台类目第三级类目ID
  repeated SkuPhotos photos = 12;
//  //富文本图文详情
//  string rtf = 13;
  //库存数量
  int32 left_num = 14;
  //销售价格
  int32 sale_price = 15;
  //重量
  float weight = 16;
  //商品自定义ID
  string custom_sku_id = 17;
  //商品描述
  string summary = 18;
  //商品准备时长
  int32 preparation_time = 19;
  //是否支持附加加工服务，0不支持， 1支持
  int32 process_type = 20;
  //加工服务内容
  repeated ProcessDetail process_detail = 21;
  //商家自定义属性
  repeated SkuProperty sku_property = 22;
  //预扣款重量
  int32 preminus_weight = 23;
  //起购量
  int32 minimum = 24;
  //商品增加步长
  int32 sale_step = 25;
  //售卖单位
  string sale_unit = 26;
  //部分更新字段
  string update_field = 27;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 28;
  //商品描述,本字段现已支持传入图文详情
  string desc=29;
}

message  BatchUpdateElmShopSkuRequest {
  int64  seller_id = 1;  // 内部seller_id
  int64 store_id = 2; // 内部store_id
  string shop_id = 3;  // 合作方门店id
  string platform_shop_id = 4;  // 平台门店id
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 5;

  // 商品信息
  repeated UpdateElmShopSkuRequest update_list = 6;
}

message  BatchCreateElmShopSkuRequest {
  int64  seller_id = 1;  // 内部seller_id
  int64 store_id = 2; // 内部store_id
  string shop_id = 3;  // 合作方门店id
  string platform_shop_id = 4;  // 平台门店id
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 5;

  // 商品信息
  repeated UpdateElmShopSkuRequest update_list = 6;
}

message SkuPhotos {
  //是否设为主图 1是, 0否
  int32 is_master = 1;
  //图片链接
  string url = 2;
}

message ProcessDetail {
  //加工类型
  string type = 1;
  //加工时长
  int32 time = 2;
}

message SkuProperty {
  //商家自定义属性项名称
  string name = 1;
  //属性值内容
  repeated SkuPropertyDetail detail = 2;
}

message SkuPropertyDetail {
  //属性值
  string k = 1;
}

message UploadPictureRequest {
  //合作方图片地址,和data属性二选一,都存在时以url为主
  string url = 1;
  //合作方图片的base64编码,和url二选一
  string data = 2;
  //为1时，返回url和water_url；非1时，只返回url
  string type = 3;
  //shop_id
  string shop_id = 4;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 5;
}

//饿了么订单类API
service ELMOrderService {
  //获取饿了么订单详细信息
  rpc GetELMOrderDetail (ELMOrderDetailRequest) returns (ELMOrderDetailResponse);
  //发起部分退款
  rpc ELMOrderPartreFund (ELMOrderPartreFundRequest) returns (ELMBaseResponse);
  //同意用户的取消订单申请,同意用户发起的部分退款申请,同意全单退申请 请求参数
  rpc ELMAgreereFund (ELMOAnswerFundRequest) returns (ELMBaseResponse);
  //拒绝用户的取消订单申请,拒绝用户发起的部分退款申请,拒绝全单退申请
  rpc ELMRefuseFund (ELMOAnswerFundRequest) returns (ELMBaseResponse);
  //拒绝用户的取消订单申请,拒绝用户发起的部分退款申请,拒绝全单退申请
  rpc ELMOrderCancel (ELMOrderCancelRequest) returns (ELMBaseResponse);
  //确认订单
  rpc ElmOrderConfirm (ELMOrderConfirmRequest) returns (ELMOrderConfirmlResponse);
  //订单拣货完成
  rpc ElmOrderPickcomplete (ElmOrderPickcompleteRequest) returns (ElmOrderPickcompleteResponse);
  //呼叫配送-order.callDelivery
  rpc ElmOrderCallDelivery (ElmOrderCallDeliveryRequest) returns (ElmOrderCallDeliveryResponse);
  //订单送出 - order.sendout
  rpc ElmOrderSendout (ElmOrderSendoutRequest) returns (ElmOrderSendoutResponse);
  //订单送达-order.complete
  rpc ElmOrderComplete (ElmOrderCompleteRequest) returns (ElmOrderCompleteResponse);
  //2.0订单逆向查询详情
  rpc ElmOrderReverseQuery (ElmOrderReverseQueryRequest) returns (ElmOrderReverseQueryResponse);
  //2.0订单逆向同意/拒绝
  rpc ElmOrderReverseProcess (ElmOrderReverseProcessRequest) returns (ELMBaseResponse);
  //2.0订单逆向商家发起逆向单
  rpc ElmOrderReverseApply (ElmOrderReverseApplyRequest) returns (ELMBaseResponse);
  //2.0订单逆向查询订单逆向可退信息及退款预览
  rpc ElmOrderReverseConsult (ElmOrderReverseConsultRequest) returns (ElmOrderReverseConsultResponse);
  //获取账单订单明细信息
  rpc ElmBillOrderDetailList(ElmBillOrderDetailListReq) returns(ElmBillOrderDetailListRes);
  //订单送达-order.complete
  rpc ElmDeliveryLocationSync (ElmDeliveryLocationSyncRequest) returns (ElmOrderCompleteResponse);
}
//zhou start
message ElmBillOrderDetailListReq{
  //平台门店id
  string baidu_shop_id = 1;
  //日期，查询传入日期当日账单。格式：10位时间戳。如传入为1632473565，查询为 2021-09-24 00:00:00 ~ 2021-09-24 23:59:59的账单。
  string bill_date = 2;
  //页码，每页默认20个订单。
  int32 page = 3;
  //第三方商户ID（商户自定义id）
  string shop_id = 4;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 app_channel = 5;
}
message ElmBillOrderDetailListRes{
  repeated ElmBillOrderDetail list = 1;
}
message ElmBillOrderDetail{
  string order_create_time = 1;
  string trade_create_time = 2;
  string order_id = 3;
  string date = 4;
}


//同意用户的取消订单申请,同意用户发起的部分退款申请,同意全单退申请 请求参数
message ELMOrderCancelRequest {
  //订单ID
  string order_id = 1;
  //取消原因分类 1：超出配送范围 2：商家已打烊 3：商品已售完 4：商品价格发生变化 5：顾客要求取消
  //6:顾客重复订单 7:商家太忙无法及时备货 8:顾客信息错误 9:假订单 10:API商户系统向门店推送订单失败 11:自定义输入
  string type = 2;
  //取消原因描述
  string reason = 3;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}

//发起部分退款请求参数
message ELMOrderPartreFundRequest {
  //订单ID
  string order_id = 1;
  //申请退款的商品信息
  repeated PartreFundProducts refund_product_list = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
  //申请退款类型, 枚举值：1 全退 ， 2 部分退（按商品部分退），如 部分退（2） 则 refund_product_list 参数不能为空
  string refund_type = 5;
  //发起退款原因 code，枚举值：7015-商品已售完、 7017-无骑手接单/无运力、 7053-骑手无法配送、 7054-无法联系到用户（未接听、关机、号码错误）、 7018-商户暂时不营业、
  // 7070-拣货人力不足、 7908-补差退款、 7001-其他原因、 7052-药师审核处方笺不通过（仅支持医药处方订单）
  string reason_code = 6;
  //详细原因信息，reason_code为7001时必填
  string reason_remarks = 7;
  //标品部分退资金类型，枚举值：0 按件数退，1 按照金额退 （部分退场景有效，不填 默认为 0 标品按件数退，称重品必须按金额退）
  string fund_calculate_type = 8;
  //非必填，reason_code为7015 缺货售中部分取消退款场景，是否需要平台外呼通知用户，仅refund_type=2时 传入有效；枚举值： 1:通知 ， 0 or null :不通知
  int32 need_ivr_user = 9;
}
//申请退款的商品信息
message PartreFundProducts {
  //子订单ID，必传
  string sub_biz_order_id = 1;
  //商品的sku码,和upc,custom_sku_id三选一
  string sku_id = 2;
  //商品的upc编码,和sku_id,custom_sku_id三选一
  string upc = 3;
  //商品的自定义编码ID,和sku_id,upc三选一
  string custom_sku_id = 4;
  //退款的该商品数量
  string number = 5;
  //平台商品ID
  int64 platform_sku_id = 6;
}
//同意用户的取消订单申请,同意用户发起的部分退款申请,同意全单退申请 请求参数
message ELMOAnswerFundRequest {
  //订单ID
  string order_id = 1;
  //主退单ID
  string refund_order_id = 2;
  //拒绝原因，如果是同意不需要
  string refuse_reason = 3;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}
//操作类请求的返回参数
message ELMBaseResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;

  Body Data = 4;
  // 饿了么商品批量创建和更新第三方返回数据
  ElmBatchReturn elm_batch_return = 5;


}
message ElmBatchReturn {
  ElmBatchBody body = 1;
	string cmd = 2;
	string encrypt = 3;
	string sign = 4;
	string ticket = 5;
	int64    timestamp = 6;
	string traceid = 7;
	string version = 8;
}
message ElmBatchBody {
  int64 errno = 1;
  string error = 2;
  ElmBatchBodyData data = 3;
	
		
}
message ElmBatchBodyData{
  repeated ElmBatchBodyDataFailList fail_list = 1;
  repeated ElmBatchBodyDataSuccessList success_list = 2;
  repeated ElmBatchBodyDataResultList result_list = 3;
}
message ElmBatchBodyDataFailList{
  string errno = 1;
  string name = 2;
  string custom_sku_id = 3;
  string upc = 4;
  string error = 5;
}

message ElmBatchBodyDataSuccessList{
  string name =1;
	string custom_sku_id = 2;
	string upc = 3;
}
message ElmBatchBodyDataResultList{
  int64  item_id = 1;
	int64    cat1_id = 2;
	int64    cat2_id = 3;
	int64    cat3_id = 4;
	string name = 5;
	string custom_sku_id = 6;
	string upc = 7;
	string sku_id = 8;
}


message Body  {
  int32 Errno = 1;
  string  Error = 2;
  repeated  ResultListVo Data = 3;
}

message ResultListVo {

  string item_id = 1;
  string sku_id = 2;
}



//zhou end

//zhang start
//获取饿了么订单详情请求参数
message ELMOrderDetailRequest {
  //订单ID
  string order_id = 1;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 2;
}

//获取饿了么订单详情返回参数
message ELMOrderDetailResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //饿了么订单详情
  ELMOrderBody OrderBody = 4;
}

message ELMOrderBody {
  //返回错误信息
  string elmError = 1;
  //返回错误码
  int32 errno = 2;
  //返回信息
  ELMOrderData OrderData = 3;
}

message ELMOrderData {
  //账号
  string source = 1;
  //返回信息
  ELMOrder Order = 2;
  //订单客户信息
  ELMUser OrderGetUser = 3;
  //商户信息
  ELMShop OrderGetShop = 4;
  //订单商品信息数组
  repeated ELMProducts OrderGetProductsList = 5;
  //优惠信息
  repeated ELMDiscount OrderGetDiscountsList = 6;
}

message ELMOrder {
  //订单ID
  string order_id = 1;
  //饿了么订单ID，与order_id值相同
  string eleme_order_id = 2;
  //订单来源 1 饿了么星选 2饿了么
  string order_from = 3;
  //是否是预约单 1即时单 2预约单
  int32 send_immediately = 4;
  //是否降级;1:是,0:否;
  //极少数订单因网络或信息交互异常，
  //导致订单部分字段（如订单金额）生成延迟，
  //此时订单会被标记为“已降级”状态，
  //需开发者重新调用查看订单详情接口获取完整订单数据。
  int32 down_flag = 5;
  //订单状态
  int32 status = 6;
  //取货类型 0-外卖到家，1-用户到店自提
  string business_type = 7;
  //用户到店自提取货码
  string pick_up_code = 8;
  //订单完成时间
  int64 finished_time = 9;
  //商家确认订单时间
  int64 confirm_time = 10;
  //订单取消时间
  string cancel_time = 11;
  //包装费，单位：分
  int32 package_fee = 12;
  //优惠总金额，单位：分
  int32 discount_fee = 13;
  //商户应收金额，订单预计给商家，单位：分
  int32 shop_fee = 14;
  //订单总金额，单位：分
  int32 total_fee = 15;
  //用户实付金额，单位：分
  int32 user_fee = 16;
  //付款类型 2 在线支付
  int32 pay_type = 17;
  //付款状态 1 未支付 2 已支付
  int32 pay_status = 18;
  //是否需要发票 1 是 2 否
  int32 need_invoice = 19;
  //发票抬头
  string invoice_title = 20;
  //纳税人识别号
  string taxer_id = 21;
  //订单备注
  string remark = 22;
  //创建时间
  string create_time = 23;
  //配送费，单位：分
  int32 send_fee = 24;
  //物流类型
  //1 蜂鸟 2 蜂鸟自配送 3 蜂鸟众包 4 饿了么众包
  //5 蜂鸟配送 6 饿了么自配送 7 全城送 8 快递配送
  string delivery_party = 25;
  //用户期望最早送达时间
  int64 send_time = 26;
  //用户期望最早送达时间
  int64 latest_send_time = 27;
  //佣金，单位：分
  int32 commission = 28;
  //骑士手机号
  string delivery_phone = 29;
}

message ELMUser {
  //客户ID
  string user_id = 1;
  //客户名称
  string name = 2;
  //客户所在省份
  string province = 3;
  //客户所在城市
  string city = 4;
  //客户所在区
  string district = 5;
  //客户电话，订单完成后48小时内可返回客户联系电话。
  //匿名订单会返回隐私小号，因小号存在有效期，如遇小号过期拨打不通，
  //可调用order.get接口重新获取。
  string phone = 6;
  //客户地址
  string address = 7;
  //客户百度经纬度
  ELMCoord OrderGetCoord = 8;
}

message ELMCoord {
  //送货地址百度经度
  string longitude = 1;
  //送货地址百度纬度
  string latitude = 2;
}

message ELMShop {
  //合作方门店ID
  string id = 1;
  //平台门店ID
  string baidu_shop_id = 2;
  //门店名称
  string name = 3;
}

message ELMProducts {
  //商品ID
  string baidu_product_id = 1;
  //商品自定义ID;未设置为空;
  string custom_sku_id = 2;
  //子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
  string sub_biz_order_id = 3;
  //商品类型,1.单品 2 套餐 3 配料
  int32 product_type = 4;
  //商品名称
  string product_name = 5;
  //商品份数
  int32 product_amount = 6;
  //总重量，单位：克
  int32 total_weight = 7;
  //商品单价
  int32 product_price = 8;
  //商品总价，单位：分
  int32 product_fee = 9;
  //包装费
  int32 package_price = 10;
  //包装费总价，单位：分
  int32 package_fee = 11;
  //包装数量
  int32 package_amount = 12;
  //包装数量
  int32 total_fee = 13;
  //商品优惠信息
  EMLProductSubsidy OrderProductSubsidy = 14;
}

message EMLProductSubsidy {
  //总优惠金额;单位分
  int32 discount = 1;
  //平台承担费用;单位分;
  int32 baidu_rate = 2;
  //商户承担费用;单位分;
  int32 shop_rate = 3;
  //代理商承担费用;单位分;
  int32 agent_rate = 4;
  //物流承担费用;单位分;
  int32 logistics_rate = 5;
}

message ELMDiscount {
  //优惠类型
  string Type = 1;
  //优惠金额 单位：分
  int32 Fee = 2;
  //活动ID
  string ActivityId = 3;
  //平台承担费用;单位分;
  int32 BaiduRate = 4;
  //商户承担费用;单位分;
  int32 ShopRate = 5;
  //代理商承担费用;单位分;
  int32 AgentRate = 6;
  //物流承担费用;单位分;
  int32 LogisticsRate = 7;
  //优惠描述
  string Desc = 8;
  //优惠信息商品
  repeated EMLDiscountProducts OrderDiscountProducts = 9;
}
//优惠信息商品
message EMLDiscountProducts {
  //商品ID
  string baidu_product_id = 1;
  //优惠后金额
  int32 now_price = 2;
}

message ELMOrderConfirmRequest {
  //订单ID
  string order_id = 1;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 2;
}

message ELMOrderConfirmlResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;

}
message ElmOrderPickcompleteRequest {
  //订单ID
  string order_id = 1;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 2;
}

message ElmOrderPickcompleteResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
}

message ElmOrderCallDeliveryRequest {
  //订单ID
  string order_id = 1;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 2;
}

message ElmOrderCallDeliveryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
}


message ElmOrderSendoutRequest {
  //订单ID
  string order_id = 1;
  //配送员电话，为空取商家联系电话
  string phone = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
}

message ElmOrderSendoutResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
}


message ElmOrderCompleteRequest {
  //订单ID
  string order_id = 1;
  //配送员电话，为空取商家联系电话
  string phone = 2;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 3;
  //配送状态
  //1:生成运单
  //2:配送待分配
  //3:骑士接单（必传）
  //8:骑士到店（必传）
  //20:骑手送出（必传）
  //30:配送完成（必传）
  //6:配送取消（必传）
  //7:配送异常（必传）
  //101:配送拒单
  int32 selfStatus = 4;
}

message ElmDeliveryLocationSyncRequest {
  //订单ID
  string order_id = 1;
  //当前时间，格式：10位时间戳
  string utc = 2;
  // 海拔高度，如无，可传入固定值	20
  string altitude = 3;
//纬度，建议使用高德坐标	39.90038
  string latitude = 4;
  //经度，建议使用高德坐标	116.389149
  string longitude = 5;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 6;
}

message ElmOrderCompleteResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //结果
  string data = 4;
}

//zhang end

// 饿了么商品列表请求参数
message ElmGetProductListRequest {
  // 饿了么门店id
  string shop_id = 1;
  // 页码
  int32 page = 2;
  // 每页数量
  int32 pagesize = 3;
  // 商品upc
  string upc = 4;
  // 商品id
  string sku_id = 5;
  // 商品自定义ID
  string custom_sku_id = 6;
  // 商品类型,取值范围0和1,0为非标品,1为标品
  int32 upc_type = 7;
  // 传入1,表示获取未绑定商品自定义分类的商品列表，门店维度管理自定义分类时有效
  int32 get_uncate = 8;
  // 传入1时获取被删除的商品列表
  int32 delete = 9;
  // 单维查询, 传入0或1，表示下架或上架状态的商品列表
  int32 enabled = 10;
  // 对商品最后更新时间update_time的区间筛选,开始时间,时间戳格式
  int32 start_time = 11;
  // 对商品最后更新时间update_time的区间筛选,截至时间,时间戳格式
  int32 end_time = 12;
  // 商品id游标, 使用该值时，page必须始终传入1。 使用场景：根据门店全量查询数据时，分页太多容易超时，需要根据sku_id_offset查询，第一次传入0，后续每次使用返回结果中的sku_id_offset当作下次的入参
  int32 sku_id_offset = 13;
  // 是否返回商品自定义分类信息。1：需要返回；0或不传：不需要返回
  int32 include_cate_info = 14;
  // 【淘鲜达专用】，非淘鲜达商户请使用shop_id字段
  string platform_shop_id = 15;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 16;
}

// 饿了么商品列表响应参数
message ElmGetProductListResponse {
  // 返回错误码
  int32 code = 1;
  // 返回信息
  string message = 2;
  // 错误信息
  string error = 3;
  // 商品列表
  ElmProductListDto data = 4;
}

// 商品列表
message ElmProductListDto {
  // 商品列表
  repeated ElmProductList list = 1;
  // 页码
  int32 page = 2;
  // 总页数
  int32 pages = 3;
  // 商品总数
  int32 total = 4;
  // 商品id游标
  int64 sku_id_offset = 5;
}

// 饿了么商品列表
message ElmProductList {
  // 商品所属自定义分类id,多个以逗号分隔
  string custom_cat_ids = 1;
  // 商品所属自定义分类id及对应的名称
  repeated CustomCatList custom_cat_list = 2;
  // 商品自定义ID
  string custom_sku_id = 3;
  // 是否在活动中(0:不在,1:在)
  int32 is_in_activity = 4;
  // 库存,范围：0~9999
  int32 left_num = 5;
  // 市场价格，单位：分，范围：1~99999900
  int32 market_price = 6;
  // 称重品字段】起购量，如：2条。说明: （1）售卖单位为kg时：三位小数；（2）售卖单位是g时：正整数；（3）其他售卖单位：正整数
  int32 minimum = 7;
  // 商品名称
  string name = 8;
  // 是否冷链商品
  string need_ice = 9;
  // 商品图片
  repeated PhotoList photos = 10;
  //【称重品字段】预扣款重量(kg): （1）售卖单位为kg时,值为1；（2）售卖单位是g时,值为0.001；（3）其他售卖单位时，支持三位小数
  int32 preminus_weight = 11;
  // 商品准备时长, 单位: 分钟, 范围: 0~120
  int32 preparation_time = 12;
  // 药品标识(唯药品才返回此字段 1: RX 2: OTC)
  string prescription_type = 13;
  // 加工服务内容
  repeated SkuProcessDetail process_detail = 14;
  // 是否为加工商品，0否 1是
  int32 process_type = 15;
  // 产地国家
  string production_addr1 = 16;
  // 产地省份
  string production_addr2 = 17;
  // 产地城市
  string production_addr3 = 18;
  // 销售价格，单位：分，范围：1~99999900
  int32 sale_price = 19;
  // 【称重品字段】商品增加步长，如：1条。 说明:（1）售卖单位为kg时：三位小数；（2）售卖单位是g时：正整数；（3）其他售卖单位：值为1
  int32 sale_step = 20;
  // 【称重品字段】售卖单位，称重品包含两种情况： (A) 纯称重，枚举值如下: kg, g。（B）按件称重，枚举值如下：个, 份, 盒, 箱, 条, 只, 头, 瓶, 袋, 把, 包。
  string sale_unit = 21;
  // 商品货架号
  string shelf_number = 22;
  // 商品ID
  int64 sku_id = 23;
  // 商家自定义属性，如，甜度：不加糖，半糖，全糖。辣度：不加辣，微辣，中辣，麻辣。
  repeated CustomPropertyDto sku_property = 24;
  // 商品上下架状态，1为上架，0为下架
  string status = 25;
  // 商品简介描述
  string summary = 26;
  // 商品upc
  string upc = 27;
  // 商品类型,取值范围0和1,0为非标品,1为标品
  int32 upc_type = 28;
  // 毛重，单位克
  string weight = 29;
  // 【称重品字段】称重标识，1:称重品,2:非称重品。
  int32 weight_flag = 30;
  // 多规格商品详情
  repeated SkuQuerySpecResult sku_spec = 31;
  // 一级后台类目id
  int64 cate_id1 = 32;
  // 一级后台类目名称
  string cate_name1 = 33;
  // 二级后台类目id
  int64 cate_id2 = 34;
  // 二级后台类目名称
  string cate_name2 = 35;
  // 叶子后台类目id
  int64 cate_id = 36;
  // 叶子后台类目名称
  string cate_name = 37;
  // 商品在前台类目下的排序值
  int32  rank = 38;
}

// 多规格商品详情
message SkuQuerySpecResult {
  // 对应的商家产品多规格ID
  int32 product_spec_id = 1;
  // 多规格ID
  int32 sku_spec_id = 2;
  // 多规格自定义ID
  string sku_spec_custom_id = 3;
  // 库存数量,范围：0~9999
  int32 left_num = 4;
  // 销售价格，单位：分，范围：1~99999900
  int32 sale_price = 5;
  // 	条形码
  string upc = 6;
  // 重量，单位：克(g)，范围：[1,99999]
  int32 weight = 7;
  // 销售属性
  repeated OpenApiPropValue spec_property = 8;
}

// 销售属性
message OpenApiPropValue {
  // 属性id
  int32 prop_id = 1;
  // 属性文本
  string prop_text = 2;
  // 属性值ID
  int32 value_id = 3;
  // 属性值文本
  string value_text = 4;
}

// 商家自定义属性，如，甜度：不加糖，半糖，全糖。辣度：不加辣，微辣，中辣，麻辣。
message CustomPropertyDto {
  // 属性值内容
  repeated CustomPropertyDetail detail = 1;
  // 商家自定义属性项名称，比如甜度。
  string name = 2;
}

// 属性值内容
message CustomPropertyDetail {
  //属性值，1个属性项下可对应多个属性值。比如：不加糖，半糖，全糖。
  string ice = 1;
  string k = 2;
}

// 商品图片列表
message PhotoList {
  // 是否为主图 1是, 0否
  int32 is_master = 1;
  // 	图片的绝对地址
  string url = 2;
}

// 加工服务内容
message SkuProcessDetail {
  // 加工时长，加工商品必填，加工时长会计入到配送时间中，请谨慎填写。单位为分钟，仅支持数字，最大值120
  int32 time = 1;
  // 加工类型，加工商品必填。限5个字符，字符类型支持中英文，如：宰杀、去皮、加热
  string type = 2;
}

// 商品所属自定义分类id及对应的名称
message CustomCatList {
  // 商品所属自定义分类id
  string custom_cat_id = 1;
  // 商品所属自定义分类名称
  string custom_cat_name = 2;
}


//retail/batchget 按app维度，当前接口最高调用50次/秒。
message RetailBatchGetRequest {
  // 门店的财务编码，调用时进行相应的转换
  string finance_code = 1;
  // APP方商品id，即商家中台系统里商品的编码（spu_code值,接口最多支持传入100个）每个app_spu_code以,分割
  string app_food_code = 2;
  // 店铺主体Id
  int32 store_master_id = 3;
}

//查询门店商品列表 按app维度，当前接口最高调用20次/秒
message RetailListRequest {
  // 门店的财务编码，调用时进行相应的转换
  string finance_code = 1;
  // 页码
  int32 page_index = 2;
  // 分页大小,最大200
  int32 page_size = 3;
  string app_poi_code = 4;
  int32 store_master_id = 5;
}

message RetailListResponse {
  // 商品数据
  repeated StoreRetailInfo data = 1;
  // 总记录数
  RetailExtraInfo extra_info = 2;
}

message RetailExtraInfo {
  // 总记录数
  int32 total_count = 1;
}

//门店商品信息
message StoreRetailInfo {
  // APP方商品id，即商家中台系统里商品的编码 必镇
  string app_food_code = 1;
  // 商品名称 创建时必镇
  string name = 2;
  // 商品描述  非必镇
  string description = 3;
  // APP方商品的skus信息，支持同时传多个sku信息。
  string skus = 4;
  // 商品的最小购买量，创建商品时，min_order_count字段信息如不传则默认为1。 必镇
  int32 min_order_count = 5;
  // 商品的售卖单位。创建商品时，unit字段信息如不传则默认为“份”。 非必镇
  string unit = 6;
  // 分类名称 创建时必填
  string category_name = 7;
  // 商品上下架状态，字段取值范围：0-上架，1-下架。 非必镇
  int32 is_sold_out = 8;
  // 商品图片： 非必镇
  string picture = 9;
  // 商品在当前分类下的排序：非必镇
  int32 sequence = 10;
  // 美团内部商品类目id
  // 门店启用结构化属性并且传递了销售或普通属性则
  // 1.创建时必传，2.若商品创建时未传tag_id，更新时必传(只需传一次)。若门店未启用或未传递销售属性/普通属性则非必传
  int64 tag_id = 11;
  // 商品品牌 非必镇
  string zh_name = 12;
  // 商品的产地 非必镇
  string origin_name = 13;
  // 商品的图片详情 非必镇
  string picture_contents = 14;
  // 是否为“力荐”商品，字段取值范围：0-否， 1-是。 非必镇
  int32 is_specialty = 15;
  // 视频ID 非必镇
  int64 video_id = 16;
  // 分类ID 非必镇
  int32 category_id = 17;
}

message RetailInitDataCategoryRequest {
  string app_poi_code = 1;
  string app_food_code = 2;
  string category_name = 3;
  string category_code = 4;
  int32 operate_type = 5;
  int32 store_master_id = 6;
}

message DeliveryAliInfoRequest {
  //快递编号
  string number = 1;
  //快递公司编码
  string type = 2;
  //手机号码
  string mobile = 3;
}
message DeliveryAliInfoResponse {
  //0:正常查询 201:快递单号错误 203:快递公司不存在 204:快递公司识别失败 205:没有信息 207:该单号被限制，错误单号
  string status = 1;
  string msg = 2;
  string number = 3;
  string type = 4;
  //0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
  string delivery_status = 5;
  //1.是否签收
  string is_sign = 6;
  //快递公司名称
  string exp_name = 7;
  //快递公司官网
  string exp_site = 8;
  //快递公司电话
  string exp_phone = 9;
  //快递员 或 快递站(没有则为空)
  string courier = 10;
  //快递员电话 (没有则为空)
  string courier_phone = 11;
  //快递轨迹信息最新时间
  string update_time = 12;
  // 发货到收货消耗时长 (截止最新轨迹)
  string take_time = 13;
  //快递公司LOGO
  string logo = 14;
  repeated Node list = 15;
  message Node {
    string time = 1;
    string desc = 2;
  }
}

message UpdateElmSkuStockRequest {
  //第三方门店id
  string shop_id = 1;
  //第三方的skuid
  string skuid_stocks = 2;
  //upc条码
  string upc_stocks = 3;
  //本地skuid
  string custom_sku_id = 4;

  // app_channel
  int64 app_channel = 5;
}

message UpdateElmSkuStockResponse {
  int64 code = 1;
  string message = 2;
  string error = 3;
}


//2.0订单逆向
message ElmOrderReverseQueryRequest {
  //饿了么订单号
  string order_id = 1;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 2;
}

message ElmOrderReverseQueryResponse {
  //饿了么订单号
  string order_id = 1;
  //退款成功后订单当前剩余【订单总金额】，单位：分
  int64 remain_total_price = 2;
  //退款成功后订单当前剩余【用户实付总金额】，单位：分
  int64 remain_user_total_amount= 3;
  //退款成功后当前剩余【订单总优惠】金额，单位：分
  int64 remain_discount_total_amount = 4;
  //退款成功后当前订单剩余【商户应收】金额，单位：分
  int64 merchant_income = 5;
  //退款成功后当前订单剩余佣金总和【技术服务费（settled_merchant_commission_amount）+履约服务费（base_logistics_amount）+支付服务费（pay_channel_fee）】，单位：分
  int64 commission_amount = 6;
  //退款成功退款后当前订单剩余【技术服务费】，单位：分
  int64 settled_merchant_commission_amount = 7;
  //退款成功后当前订单剩余【履约服务费】，单位：分
  int64 base_logistics_amount = 8;
  //退款成功后当前订单剩余【支付服务费】，单位：分
  int64 pay_channel_fee = 9;
  //主退单列表
  repeated ReverseOrderList reverse_order_list = 10;
  message ReverseOrderList {
    //饿了么订单号
    string order_id = 1;
    //零售退款单ID
    string refund_order_id = 2;
    //退单发起退款时传入的幂等Id
    string idempotent_id = 3;
    //退款发起角色:10用户,20商户,25API商家,30客服,40系统,50物流,60风控
    string operator_role = 4;
    //逆向单场景：100 售中, 200 售后
    string scene = 5;
    //该退单的上一个退款状态：0初始化，10申请，20拒绝，30仲裁，40关闭，50成功，60失败
    int32 last_refund_status = 6;
    //该退单当前退款状态：0初始化，10申请，20拒绝，30仲裁，40关闭，50成功，60失败
    int32 refund_status = 7;
    //是否退货标志 0：否 1：是
    int32 whether_return_goods = 8;
    //该退单的上一个退货子状态:0无效状态，1001买家已申请退货等待审批处理，1002商家拒绝退货申请，1003退货仲裁已发起客服介入中，1004已同意退货等待发货，
    //1005已发货等待卖家确认收货，1006已收到货并同意退款，1007未收到货不同意退款，1008退货关闭
    int32 last_return_goods_status = 9;
    //该退单的当前退货子状态:0无效状态，1001买家已申请退货等待审批处理，1002商家拒绝退货申请，1003退货仲裁已发起客服介入中，1004已同意退货等待发货，
    //1005已发货等待卖家确认收货，1006已收到货并同意退款，1007未收到货不同意退款，1008退货关闭
    int32 return_goods_status = 10;
    //该逆向单申请退用户的金额，单位分
    int64 apply_refund_user_amount = 11;
    //该逆向单实际退款给用户的金额，单位分
    int64 refund_user_amount = 12;
    //发起退单的原因code，具体原因枚举值详见文档 https://open-retail.ele.me/#/guide?topic=ntmt8f
    int32 refund_reason_code = 13;
    //发起退单的原因code描述
    string refund_reason_code_desc = 14;
    //发起退款的原因附加备注描述信息
    string refund_reason_content = 15;
    //退单凭证列表
    repeated string image_list = 16;
    //本次退单退款成功后，订单所有商品是否已全部退完，0|未退完 1|已退完
    int32 is_refund_all = 17;
    //逆向单创建（申请）时间戳，单位毫秒
    int64 apply_time = 18;
    //退单补贴分摊金额
    DiscountDetail discount_detail = 19;
    //退单购物金详情
    RefundShopCardDetail refund_shop_card_detail = 20;
    //退单中的退货信息
    ReturnGoodsInfo return_goods_info = 21;
    //子退单列表
    repeated SubReverseOrderList sub_reverse_order_list = 22;
    message RefundShopCardDetail {
      //退用户购物金金额，单位：分
      int32 refund_shop_card_price = 1;
      //本金金额，单位：分
      int32 base_shop_card_price = 2;
      //赠金金额：单位：分
      int32 give_shop_card_price = 3;
      //赠金中【平台承担】金额，单位：分
      int32 platform_give_shop_card_rate = 4;
      //赠金中【商家承担】金额，单位：分
      int32 shop_give_shop_card_rate = 5;
    }
    message ReturnGoodsInfo {
      //退货方式，枚举值：0-商家上门取货；1-自行送回
      int32 return_goods_type = 1;
      //自行退回方式，枚举值：1.快递退回；2. 跑腿退回；3.自行送回
      int32 self_return_type = 2;
      //商家上门取货时间(开始)， returnGoodsType = 1 时有效，格式：时间戳，精确到毫秒
      int64  shopGiveShopCardRate = 3;
      //商家上门取货时间(结束) ，returnGoodsType = 1 时有效，格式：时间戳，精确到毫秒
      int64 expect_pick_up_end_time = 4;
      //商家上门取货地址，returnGoodsType = 1 时有效
      string pick_up_address = 5;
      //商家上门取货 联系人名称，returnGoodsType = 1 时有效
      string contact_name = 6;
      //商家上门取货 用户真实手机号，returnGoodsType = 1 时有效
      string contact_phone = 7;
      //商家上门取货 用户隐私手机号，returnGoodsType = 1 时有效
      int32 privacy_contact_phone = 8;
      //用户在C端确认退回时间，格式：时间戳，精确到毫秒
      int64 send_off_time = 9;
      //用户自行送回的快递单号，当selfReturnType = 1 时 必填
      string logistics_order_id = 10;
    }
    message SubReverseOrderList {
      //商品多规格自定义id
      string custom_sku_spec_id = 1;
      //商品子单本次退款状态，枚举值：10 申请；20 拒绝；30 仲裁；40 关闭；50 成功；60 失败
      int32 refund_status = 2;
      //饿了么商品ID
      int64 platform_sku_id = 3;
      //虚拟订单类型，commodityType=4 商品类型为虚拟商品是有效；0:普通实物商品 1:包装费子单 2:处方服务费子单、3:附加0.01费子单、4:配送费子单
      string virtual_type = 4;
      //是否是赠品子单，true 是， false 否
      bool free_gift = 5;
      //申请退款件数/金额，件数单位：整型，金额单位：分
      int64 apply_quantity = 6;
      //商品 upc 信息
      string upc = 7;
      //商品子单类型, 1:称重品、2:标品、3:按件称重、4:虚拟商品子单
      int32 commodity_type = 8;
      //赠品子单关联的主品子单Id列表 （free_gift = true 时，有效）
      repeated string gift_related_sub_biz_order_id_list = 9;
      //商品子单 退款算价类型 ，枚举值：0按件数退，1按照金额退
      int32 fund_calculate_type = 10;
      //商品子单唯一标识
      string sub_biz_order_id = 11;
      //商家同意件数/金额，件数单位：整型，金额单位：分
      int64 refund_quantity = 12;
      //商品子单的退款分摊明细详情
      DiscountDetail discount_detail = 13;
      //商品子单的上一次退款状态，枚举值：10 申请；20 拒绝；30 仲裁；40 关闭；50 成功；60 失败
      int32 last_refund_status = 14;
      //主退单ID，标识订单一次退款/退货
      int64 refund_order_id = 15;
      //商品自定义id
      string custom_sku_id = 16;
      //商品子单实际退用户的金额，单位：分
      int64 refund_user_amount = 17;
      //商品子单申请创建时间，单位：毫秒
      int64 apply_refund_time = 18;
      //sku 商品名称
      string sku_name = 19;
      //商品子单用户申请退款金额，单位：分
      int64 apply_refund_user_amount = 20;
    }
  }
}

message DiscountDetail {
  //商品原总价 = （用户实付 + 补贴总额 discount_total_amount ）
  int64 total_price = 1;
  //本次退款总金额中的补贴总额，单位：分
  int64 discount_total_amount = 2;
  //本次退款总金额中 包含的【商家】承担金额，单位：分
  int64 merchant_discount_amount = 3;
  //本次退款总金额中 包含的【平台】承担金额，单位：分
  int64 platform_discount_amount = 4;
  //本次退款总金额中 包含的【代理商】承担金额，单位：分
  int64 agent_discount_amount = 5;
  //本次退款总金额中 包含的【用户】承担金额，单位：分
  int64 user_discount_amount = 6;
}

message ElmOrderReverseProcessRequest {
  //逆向单ID（请通过逆向推送消息order.reverse.push获取）
  string reverse_order_id = 1;
  //饿了么订单号
  string order_id = 2;
  //幂等Id（商家自定义），请求唯一标识
  string idempotent_id = 3;
  //逆向单审批操作类型，枚举值：【1-同意全单/部分退款申请 、 2-拒绝全单/部分退款申请、 3-同意退货申请 、 4-拒绝退货申请】
  string action_type = 4;
  //当拒绝场景必填，即action_type = 2 or action_type =4 时 必填，枚举值： 7019-双方协商一致不再取消订单、 7020-商品已经备货完成、 7021-商品已送出、
  // 7802-商品发出时完好、 7803-用户未举证/举证无效、 7804-商品影响二次销售、 7805-商品不符合7天无理由退款、 7302-未收到退货(仅退货单支持传入该code)、 7001-其他原因
  string reason_code = 5;
  //原因备注说明信息（商家自定义），当reason_code为7001时 必填
  string reason_remarks = 6;
  //同意申请退款的商品明细列表
  repeated RefundProductList refund_product_list = 7;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 8;
}

message RefundProductList {
  //商品子单ID
  string sub_biz_order_id = 1;
  //平台商品ID
  int64 platform_sku_id = 2;
  //申请退款件数（整型）
  string number = 3;
  //申请退款金额（单位：分，整型）
  string refund_amount = 4;
}

message ElmOrderReverseApplyRequest {
  //饿了么订单号
  string order_id = 1;
  //幂等Id（商家自定义），请求唯一标识
  string idempotent_id = 2;
  //申请退款类型, 枚举值：1 全退 ， 2 部分退（按商品部分退），如 部分退（2） 则 refund_product_list 参数不能为空
  string refund_type = 3;
  //发起退款原因 code，枚举值：7015-商品已售完、 7017-无骑手接单/无运力、 7053-骑手无法配送、 7054-无法联系到用户（未接听、关机、号码错误）、 7018-商户暂时不营业、
  //7070-拣货人力不足、 7908-补差退款、 7001-其他原因、 7052-药师审核处方笺不通过（仅支持医药处方订单）
  string reason_code = 4;
  //详细原因信息，reason_code为7001时必填
  string reason_remarks = 5;
  //标品部分退资金类型，枚举值：0 按件数退，1 按照金额退 （部分退场景有效，不填 默认为 0 标品按件数退，称重品必须按金额退）
  string fund_calculate_type = 6;
  //非必填，reason_code为7015 缺货售中部分取消退款场景，是否需要平台外呼通知用户，仅refund_type=2时 传入有效；枚举值： 1:通知 ， 0 or null :不通知
  string need_ivr_user = 7;
  //申请退款商品列表信息（非必填），部分退（ refund_type = 2）时，refund_product_list 必填
  repeated RefundProductList refund_product_list = 8;
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 9;
}

message ElmOrderReverseConsultRequest {
  //饿了么订单号
  string order_id = 1;
  //查询订单剩余可退商品信息； true 查询订单剩余可退商品及分摊信息 ， false or null 按照入参传入商品子单列表（order_line_list）查询可退商品金额及分摊信息
  bool refund_all_sub_order = 2;
  //需退款预览的商品子单列表， refund_all_order_line = true , 传入的子单列表无效
  repeated SubOrderList sub_order_list = 3;
  message SubOrderList {
    //商品子单ID（必填）
    string sub_biz_order_id = 1;
    //平台商品ID
    int64 platform_sku_id = 2;
    //退款咨询算价类型，枚举值： 0:按件数退、 1:按金额退、2:按重量退（查询商品重量可退金额）
    int32 refund_cal_type = 3;
    //退款数量 （refund_cal_type = 0 时，必填）
    int64 refund_quantity = 4;
    //退款金额，单位/分 (refund_cal_type = 1 时，必填)
    int64 refund_amount = 5;
    //实际要退的重量，单位/克 g （refund_cal_type = 2 时，必填）
    int64 refund_weight = 6;
  }
  // app渠道 1.阿闻自有,2.TP代运营
  int32 appChannel = 4;
}

message ElmOrderReverseConsultResponse {
  //饿了么订单号
  string order_id = 1;
  //可退回用户的总金额，单位/分
  int64 refund_user_total_amount = 2;
  //退回平台的总金额，单位/分
  int64 merchant_refund_total_amount = 3;
  //是否已全部退完；枚举值：true 是、 false 否
  bool is_refund_all = 4;
  //商品子单 算价结果明细(注：订单包装费、配送费等虚拟费用会以商品子单结构体现)
  repeated SubOrderConsultResultList sub_order_consult_result_list = 5;
  PartRefundCheckResult part_refund_check_result = 6;
  PartRefundCheckResult all_refund_check_result = 7;
  message SubOrderConsultResultList {
    //商品子单ID（必填）
    string sub_biz_order_id = 1;
    //平台商品ID
    int64 platform_sku_id = 2;
    //商家商品自定义id
    string custom_sku_id = 3;
    //商品多规格自定义id
    string custom_sku_spec_id = 4;
    //商品 upc 信息
    string upc = 5;
    //sku商品名称
    string sku_name = 6;
    //商品子单类型, 枚举值：1:称重品、2:标品、3:按件称重、4:虚拟费用商品
    int32 commodity_type = 7;
    //虚拟商品子单类型, commodity_type =4 时有效；枚举值： 0:普通实物商品 1:包装费 2:处方服务费 3:附加0.01费 4:配送费
    int32 virtual_type = 8;
    //是否是赠品商品子单，枚举值：true 是， false 否
    bool free_gift = 9;
    //赠品商品子单关联的主品子单Id列表 （free_gift = true 时，有效）
    repeated string gift_related_sub_biz_order_id_list = 10;
    //当前商品子单 应退商品数量，商品总数量 = cur_refund_quantity+ refunded_quantity + refundable_quantity
    int64 cur_refund_quantity = 11;
    //当前商品子单 已退商品数量（如：称重品：全部退完时为 1 ， 未退完时为 0 ）
    int64 refunded_quantity = 12;
    //当前商品子单 剩余可退的商品数量， 整型
    int64 refundable_quantity = 13;
    //当前商品子单 应退用户的实付金额，单位/分，商品用户总实付 = cur_refund_user_amount + refunded_user_amount + refundable_user_amount
    int64 cur_refund_user_amount = 14;
    //当前商品子单 已退用户实付金额，单位/分
    int64 refunded_user_amount = 15;
    //当前商品子单 剩余可退的用户实付金额，单位/分
    int64 refundable_user_amount = 16;
    //当前商品子单 退款原总价，单位/分，商品总原价 = cur_refund_total_price + refunded_total_price + refundable_total_price
    int64 cur_refund_total_price = 17;
    //当前商品子单 已退的原价总额，单位/分
    int64 refunded_total_price = 18;
    //当前商品子单 剩余可退 原价总额，单位/分
    int64 refundable_total_price = 19;
    //按照当前传参商品预算价，该商品子单是否已退完，枚举值：true 已退完，false 未退完
    bool is_refund_all = 20;
    //是否为组合品
    bool is_combine = 21;
    SubOrderDiscountDetail sub_order_discount_detail = 22;
    message SubOrderDiscountDetail {
      //原总价 = （用户实付 cur_refund_user_amount + 补贴总额 discount_total_amount ），单位：分
      int64 total_price = 1;
      //当前退款总金额中的补贴总额，单位：分
      int64 discount_total_amount = 2;
      //当前退款总金额中 包含的【商家】承担金额，单位：分
      int64 merchant_discount_amount = 3;
      //当前退款总金额中 包含的【平台】承担金额，单位：分
      int64 platform_discount_amount = 4;
      //当前退款总金额中 包含的【代理商】承担金额，单位：分
      int64 agent_discount_amount = 5;
      //当前退款总金额中 包含的【用户】承担金额，单位：分
      int64 user_discount_amount = 6;
    }
  }
  message PartRefundCheckResult {
    //校验结果 true: 支持 、 false:不支持
    bool check_result = 1;
    //不支持原因code ， 不支持的原因code；check_result = false 有效
    string error_code = 2;
    //不支持原因描述，不支持的原因描述；check_result = false 有效
    string error_message = 3;
  }
}


service DaDaService{
  //查询运费
  rpc QueryDeliverFee(DaDaDeliverFeeRequst) returns (DaDaDeliverFeeResponse);
  //查询运费后下单
  rpc AddAfterQuery(DaDaAddAfterQueryRequst) returns (ExternalResponse);
  //取消订单
  rpc FormalCancel(DaDaformalCancelRequst) returns (ExternalResponse);
  //查询订单详情
  rpc Query(DaDaformalCancelRequst) returns (DaDaQueryResponse);
  //商家确认物品已返还
  rpc ConfirmGoods(DaDaformalCancelRequst) returns (ExternalResponse);
}

service FnService{
  //查询运费
  rpc QueryDeliverFee(FnDeliverFeeRequst) returns (FnBaseResponse);
  //查询运费后下单
  rpc AddAfterQuery(FnDeliverFeeRequst) returns (FnBaseResponse);
  //取消订单
  rpc CancelOrder(FnCancelOrderRequst) returns (FnBaseResponse);
  //查询订单详情
  rpc Query(FnDeliverFeeRequst) returns (FnBaseResponse);
  //查询骑手信息
  rpc GetKnightInfo(FnCancelOrderRequst) returns (FnBaseResponse);
  //通过code获取token
  rpc GetToken(TokenRequst) returns (FnBaseResponse);

}

//蜂鸟查询运费请求参数
message TokenRequst{
  //授权的code
  string  code=1;
  //取消原因code
  string  merchant_id=2;
}

//蜂鸟查询运费请求参数
message FnCancelOrderRequst{
  //取消的角色	1商户取消, 2 用户取消
int32  order_cancel_role=1;
  //取消原因code
  int32  order_cancel_code=2;
  //	取消原因补充
  string  order_cancel_other_reason=3;
  //外部单号
  string  partner_order_code=4;

}
message FnBaseResponse{
  //返回code
  string code = 1;
  //错误原因
  string msg = 2;
  //返回的业务数据
  string business_data=3;
}

//蜂鸟查询运费请求参数
message FnDeliverFeeRequst{
  //外部订单号
  string partner_order_code = 1;
  // 货物件数
  int32 goods_count = 2;
  // 收货人纬度
  double receiver_latitude = 3;
  // 订单商品总金额 单位：分
  int64 goods_total_amount_cent = 4;
  //订单类型（1:即时单，3:预约单）
  int32 order_type = 5;
  //货物总重量 单位：kg
  float goods_weight = 6;
  //订单货物实付金额 单位：分
  int64 goods_actual_amount_cent=7;
  // 收货人经度
  double receiver_longitude = 9;
  // 收货人地址
  string receiver_address = 10;
  // 经纬度来源  坐标经纬度来源（1:腾讯地图,2:百度地图, 3:高德地图），蜂鸟建议使用高德地图
  int32 position_source = 11;
  //货物明细
  repeated OrderItemOpenapiDto goods_item_list=12;
  //优惠后配送费总价格(含入参小费金额) 入单实际配送费价格  不为null则校验配送费价格，不一致拒绝入单 (含入参小费)校验预询配送费价格时 必传
  int64 actual_delivery_amount_cent=13;
  //预询后下单标识 为null不校验配送费价格直接入单 不为null强校验配送费价格 与入参价格不一致拒绝入单
  string  pre_create_order_t_index_id=14;
  //门店id
  int64 chain_store_id=15;
  //收货人名称
  string  receiver_name=16;
  //收货人联系方式
  string receiver_primary_phone=17;

}
message OrderItemOpenapiDto{
  //商品实际支付金额
  int64 item_actual_amount_cent = 1;
  //商品原价
  int64 item_amount_cent = 2;
}

message DaDaQueryResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  DaDaQueryData data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;
}

message DaDaQueryData {
  //第三方订单编号
  string orderId = 1;
  //订单状态(待接单＝1,待取货＝2,配送中＝3,已完成＝4,已取消＝5, 指派单=8,妥投异常之物品返回中=9, 妥投异常之物品返回完成=10, 骑士到店=100,创建达达运单失败=1000 可参考文末的状态说明）
  int32 statusCode = 2;
  //订单状态
  string statusMsg = 3;
  //骑手姓名
  string transporterName = 4;
  //骑手电话
  string transporterPhone = 5;
  //骑手经度
  string transporterLng = 6;
  //骑手纬度
  string transporterLat = 7;
  //订单总费用，包含运费、小费、保价费
  double deliveryFee = 8;
  //配送距离,单位为米
  double distance = 9;
  //发单时间
  double createTime = 10;
}

message DaDaDeliverFeeResponse {
  //状态码
  int32 code = 1;
  //消息
  string message = 2;
  //错误信息
  string error = 3;
  //json 格式数据
  DaDaDeliverFeeData data = 4;
  //外部接口返回错误码（例如美配，美团）
  string external_code = 5;
}

message DaDaDeliverFeeData {
  //配送距离(单位：米)
  float distance = 1;
  //实际运费(单位：元)，运费减去优惠券费用
  float fee = 2;
  //运费(单位：元)
  float deliverFee = 3;
  //平台订单号
  string deliveryNo = 4;
  //保价费(单位：元)
  float insuranceFee = 5;
  //小费（单位：元，精确小数点后一位，小费金额不能高于订单金额。）
  float tips = 6;
  //外部接口返回错误码（例如美配，美团）
  int64 expiredTime = 7;
}

message DaDaDeliverFeeRequst{
  //门店编号，门店创建后可在门店列表查看，填写了财务编码可以不填写这个
  string shop_no = 1;
  // 第三方订单ID
  string origin_id = 2;
  // 订单金额（单位：元）
  double cargo_price = 3;
  // 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
  int32 is_prepay = 4;
  //收货人姓名
  string receiver_name = 5;
  //收货人地址
  string receiver_address = 6;
  //回调URL
  string callback = 7;
  //订单重量（单位：Kg）
  double cargo_weight = 8;
  //收货人地址纬度（高德坐标系，若是其他地图经纬度需要转化成高德地图经纬度，高德地图坐标拾取器）
  double receiver_lat = 9;
  //收货人地址经度（高德坐标系，若是其他地图经纬度需要转化成高德地图经纬度，高德地图坐标拾取器)
  double receiver_lng = 10;
  //收货人电话
  string receiver_phone = 11;
  //取货码
  string fetch_code = 12;
}
message DaDaAddAfterQueryRequst{
  //平台订单编号
  string deliveryNo = 1;
  // 第三方订单ID
  string origin_id = 2;
}
message DaDaformalCancelRequst{
  //平台订单编号
  string order_id = 1;
  //取消原因ID
  int32 cancel_reason_id = 2;
  //取消原因(当取消原因ID为其他时，此字段必填)
  string cancel_reason = 3;

}

//工具服务
service ToolService{
  //删除第三方以及本地上架的商品信息
  rpc DealErrorProductFromChannel(DealErrorProductFromChannelReq) returns (google.protobuf.Empty);
}
message DealErrorProductFromChannelReq{}

message ElmStoreyGetRequest {
  // 合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 1;
  // 平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 2;
  // 门店财务编码
  string finance_code = 3;
}

message ElmStoreyGetResponse{
  int32 Errno = 1;
  string Error = 2;
  ElmStoreyGetData data=3;
}

message ElmStoreyGetData{
  //合作方门店ID（商家系统门店ID）
  string shop_id = 1;
  //供应商ID
  string supplier_id = 2;
  //供应商名称
  string supplier_name = 3;
  //商户名称
  string name = 4;
  //商户图片
  string shop_logo = 5;
  //商户所在省
  string province = 6;
  //商户所在市
  string city = 7;
  //商户所在区县
  string county = 8;
  //商户地址
  string address = 9;
  //商户电话,座机必须填写区号
  string phone = 10;
  //客服电话
  string service_phone = 11;
  //订单提醒电话
  string ivr_phone = 12;
  //经度
  double longitude = 13;
  //纬度
  double latitude = 14;
  //新版营业时间，如果开始时间大于结束时间则表示跨天
  StoreBusinessTimeVO business_time2 = 15;
  //是否可以提供发票，枚举值（1:是；0:否）
  string invoice_support = 16;
  //餐盒费，单位：分
  int32 package_box_price = 17;
  //3731 兜底服务包; 794 代理商新零售E基础KA; 2947 代理商新零售E配送 4.0版; 2955 代理商新零售E配送KA4.0版; 2963 代理商新零售E配送KA固定-新; 2971 代理商新零售E快送 4.0版; 4019 代理商新零售E配送4.0X+Y; 4163 代理商新零售E聚合散店4.0; 4187 代理商新零售E基础选推; 4 星火计划; 2811零售即配-专送; 2819 零售即配-快送; 2827 零售即配-混合送; 2851 零售众包
  int32 delivery_party = 18;
  //商家预计拣货时长，单位:分钟
  int32 pick_time = 19;
  //平台门店ID
  string baidu_shop_id = 20;
  //经营品类,分类数组;参见下方data-categorys;
  repeated ShopGetCategorys categorys = 21;
  //坐标类型，枚举值（bdll：百度经纬度；amap：高德经纬度）
  string coord_type = 22;
  //门店客服电话
  repeated string service_phones = 23;
  //订单推送状态是否推送，枚举值（1 是；0 否）
  int32 order_status_push = 24;
  //最小下单金额
  string min_order_price = 25;
  //订单创单是否推送，枚举值（1 是；0 否）
  int32 order_push = 26;
}

message ElmStoreySetRequest{
  //合作方门店ID（商家系统门店ID）
  string shop_id = 1;
  //平台门店ID，baidu_shop_id与shop_id必填一项
  string baidu_shop_id = 2;
  //商户名称
  string name = 3;
  //商户图片
  string shop_logo = 4;
  //商户电话,座机必须填写区号
  string phone = 5;
  //客服电话
  string service_phone = 6;
  //订单提醒电话
  string ivr_phone = 7;
  //经度
  string longitude = 8;
  //纬度
  string latitude = 9;
  //新版营业时间，如果开始时间大于结束时间则表示跨天
  StoreBusinessTimeVO business_time2 = 10;
  //是否可以提供发票，枚举值（1:是；0:否）
  string invoice_support = 11;
  //餐盒费，单位：分
  int32 package_box_price = 12;
  //商家预计拣货时长，单位:分钟
  int32 pick_time = 13;
  //门店客服电话
  repeated string service_phones = 14;
  //是否平台计算拣货时长，枚举值（ 1 是， 0 否）
  int32 shipment_platform_computing = 15;
  // 门店财务编码
  string finance_code = 16;
}

message ElmStoreySetResponse{
  //返回错误信息
  string error = 1;
  //返回错误码
  int32 errno = 2;
  //返回信息
  bool data = 3;
}

message StoreBusinessTimeVO{
  //日常营业时间列表
  repeated NormalBusinessTimeVO normal_business_time_list = 1;
  //特殊营业时间列表，目前仅支持设置一个
  repeated SpecialBusinessTimeVO special_business_time_list = 2;
}

message NormalBusinessTimeVO{
  BusinessHourVO business_hour = 1;
}

message BusinessHourVO {
  //归属的周，枚举值（1：星期一；2：星期二；3：星期三；4：星期四；5：星期五；6：星期六；7：星期日）
  repeated int32 weeks = 1;
  //营业类型，枚举值：（1:24小时；2:自定义；3:歇业）
  int32 type = 2;
  //营业小时区间，最多3个
  repeated HourRangeVO ranges = 3;
}

message HourRangeVO{
  //开始时间，格式 ：08:00
  string start_time = 1;
  //结束时间，格式 ：20:00
  string end_time = 2;
}

message SpecialBusinessTimeVO{
  //支持的日期段
  DateRangeVO date_range = 1;
  //特殊营业时间原因
  string reason = 2;
  //营业时间段
  BusinessHourVO business_hour = 3;
}

message DateRangeVO{
  //开始日期，格式：2022-05-01
  string start_date = 1;
  //结束日期，格式 ：2022-05-07
  string end_date = 2;
}

message ShopGetCategorys {
  //一级分类
  string category1 = 1;
  //二级分类
  string category2 = 2;
  //三级分类
  string category3 = 3;
}

message ElmBusStatusGetRequest{
  //合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 1;
  //平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 2;
  //来源平台，枚举值：1 表示饿了么；2 表示饿了么星选
  string platformFlag = 3;
  // 门店财务编码
  string finance_code = 4;
}

message ElmBusStatusGetResponse{
  //返回错误信息
  string error = 1;
  //返回错误码
  int32 errno = 2;
  //返回信息
  ShopBusstatusGetData data = 3;
  // 门店财务编码
  string finance_code = 4;
}

message ShopBusstatusGetData{
  //平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 1;
  //合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 2;
  //商户在C端的营业状态，饿了么app（1表示休息中 2表示可预订 3表示营业中 4表示暂停营业），饿了么星选app（1表示休息中 2表示可预订 3表示营业中 4表示暂停营业（目前4已废弃） 5表示必须跨天预订）
  int32 shop_busstatus = 3;
}

message ElmBusStatusSetRequest{
  //平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 1;
  //合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 2;
  //状态：1-休息中，3-营业中
  int32 status = 3;
  // 门店财务编码
  string finance_code = 4;
}

message ElmBusStatusSetResponse{
  //返回错误信息
  string error = 1;
  //返回错误码
  int32 errno = 2;
}

message ElmAnnouncementGetRequest{
  // 合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 1;
  // 平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 2;
  // 门店财务编码
  string finance_code = 3;
}

message ElmAnnouncementGetResponse{
  //返回错误信息
  string error = 1;
  //返回错误码
  int32 errno = 2;
  //返回信息
  ShopAnnouncementGetData data = 3;
}

message ShopAnnouncementGetData{
  //公告内容，展示到饿了么app门店页面
  string content = 1;
  //简介
  string description = 2;
}

message ElmAnnouncementSetRequest{
  //平台门店ID，shop_id和baidu_shop_id必填一项
  string baidu_shop_id = 1;
  //合作方门店ID（商家系统门店ID），shop_id和baidu_shop_id必填一项
  string shop_id = 2;
  //简介，仅饿了么前端展示使用
  string descritption = 3;
  //内容，饿了么app端展示公告信息
  string content = 4;
  // 门店财务编码
  string finance_code = 5;
}

message ElmAnnouncementSetResponse{
  //返回错误信息
  string error = 1;
  //返回错误码
  int32 errno = 2;
}

message Coordinate {
  string latitude = 1;
  string longitude = 2;
}

message DistanceFeeStage {
  string fee = 1;
  string start = 2;
  string end = 3;
}

message DistanceExtFee {
  repeated DistanceFeeStage distance_fee_stages = 1;
}

message DistancePriceStage {
  string price = 1;
  string start = 2;
  string end = 3;
}

message DistanceExtPrice {
  repeated DistancePriceStage distance_price_stages = 1;
}

message WeightFeeStage {
  int32 start_weight = 1;
  int32 unit = 2;
  int32 end_weight = 3;
  string fee = 4;
}

message WeightExtFee {
  repeated WeightFeeStage weight_fee_stage_list = 1;
}

message TimeStageFee {
  string start_time = 1;
  string fee = 2;
  string end_time = 3;
}

message TimeExtFee {
  repeated TimeStageFee time_stage_fee_stage_list = 1;
}

message TimePriceStage {
  string start_time = 1;
  string price = 2;
  string end_time = 3;
}

message TimeExtPrice {
  repeated TimePriceStage time_price_stage_list = 1;
}

message MultiPeriod {
  string start = 1;
  string end = 2;
}

message DeliveryArea {
  repeated Coordinate coordinates = 1;
  DistanceExtFee distance_ext_fee = 2;
  int32 max_weight = 3;
  DistanceExtPrice distance_ext_price = 4;
  int32 area_type = 5;
  string agent_fee = 6;
  WeightExtFee weight_ext_fee = 7;
  string uuid = 8;
  TimeExtFee time_ext_fee = 9;
  TimeExtPrice time_ext_price = 10;
  string delivery_price = 11;
  repeated MultiPeriod multi_period = 12;
}

message DataEntry {
  repeated DeliveryArea delivery_areas = 1;
  int32 product_id = 2;
}

message ElmDeliveryInfoResBody {
  int32 errno = 1;
  repeated DataEntry data = 2;
  string error = 3;
}

message ElmDeliveryInfoRes {
  ElmDeliveryInfoResBody body = 1;
  //状态码
  int32 code = 2;
  //消息
  string message = 3;
}

//批量获取门店详细请求参数
message ElmStoreUpdateRequest {
  //平台门店ID
  string baidu_shop_id = 1;
  //门店ID
  string shop_id = 2;
  // 标品id，请通过
  string product_id = 3;
  repeated DeliveryArea delivery_areas = 4;
  // 门店财务编码
  string finance_code = 5;
}