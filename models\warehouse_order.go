package models

type WarehouseOrder struct {
	Id            int    `xorm:"not null pk autoincr comment('自增') INT(11)"`
	OrderId       string `xorm:"default 'NULL' comment('订单Id') VARCHAR(50)"`
	WarehouseId   int    `xorm:"default NULL comment('对应仓库Id') INT(11)"`
	GoodsId       string `xorm:"default 'NULL' comment('商品Id') VARCHAR(50)"`
	Quantity      int    `xorm:"default NULL comment('商品数量') INT(11)"`
	Thirdid       string `xorm:"default 'NULL' comment('第三方仓库id 例如a8id,管易id') VARCHAR(50)"`
	WarehouseCode string `xorm:"default 'NULL' comment('第三方仓库code 例如a8code,管易code') VARCHAR(50)"`
}
