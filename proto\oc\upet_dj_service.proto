syntax = "proto3";

package oc;


// 服务接口定义
service UpetDjService{
  // 获取省市区列表
  rpc QuerySectionList(SectionQueryRequest) returns (SectionQueryResponse);
  // 根据配送区域计算运费
//  rpc CalcUpetDjMoney(UpetDjMoneyCalcRequest) returns(UpetDjMoneyResponse);
  // 修改订单状态，确认收货
  rpc ConfirmUpetDj(UpetDjConfirmRequest) returns (UpetDjConfirmResponse);
  // 查询阿闻到家订单列表
  rpc QueryUpetDjOrderList(UpetDjOrderQueryRequest) returns(UpetDjOrderQueryResponse);
  // 查询阿闻到家订单详情
  rpc QueryUpetDjOrderDetail(UpetDjOrderDetailRequest) returns(UpetDjOrderDetailResponse);
  // 查询阿闻到家订单是否需要自动打印
  rpc QueryUpetDjOrderIsAutoPrint(UpetDjOrderIsAutoPrintQueryRequest) returns(UpetDjOrderIsAutoPrintQueryResponse);
  // 查询促销活动的订单统计信息
  rpc QueryPromotonOrderReport(PromotonOrderReportRequest) returns(PromotionOrderReportReponse);
}

// 加载省市区列表数据请求
message SectionQueryRequest {
  // 上级ID
  int32 parentId = 1;
  // 是否加载子区域
  bool isChild = 2;
};

// 省市区 数据模型
message SectionDto {
  // 区域Id
  int32 id = 2;
  // 上级部门Id
  int32 parentId = 1;
  // 名称
  string sectionName = 3;
  // 子区域
  repeated SectionDto childSectionDto = 4;
};

// 加载省市区列表数据响应
message SectionQueryResponse {
  // 代码 非 200 取 message 错误信息
  int32 code = 2;
  // 省市列表
  repeated SectionDto data = 1;
};

// 根据配送区域计算运费数据请求
message UpetDjMoneyCalcRequest{
    // 店铺Id
    string shopId=1;
    //目的地坐标X
    double destinationX=3;
    //目的地坐标Y
    double destinationY=4;
    // 总重量，换算成克
    int32 totalWeight=5;
    // 商品sku列表
    repeated UpetDjMoneyCalcProductDto products=6;
};
// 商品信息
message UpetDjMoneyCalcProductDto {
    // 商品数量
    int32 count=1;
    // 商品Sku
    string skuId=2;
}

// 根据配送区域计算运费数据Dto
message UpetDjMoneyDto {
    // 运费金额
    float upetDjMoney=1;
    // 时间
    float upetDjSeconds=2;
    // 总重量
    int32 totalWeight=3;
    // 运费金额,以分为单位
    int32 upetDjMoneyByMinUnit=5;
};

// 根据配送区域计算运费数据响应
message UpetDjMoneyResponse{
    // 代码 非 200 取 message 错误信息
    int32 code=2;
    // 错误信息
    string message=3;
    // 运费数据 Dto
    UpetDjMoneyDto dto=1;
};

// 确认收货数据请求
message UpetDjConfirmRequest{
    // 订单Id
    string orderId=1;
}

// 确认收货数据相应
message UpetDjConfirmResponse{
  // 代码 非 200 取 message 错误信息
  int32 code = 1;
  // 错误信息
  string message = 2;
}

// 400 响应内容
message BadRequestResponse {
  // 代码 非 200 取 message 错误信息
  int32 code = 1;
  // 错误消息
  string message = 2;
}

// 阿闻到家订单查询
message UpetDjOrderQueryRequest{
  // 页索引
  int32 pageIndex = 1;
  // 页大小
  int32 pageSize = 2;
  // 店铺Id
  repeated string shopId = 3;
  // 登录会员Id
  string memberId = 4;
  //订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 30 已完成
  UpetDjOrderState state = 5;
  //订单号
  string orderSn = 6;
}

// 根据订单Id查询到家订单详情
message UpetDjOrderDetailRequest {
    // 订单Id
    string orderId=1;
    // 订单号码 orderId 与 orderSn任选其一
    string orderSn=2;
    string member_id = 3;
}

// 阿闻订单数据模型
message UpetDjOrderDto{
  // 订单Id
  string orderId = 1;
  // 订单号码
  string orderNo = 2;
  // 订单号码
  string parent_order_sn = 3;
  //店铺Id
  string shopId = 4;
  //店铺名字
  string shopName = 5;
  //店铺联系电话
  string shopMobile = 6;
  //订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 27 商家手动已接单 28 备货完成, 待买家自提 29 商家已接单, 备货中 30 已完成, 31拆单中, 32拆单失败, 33未核销, 34待骑手接单，50已发货
  UpetDjOrderState state = 7;
  // 订单总金额
  float totalMoney = 8;
  // 优惠金额
  double privilege = 9;
  // 下单日期
  string createDateTime = 10;
  // 支付方式 0 待支付 1支付宝  2微信 3美团 4其他
  int32 payMode = 11;
  //有售后记录 0 无 11 整单退款 12 整单退货 21 部分退款 22 部分退货
  int32 returnWay = 12;
  // 最近一次售后记录
  string returnNo = 13;
  // 订单备注
  string remarks = 14;
  // 订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
  int32 returnState = 15;
  // 店铺营业时间段
  string business_times = 16;
  // 取货码
  string pickup_code = 17;
  // 店铺地址
  string shop_address = 18;
  // 店铺备货时长
  int32 stock_up_time = 19;
  // 是否显示退款按钮
  bool is_apply_btn = 20;
  // 是否是虚拟订单，0否1是
  int32 is_virtual = 21;
  // 是否可以取消订单
  bool enable_cancel = 24;
  // 阿闻订单商品列表
  repeated UpetDjOrderProductDto productList = 22;
  // 阿闻订单子订单列表
  repeated UpetDjOrderDto childOrderList = 23;

  // 阿闻宠团团这个门店（财务编码：QZC00300）的所有订单，在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款
  // 0默认显示 1不显示
  int32 no_display_after_payment = 25;


  int32 is_pay = 26;

  message GroupInfo {
      // 社区团购活动状态 0开团中 1拼团成功 2拼团失败
      int32 group_status = 1;
      // 收件人
      string receiver_name = 2;
      // 收件省
      string receiver_state = 3;
      // 收件市
      string receiver_city = 4;
      // 收件区
      string receiver_district = 5;
      // 收件地址
      string receiver_address = 6;
      // 收件手机
      string receiver_mobile = 7;
      // 是否团长 1是 0否
      int32 group_leader = 8;
      // 团员信息 名字
      string group_name = 9;
      // 团员信息 手机
      string group_mobile = 10;
      // 团长代收状态 0不代收 1代收
      int32 final_take_type = 11;
      // 团ID
      int32 group_id = 12;
      // 最晚N日内送达
      string deliver_days = 13;
      // 团员信息 地址
      string group_address = 14;
  }
  GroupInfo group_info = 27;
  //渠道id（datacenter.platform_channel表）,订单来源 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗
  int32 channel_id = 28;
  // 处方单号
  string consult_order_sn = 29;
}


// 配送模型
message UpetDjDeliveryDto{
    //收货人名称
    string receivername=1;
    //收货省
    string receiverstate=2;
    //收货市
    string receivercity=3;
    //收货区
    string receiverdistrict=4;
    //收货地址
    string receiveraddress=5;
    //收货电话
    string receiverphone=6;
    //收货人手机号
    string receivermobile=7;
    //配送费
    double freight=8;
    // 送达时间
    string deliveryTime=9;
    // 配送方式 1 快递 2 外卖 3 自提 4 同城
    int32 deliveryType=10;
    //配送方式名称
    string deliveryTypeName = 21;
    // 配送标识
    int64 deliveryId=15;
    // 配送单号
    string deliveryOrderId=16;
    // 配送服务代码
    string deliveryServiceCode=18;
    // 收货地址维度
    double latitude=11;
    // 收货地址经度
    double longitude=12;
    // 店铺维度
    double shopLatitude=13;
    // 店铺经度
    double shopLongitude=14;
    //提货点名称
    string pickupStationName = 19;
    //提货点地址
    string pickupStationAddress = 20;
    // 配送节点信息
    repeated UpetDjDeliverNodesDto nodes=17;
    // 订单发货记录
    repeated OrderExpress express = 22;
    // 配送标识字符串类型
    string deliveryIdStr=23;

    message OrderExpress {
        //快递单号
        string express_no = 1;
        //商品数量
        int32 num = 2;
        //快递名称
        string express_name = 3;
    }
}

// 退款单汇总信息
message UpetDjRefundDto{
    // 最近退款单号
    string refundNo=1;
    // 总退款金额
    double refundMoney=2;
    // 退款单状态 1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
    int32 refundState=3;
}
// 退款商品列表
message UpetDjRefundProductDto{
    // 退款商品Id
    string productId=1;
    // 最终退款数量
    int32 refundCount=2;
}

// 配送节点信息
message UpetDjDeliverNodesDto{
    // 节点说明
    string message=1;
    // 节点日期
    string createTime=2;
}

// 阿闻订单状态
enum UpetDjOrderState {
    // 查询用，代表所有订单
    all=0;
    // 未付款
    unPay=10;
    // 未付款取消
    unPayCancel=11;
    //已付款
    payed=20;
    // 已付款取消
    payedCancel=21;
    // 店铺待接单
    payedWaitShopReceive=22;
    // 店铺已接单
    payedShopReceived=23;
    // 骑手已接单
    deliveried=24;
    // 骑手配送中
    delivering=25;
    // 商家配送中
    shipping=26;
    // 商家手动已接单
    payedShopReceivedManual=27;
    // 备货完成, 待买家自提
    buyerSelfCollection = 28;
    // 商家已接单, 备货中
    payedShopReceivedPicking = 29;
    // 已完成
    finished=30;
    // 拆单中
    splitIng = 31;
    // 拆单失败
    splitFail = 32;
    // 未核销
    unVerify = 33;
    // 待骑手接单
    waitDeliveried = 34;
    // 退款中
    refunding = 41;
    // 退款关闭
    refundClosed = 42;
    // 退款成功
    refundSuccess = 43;
    // 退款初审通过
    refundFirstPass = 46;
    // 退款终审通过
    refundFinalPass = 47;
    // 退款失败
    refundFail = 48;
    // 撤销退款
    refundUndo = 49;
    // 待发货
    expressWaitingDelivery = 50;
    // 已发货
    expressShipped = 51;
};

// 阿闻订单商品数据模型
message UpetDjOrderProductDto{
    //商品Id
    string productId=1;
    // 商品名称
    string productName=2;
    // 商品店铺单价
    double productPrice=3;
    // 商品实际单价
    double productActualPrice=4;
    // 数量
    int32 productCount=5;
    // 商品Logo
    string productPic=6;
    // 规格
    string productSpecifica=7;
    // 总实收
    double productActaulMoney=8;
    // 分类
    string productCategoryId=9;
    // 参与限时折扣的活动id
    int32 promotionId=10;
    // 商品类别1-实物商品2-虚拟商品3-组合商品
    int32 productType=11;
    //核销码列表
    repeated UpetDjProductVerifyCodeDto verifyCodeList=12;
    //skuid
    string skuId=13;
    //上级skuid
    string parentSkuId=14;
    //上级productid
    string parentProductId=15;
    // 是否处方药
    int32 is_prescribed_drug = 17;
    //子商品列表
    repeated UpetDjOrderProductDto childProductList=16;
}

// 阿闻订单虚拟商品核销码模型
message UpetDjProductVerifyCodeDto{
    // 核销Id
    int64 id=1;
    // 商品Id
    string productId=2;
    // 所属子订单号
    string orderSn=3;
    // 核销码
    string verifyCode=4;
    // 核销码有效期
    string verifyCodeExpiryDate=5;
    // 核销码状态 0未核销，1已核销，2已退款
    int32 verifyStatus=6;
    // 核销时间
    string verifyTime=7;
    // 核销地点财务编码
    string verifyShop=8;
    // 创建时间
    string createTime=9;
    // 最后更新时间
    string updateTime=10;
}

// 阿闻到家订单查询数据响应
message UpetDjOrderQueryResponse{
    // 代码 非 200 取 message 错误信息
    int32 code=3;
    //总订单数据
    int64 total=1;
    // 当前页数据
    repeated UpetDjOrderDto data=2;
    // 是否可以加载更多
    bool hasMore=4;
}

// 阿闻到家订单查询明细数据响应
message UpetDjOrderDetailResponse {
    // 代码 非 200 取 message 错误信息
    int32 code=1;
    // 错误信息
    string message=2;
    // 剩余秒数
    int32 remainSeconds=7;
    // 订单信息
    UpetDjOrderDto orderListInfo=3;
    // 配送信息
    UpetDjDeliveryDto deliveryInfo=4;
    // 退款信息
    UpetDjRefundDto refundInfo=5;
    // 退款商品列表
    repeated UpetDjRefundProductDto refundProductList=6;
}

// 查询订单是否需要自动打印请求
message UpetDjOrderIsAutoPrintQueryRequest {
    // 订单号码
    string orderSn=1;
}

// 查询订单是否需要自动打印响应
message UpetDjOrderIsAutoPrintQueryResponse {
    // 代码 非 200 取 message 错误信息
    int32 code=1;
    // 错误信息
    string message=2;
    // 是否自动打印
    bool isAutoPrint=3;
}

// 促销活动的报表查询
message PromotonOrderReportRequest {
    // 促销活动Id
    int32 promotionId=1;
    // 统计开始日期
    string startDate=2;
    // 统计截止日期
    string endDate=3;
}

// 促销活动的报表信息
message PromotionOrderReportReponse {
    //昨天订单数量
    int32 orderCountYesterday=1;
    //昨天订单流水
    double orderMoneyYesterday=2;
    // 全部活动订单数量
    int64 orderCountTotal=3;
    // 全部活动订单流水
    double orderMoneyTotal=4;

    //订单列表
    repeated PromotionOrderReportWithDateListDto orderList=9;
}

// 查询当前用户的每天的活动订单信息
message PromotionOrderReportWithDateListDto {
    // 统计日期
    string calcDate=1;
    // 订单数量
    int32 orderCount=2;
    // 订单流水
    double orderMoney=3;
}
