syntax = "proto3";

package mk;

import "mk/promotion.proto";
import "mk/reachReduce_message.proto";
import "mk/timeDiscount_message.proto";
import "mk/reduceDelivery_message.proto";
import "mk/promotion_configuration.proto";
import "mk/promotiontask_message.proto";
import "mk/market.proto";

service PromotionService {
    // 活动概览
    rpc Summary(promotionSummaryReq) returns(promotionSummaryRes);
    // 查询活动详情
    rpc QueryPromotionDetailById (queryByIdRequest) returns (promotionDetailResponse);
    // 根据店铺Id查询活动详情
    rpc QueryPromotionByShopId (QueryPromotionByShopIdReq) returns (promotionQueryByShopIdResponse);
    // 通过skuids 查询促销活动信息
    rpc QueryPromotionBySkuIds (promotionQueryBySkuIdsRequest) returns (promotionQueryBySkuIdsResponse);
    // 计算减免金额
    rpc CalcPromotionMoney (promotionCalcRequest) returns (promotionCalcResponse);
    // 通过shopId 查询符合促销活动的商品skuid  -新
    rpc QueryPromotionSkuByShopNew (promotionQueryByShopIdRequest) returns (promotionQueryBySkuIdsResponse);
    //海报管理-创建海报
    rpc CreatePlayBill (CreatePlayBillRequest) returns (CreatePlayBillResponse);
    //海报管理-多门店列表
    rpc GetPlayBillList (GetPlayBillListRequest) returns (GetPlayBillListResponse);
    //海报管理-单门店门店列表
    rpc GetSinglePlayBillList (GetSinglePlayBillListRequest) returns (GetSinglePlayBillListResponse);
    //海报管理-单门店门海报删除
    rpc DeleteSinglePlayBill (DeleteSinglePlayBillRequest) returns (baseResponse);
    //海报管理-下载海报
    rpc DownloadPlayBill (DownloadPlayBillRequest) returns (DownloadPlayBillResponse);
    //活动操作日志列表
    rpc GetPromotionOperateLogList (GetPromotionOperateLogListRequest) returns (GetPromotionOperateLogListResponse);
    //活动操作日志保存
    rpc StorePromotionOperateLog (StorePromotionOperateLogRequest) returns (baseResponse);
}

// 满减优惠
service ReachReduceService {
    // 添加
    rpc Add (reachReduceAddRequest) returns (baseResponse);
    // 更新
    rpc Update (reachReduceUpdateRequest) returns (baseResponse);
    // 更新是否启用
    rpc UpdateIsEnable (updateIsEnableRequest) returns (baseResponse);
    // 删除多个店铺
    rpc DeletePromotionShopById (deletePromotonShopByIds) returns (baseResponse);
    // 根据查询条件删除活动与店铺关联关系
    rpc DeletePromotionShopByQuery (reachReducePromotionShopQuery) returns (baseResponse);

    // 根据查询条件查询活动列表
    rpc QueryPromotionShopByShopIds (reachReducePromotionShopQuery) returns (reachReducePromotionShopResponse);
    // 根据满减优惠Id查询相信信息
    rpc QueryById (queryByIdRequest) returns (reachReduceByIdResponse);
    // 根据活动Id查询参与商品
    rpc QueryPromotionProduct(reachReduceProductQueryRequest) returns(reachReduceProductQueryResponse);
    // 查询可以选择的商品列表(编辑满减活动时用来选择包含商品)
    rpc QueryOptionalSelectPromotionProduct(reachReduceProductQueryRequest) returns(reachReduceProductQueryResponse);
}

// 限时折扣
service TimeDiscountService {
    // 添加
    rpc Add (promotionTimeDiscountAddRequest) returns (baseResponse);
    // 修改
    rpc Update (timeDiscountUpdateRequest) returns (baseResponse);
    // 根据ID删除限时折扣与商品的关联关系
    rpc DeletePromotionProducById (deleteRequest) returns (baseResponse);
    // 删除限时折扣与商品的关联关系
    rpc DeletePromotionProducByQuery (timeDiscountProductRequest) returns (baseResponse);

    // 查询限时折扣与商品的关联关系
    rpc QueryPromotionProducByQuery (timeDiscountProductRequest) returns (timeDiscountProductResponse);
    // 根据限时折扣Id查询相信信息
    rpc QueryById (queryByIdRequest) returns (timeDiscountByIdResponse);

    // 根据活动Id和sku查询限时折扣活动的单日库存
    rpc QueryLimitCountBySkus (queryLimitCountRequest) returns (queryLimitCountResponse);

    //全局配置
    rpc GetPromotionConfigurationDetail (GetPromotionConfigurationDetailRequest) returns (GetPromotionConfigurationDetailResponse); //获取课程详情
    rpc PromotionConfigurationCreateInfo (PromotionConfigurationCreateInfoRequest) returns (PromotionConfigurationCreateInfoResponse); //创建课程信息
    rpc PromotionConfigurationUpdateInfo (PromotionConfigurationUpdateInfoRequest) returns (PromotionConfigurationUpdateInfoResponse);
    rpc PromotionConfigurationReplaceInfo (PromotionConfigurationReplaceInfoRequest) returns (PromotionConfigurationReplaceInfoResponse); //更新课程信息

}

// 满减运费
service ReduceDeliveryService {
    // 添加
    rpc Add (reduceDeliveryAddRequest) returns (baseResponse);
    // 修改
    rpc Update (reduceDeliveryUpdateRequest) returns (baseResponse);
    // 启用与撤销满减运费活动
    rpc UpdateIsEnable (updateIsEnableRequest) returns (baseResponse);
    // 根据ID删除单个满减运费与店铺的关联关系
    rpc DeletePromotionShopById (deleteRequest) returns (baseResponse);
    // 删除满减运费与店铺的关联关系
    rpc DeletePromotionShopByQuery (reduceDeliveryShopRequest) returns (baseResponse);

    // 查询满减运费与店铺的关联关系
    rpc QueryPromotionShopByQuery (reduceDeliveryShopRequest) returns (reduceDeliveryShopResponse);
    // 根据满减运费Id查询相信信息
    rpc QueryById (queryByIdRequest) returns (reduceDeliveryByIdResponse);
}

// 活动创建任务
service PromotionTaskService {
    // 查询活动任务创建列表
    rpc QueryPromotionTask(promotionTaskQueryRequest) returns(promotionTaskQueryResponse);
    // 根据Id 查询任务明细
    rpc QueryPromotionTaskById(queryByIdRequest) returns(promotionTaskByIdQueryResponse);
    // 根据活动Id查询明细
    rpc QueryPromotionTaskDetail(promotionTaskDetailQueryRequest) returns(promotionTaskDetailQueryResponse); 
}

//营销中心活动
service MarketService{
    // 肋力活动列表
    rpc QueryActivityIds(activityIdsRequest) returns(activityIdsResponse);
    // 肋力活动删除
    rpc DelActivity(IdRequest) returns(baseResponse);
    // 肋力活动状态更新
    rpc StatusFreshActivity (statusFreshActivityRequest) returns (baseResponse);
    // 肋力活动新增/编辑
    rpc NewActivity (newActivityRequest) returns (baseResponse);
    // 肋力活动配置查询
    rpc QuerySettingActivity (querySettingActivityRequest) returns (settingActivityResponse);
    // 肋力活动配置保存
    rpc NewSettingActivity(settingActivityRequest) returns(baseResponse);
    //流浪救助参与用户列表
    rpc SuccourUser(userListRequest) returns(userListResponse);
    //流浪救助参与用户编辑
    rpc SuccourEditUser(userRequest) returns(userResponse);
    //二维码列表
    rpc SuccourQrcode(qrcodeRequest) returns(qrcodeResponse);
    //二维码新增
    rpc SuccourQrcodeAdd(qrcodeAddRequest) returns(qrcodeAddResponse);
//二维码新增
    rpc SuccourQrcodeSet(qrcodeSetRequest) returns(qrcodeSetResponse);

     //救助站列表
     rpc SuccourSalvation(salvationRequest) returns(salvationResponse);
      //新增，编辑
      rpc SuccourSalvationAdd(salvationAddRequest) returns(salvationAddResponse);

     //设置停止
     rpc SuccourSalvationSet(salvationSetRequest) returns(salvationSetResponse);
      //查看凭证
     rpc SuccourSalvationReceiving(salvationReceivingRequest) returns(salvationReceivingResponse);

     //上传凭证
     rpc SuccourSalvationReceivingAdd(salvationReceivingAddRequest) returns(salvationReceivingAddResponse);

     //设置捐赠限制
     rpc SuccourDonateSet(donateSetRequest) returns(donateSetResponse);

     //访问限制
     rpc SuccourQrcodeLimit(qrcodeLimitRequest) returns(qrcodeLimitResponse);
     //修改访问限制
     rpc SuccourQrcodeLimitEdit(qrcodeLimitEditRequest) returns(qrcodeLimitEditResponse);

    // 优惠券活动新增/编辑
    rpc CouponActivityNew (CouponActivityNewRequest) returns (baseResponse);
    // 优惠券列表
    rpc CouponActivityList (couponListRequest) returns (couponListResponse);
    // 终止优惠券活动
    rpc EndCouponActivity (IdRequest) returns (baseResponse);
    // 领取优惠券列表
    rpc VoucherList (VoucherListRequest) returns (VoucherListResponse);
    // 领取优惠券列表导出
    rpc VoucherListExport (VoucherListRequest) returns (VoucherListResponse);
    // 增加优惠券领取数量
    rpc AddCouponCount (IdRequest) returns (baseResponse);
}