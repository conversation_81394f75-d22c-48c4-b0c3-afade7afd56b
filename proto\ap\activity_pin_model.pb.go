// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ap/activity_pin_model.proto

package ap

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

//通用返回
type BaseResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string   `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{0}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BaseResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type CancelPinOrderResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//下单时间(13位，毫秒)
	CreateTime           int64    `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPinOrderResponse) Reset()         { *m = CancelPinOrderResponse{} }
func (m *CancelPinOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CancelPinOrderResponse) ProtoMessage()    {}
func (*CancelPinOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{1}
}

func (m *CancelPinOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPinOrderResponse.Unmarshal(m, b)
}
func (m *CancelPinOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPinOrderResponse.Marshal(b, m, deterministic)
}
func (m *CancelPinOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPinOrderResponse.Merge(m, src)
}
func (m *CancelPinOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CancelPinOrderResponse.Size(m)
}
func (m *CancelPinOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPinOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPinOrderResponse proto.InternalMessageInfo

func (m *CancelPinOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CancelPinOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CancelPinOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CancelPinOrderResponse) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

//订单支付通知
type OrderPayNotifyRequest struct {
	//订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	//支付单号
	PaySn string `protobuf:"bytes,2,opt,name=pay_sn,json=paySn,proto3" json:"pay_sn"`
	//支付类型1支付宝  2微信 3美团 4其他
	PayMode int32 `protobuf:"varint,3,opt,name=pay_mode,json=payMode,proto3" json:"pay_mode"`
	//支付时间
	PayTime string `protobuf:"bytes,4,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	//实际支付金额
	PayAmount            int32    `protobuf:"varint,5,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderPayNotifyRequest) Reset()         { *m = OrderPayNotifyRequest{} }
func (m *OrderPayNotifyRequest) String() string { return proto.CompactTextString(m) }
func (*OrderPayNotifyRequest) ProtoMessage()    {}
func (*OrderPayNotifyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{2}
}

func (m *OrderPayNotifyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPayNotifyRequest.Unmarshal(m, b)
}
func (m *OrderPayNotifyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPayNotifyRequest.Marshal(b, m, deterministic)
}
func (m *OrderPayNotifyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPayNotifyRequest.Merge(m, src)
}
func (m *OrderPayNotifyRequest) XXX_Size() int {
	return xxx_messageInfo_OrderPayNotifyRequest.Size(m)
}
func (m *OrderPayNotifyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPayNotifyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPayNotifyRequest proto.InternalMessageInfo

func (m *OrderPayNotifyRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderPayNotifyRequest) GetPaySn() string {
	if m != nil {
		return m.PaySn
	}
	return ""
}

func (m *OrderPayNotifyRequest) GetPayMode() int32 {
	if m != nil {
		return m.PayMode
	}
	return 0
}

func (m *OrderPayNotifyRequest) GetPayTime() string {
	if m != nil {
		return m.PayTime
	}
	return ""
}

func (m *OrderPayNotifyRequest) GetPayAmount() int32 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

type GetGroupListRequest struct {
	//用户小程序ID
	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//用户ID
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//商品SKUid
	SkuId string `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//当前页码
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页行数
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupListRequest) Reset()         { *m = GetGroupListRequest{} }
func (m *GetGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupListRequest) ProtoMessage()    {}
func (*GetGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{3}
}

func (m *GetGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListRequest.Unmarshal(m, b)
}
func (m *GetGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListRequest.Merge(m, src)
}
func (m *GetGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupListRequest.Size(m)
}
func (m *GetGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListRequest proto.InternalMessageInfo

func (m *GetGroupListRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetGroupListRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetGroupListRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GetGroupListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetGroupListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGroupListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error      string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	TotalCount int32  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	//拼团明细
	Data                 []*GroupDetail `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGroupListResponse) Reset()         { *m = GetGroupListResponse{} }
func (m *GetGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupListResponse) ProtoMessage()    {}
func (*GetGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{4}
}

func (m *GetGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListResponse.Unmarshal(m, b)
}
func (m *GetGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListResponse.Merge(m, src)
}
func (m *GetGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupListResponse.Size(m)
}
func (m *GetGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListResponse proto.InternalMessageInfo

func (m *GetGroupListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetGroupListResponse) GetData() []*GroupDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetGroupDetailRequest struct {
	//拼团订单号
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	// 用户ID
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 是否是分享界面过来的
	IsShare              int32    `protobuf:"varint,3,opt,name=is_share,json=isShare,proto3" json:"is_share"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupDetailRequest) Reset()         { *m = GetGroupDetailRequest{} }
func (m *GetGroupDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupDetailRequest) ProtoMessage()    {}
func (*GetGroupDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{5}
}

func (m *GetGroupDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupDetailRequest.Unmarshal(m, b)
}
func (m *GetGroupDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupDetailRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupDetailRequest.Merge(m, src)
}
func (m *GetGroupDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupDetailRequest.Size(m)
}
func (m *GetGroupDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupDetailRequest proto.InternalMessageInfo

func (m *GetGroupDetailRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *GetGroupDetailRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetGroupDetailRequest) GetIsShare() int32 {
	if m != nil {
		return m.IsShare
	}
	return 0
}

type GetGroupDetailResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//拼团明细
	Data                 *GroupDetail `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupDetailResponse) Reset()         { *m = GetGroupDetailResponse{} }
func (m *GetGroupDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupDetailResponse) ProtoMessage()    {}
func (*GetGroupDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{6}
}

func (m *GetGroupDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupDetailResponse.Unmarshal(m, b)
}
func (m *GetGroupDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupDetailResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupDetailResponse.Merge(m, src)
}
func (m *GetGroupDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupDetailResponse.Size(m)
}
func (m *GetGroupDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupDetailResponse proto.InternalMessageInfo

func (m *GetGroupDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupDetailResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupDetailResponse) GetData() *GroupDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupDetail struct {
	//拼团订单号
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//商品skuid
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//拼团状态 0已取消 10未支付 20拼团进行中 30拼团成功 40拼团失败
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	//开始时间
	StartTime string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//创建时间
	CreateTime string `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//参团人
	GroupMembers []*GroupMembers `protobuf:"bytes,7,rep,name=group_members,json=groupMembers,proto3" json:"group_members"`
	//最大参与人数
	MaxParticipantNumber int32 `protobuf:"varint,8,opt,name=max_participant_number,json=maxParticipantNumber,proto3" json:"max_participant_number"`
	//已参与人数
	ParticipantNumber int32 `protobuf:"varint,9,opt,name=participant_number,json=participantNumber,proto3" json:"participant_number"`
	// 商品名称
	ProductName string `protobuf:"bytes,10,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品图片
	ProductImage string `protobuf:"bytes,11,opt,name=product_image,json=productImage,proto3" json:"product_image"`
	// 规格名称
	SpecName string `protobuf:"bytes,12,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	// 拼团价
	PinPrice int64 `protobuf:"varint,13,opt,name=pin_price,json=pinPrice,proto3" json:"pin_price"`
	// 商品原价
	MarkingPrice int64 `protobuf:"varint,14,opt,name=marking_price,json=markingPrice,proto3" json:"marking_price"`
	// 是否团长 1是 0 否
	IsPinHead int32 `protobuf:"varint,15,opt,name=is_pin_head,json=isPinHead,proto3" json:"is_pin_head"`
	// 用户名
	OpenName string `protobuf:"bytes,16,opt,name=open_name,json=openName,proto3" json:"open_name"`
	// 头像
	Portrait string `protobuf:"bytes,17,opt,name=portrait,proto3" json:"portrait"`
	// 团长订单号
	PinHeadOrderSn string `protobuf:"bytes,18,opt,name=pin_head_order_sn,json=pinHeadOrderSn,proto3" json:"pin_head_order_sn"`
	// 团长头像
	PinHeadPortrait string `protobuf:"bytes,19,opt,name=pin_head_portrait,json=pinHeadPortrait,proto3" json:"pin_head_portrait"`
	// 是否参团 0 否 1 是
	IsAddGroup int32 `protobuf:"varint,20,opt,name=is_add_group,json=isAddGroup,proto3" json:"is_add_group"`
	//是否虚拟订单 0否 1是
	IsVirtual int32 `protobuf:"varint,21,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	// 拼团活动ID
	GroupBuyId int32 `protobuf:"varint,22,opt,name=group_buy_id,json=groupBuyId,proto3" json:"group_buy_id"`
	// 是否发券 0默认 1已发优惠券
	IsCoupon int32 `protobuf:"varint,23,opt,name=is_coupon,json=isCoupon,proto3" json:"is_coupon"`
	// 当前时间
	NowTime string `protobuf:"bytes,24,opt,name=now_time,json=nowTime,proto3" json:"now_time"`
	// 分享卡片图片地址
	ShareImgUrl string `protobuf:"bytes,25,opt,name=share_img_url,json=shareImgUrl,proto3" json:"share_img_url"`
	// 拼团成功送的优惠券ID
	SuccessCoupon string `protobuf:"bytes,26,opt,name=success_coupon,json=successCoupon,proto3" json:"success_coupon"`
	//团状态：0拼主已创建但未支付 10拼团进行中 20 拼团成功 30 拼团失败
	GroupStatus int32 `protobuf:"varint,27,opt,name=group_status,json=groupStatus,proto3" json:"group_status"`
	//活动id
	PinGroupId int32 `protobuf:"varint,28,opt,name=pin_group_id,json=pinGroupId,proto3" json:"pin_group_id"`
	//数量
	Number               int32    `protobuf:"varint,29,opt,name=number,proto3" json:"number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupDetail) Reset()         { *m = GroupDetail{} }
func (m *GroupDetail) String() string { return proto.CompactTextString(m) }
func (*GroupDetail) ProtoMessage()    {}
func (*GroupDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{7}
}

func (m *GroupDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupDetail.Unmarshal(m, b)
}
func (m *GroupDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupDetail.Marshal(b, m, deterministic)
}
func (m *GroupDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupDetail.Merge(m, src)
}
func (m *GroupDetail) XXX_Size() int {
	return xxx_messageInfo_GroupDetail.Size(m)
}
func (m *GroupDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GroupDetail proto.InternalMessageInfo

func (m *GroupDetail) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *GroupDetail) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GroupDetail) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GroupDetail) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GroupDetail) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GroupDetail) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *GroupDetail) GetGroupMembers() []*GroupMembers {
	if m != nil {
		return m.GroupMembers
	}
	return nil
}

func (m *GroupDetail) GetMaxParticipantNumber() int32 {
	if m != nil {
		return m.MaxParticipantNumber
	}
	return 0
}

func (m *GroupDetail) GetParticipantNumber() int32 {
	if m != nil {
		return m.ParticipantNumber
	}
	return 0
}

func (m *GroupDetail) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupDetail) GetProductImage() string {
	if m != nil {
		return m.ProductImage
	}
	return ""
}

func (m *GroupDetail) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *GroupDetail) GetPinPrice() int64 {
	if m != nil {
		return m.PinPrice
	}
	return 0
}

func (m *GroupDetail) GetMarkingPrice() int64 {
	if m != nil {
		return m.MarkingPrice
	}
	return 0
}

func (m *GroupDetail) GetIsPinHead() int32 {
	if m != nil {
		return m.IsPinHead
	}
	return 0
}

func (m *GroupDetail) GetOpenName() string {
	if m != nil {
		return m.OpenName
	}
	return ""
}

func (m *GroupDetail) GetPortrait() string {
	if m != nil {
		return m.Portrait
	}
	return ""
}

func (m *GroupDetail) GetPinHeadOrderSn() string {
	if m != nil {
		return m.PinHeadOrderSn
	}
	return ""
}

func (m *GroupDetail) GetPinHeadPortrait() string {
	if m != nil {
		return m.PinHeadPortrait
	}
	return ""
}

func (m *GroupDetail) GetIsAddGroup() int32 {
	if m != nil {
		return m.IsAddGroup
	}
	return 0
}

func (m *GroupDetail) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *GroupDetail) GetGroupBuyId() int32 {
	if m != nil {
		return m.GroupBuyId
	}
	return 0
}

func (m *GroupDetail) GetIsCoupon() int32 {
	if m != nil {
		return m.IsCoupon
	}
	return 0
}

func (m *GroupDetail) GetNowTime() string {
	if m != nil {
		return m.NowTime
	}
	return ""
}

func (m *GroupDetail) GetShareImgUrl() string {
	if m != nil {
		return m.ShareImgUrl
	}
	return ""
}

func (m *GroupDetail) GetSuccessCoupon() string {
	if m != nil {
		return m.SuccessCoupon
	}
	return ""
}

func (m *GroupDetail) GetGroupStatus() int32 {
	if m != nil {
		return m.GroupStatus
	}
	return 0
}

func (m *GroupDetail) GetPinGroupId() int32 {
	if m != nil {
		return m.PinGroupId
	}
	return 0
}

func (m *GroupDetail) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

type GroupMembers struct {
	//用户小程序ID
	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//用户小程序名称
	OpenName string `protobuf:"bytes,2,opt,name=open_name,json=openName,proto3" json:"open_name"`
	//用户ID
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//头像
	Portrait string `protobuf:"bytes,4,opt,name=portrait,proto3" json:"portrait"`
	// 创建时间（参与时间）
	CreateTime           string   `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupMembers) Reset()         { *m = GroupMembers{} }
func (m *GroupMembers) String() string { return proto.CompactTextString(m) }
func (*GroupMembers) ProtoMessage()    {}
func (*GroupMembers) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{8}
}

func (m *GroupMembers) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMembers.Unmarshal(m, b)
}
func (m *GroupMembers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMembers.Marshal(b, m, deterministic)
}
func (m *GroupMembers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMembers.Merge(m, src)
}
func (m *GroupMembers) XXX_Size() int {
	return xxx_messageInfo_GroupMembers.Size(m)
}
func (m *GroupMembers) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMembers.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMembers proto.InternalMessageInfo

func (m *GroupMembers) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GroupMembers) GetOpenName() string {
	if m != nil {
		return m.OpenName
	}
	return ""
}

func (m *GroupMembers) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GroupMembers) GetPortrait() string {
	if m != nil {
		return m.Portrait
	}
	return ""
}

func (m *GroupMembers) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type GetGroupParticipantRequest struct {
	//拼团活动商品skuid
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//拼团活动id
	GroupId int64 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	//当前页码
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页行数
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupParticipantRequest) Reset()         { *m = GetGroupParticipantRequest{} }
func (m *GetGroupParticipantRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupParticipantRequest) ProtoMessage()    {}
func (*GetGroupParticipantRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{9}
}

func (m *GetGroupParticipantRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupParticipantRequest.Unmarshal(m, b)
}
func (m *GetGroupParticipantRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupParticipantRequest.Marshal(b, m, deterministic)
}
func (m *GetGroupParticipantRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupParticipantRequest.Merge(m, src)
}
func (m *GetGroupParticipantRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupParticipantRequest.Size(m)
}
func (m *GetGroupParticipantRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupParticipantRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupParticipantRequest proto.InternalMessageInfo

func (m *GetGroupParticipantRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GetGroupParticipantRequest) GetGroupId() int64 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupParticipantRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetGroupParticipantRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGroupParticipantResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string         `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	TotalCount           int32          `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	Data                 []*Participant `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGroupParticipantResponse) Reset()         { *m = GetGroupParticipantResponse{} }
func (m *GetGroupParticipantResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupParticipantResponse) ProtoMessage()    {}
func (*GetGroupParticipantResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{10}
}

func (m *GetGroupParticipantResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupParticipantResponse.Unmarshal(m, b)
}
func (m *GetGroupParticipantResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupParticipantResponse.Marshal(b, m, deterministic)
}
func (m *GetGroupParticipantResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupParticipantResponse.Merge(m, src)
}
func (m *GetGroupParticipantResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupParticipantResponse.Size(m)
}
func (m *GetGroupParticipantResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupParticipantResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupParticipantResponse proto.InternalMessageInfo

func (m *GetGroupParticipantResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupParticipantResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetGroupParticipantResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetGroupParticipantResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetGroupParticipantResponse) GetData() []*Participant {
	if m != nil {
		return m.Data
	}
	return nil
}

type Participant struct {
	//用户小程序ID
	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//用户小程序名称
	OpenName string `protobuf:"bytes,2,opt,name=open_name,json=openName,proto3" json:"open_name"`
	//用户ID
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//头像
	Portrait string `protobuf:"bytes,4,opt,name=portrait,proto3" json:"portrait"`
	//类型 1发起拼团 2参团成功 3拼团成功
	Type int32 `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	//创建时间
	CreateTime           string   `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Participant) Reset()         { *m = Participant{} }
func (m *Participant) String() string { return proto.CompactTextString(m) }
func (*Participant) ProtoMessage()    {}
func (*Participant) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{11}
}

func (m *Participant) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Participant.Unmarshal(m, b)
}
func (m *Participant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Participant.Marshal(b, m, deterministic)
}
func (m *Participant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Participant.Merge(m, src)
}
func (m *Participant) XXX_Size() int {
	return xxx_messageInfo_Participant.Size(m)
}
func (m *Participant) XXX_DiscardUnknown() {
	xxx_messageInfo_Participant.DiscardUnknown(m)
}

var xxx_messageInfo_Participant proto.InternalMessageInfo

func (m *Participant) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *Participant) GetOpenName() string {
	if m != nil {
		return m.OpenName
	}
	return ""
}

func (m *Participant) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *Participant) GetPortrait() string {
	if m != nil {
		return m.Portrait
	}
	return ""
}

func (m *Participant) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Participant) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type CreateGroupOrderRequest struct {
	//是否团长 1是 0 否
	IsPinHead int32 `protobuf:"varint,1,opt,name=is_pin_head,json=isPinHead,proto3" json:"is_pin_head"`
	//参团需要带上团长订单号
	PinHeadOrderSn string `protobuf:"bytes,2,opt,name=pin_head_order_sn,json=pinHeadOrderSn,proto3" json:"pin_head_order_sn"`
	//拼团活动id
	GroupBuyId int64 `protobuf:"varint,3,opt,name=group_buy_id,json=groupBuyId,proto3" json:"group_buy_id"`
	//最大参与人数
	MaxParticipantNumber int32 `protobuf:"varint,4,opt,name=max_participant_number,json=maxParticipantNumber,proto3" json:"max_participant_number"`
	//参与人数
	ParticipantNumber int32 `protobuf:"varint,5,opt,name=participant_number,json=participantNumber,proto3" json:"participant_number"`
	//渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
	ChannelId int32 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	//头像
	Portrait string `protobuf:"bytes,7,opt,name=portrait,proto3" json:"portrait"`
	//小程序的用户id
	OpenId string `protobuf:"bytes,8,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//用户昵称
	OpenName string `protobuf:"bytes,9,opt,name=open_name,json=openName,proto3" json:"open_name"`
	//用户id
	UserId string `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//拼团商品sku_id
	SkuId string `protobuf:"bytes,11,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//开始时间
	StartTime string `protobuf:"bytes,12,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	//结束时间
	EndTime string `protobuf:"bytes,13,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//业务端json
	BusinessJson string `protobuf:"bytes,14,opt,name=business_json,json=businessJson,proto3" json:"business_json"`
	//提交到订单中心json
	SubmitJson string `protobuf:"bytes,15,opt,name=submit_json,json=submitJson,proto3" json:"submit_json"`
	//总金额
	TotalPrice int32 `protobuf:"varint,16,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	//支付金额
	PayPrice int32 `protobuf:"varint,17,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	//订单状态：0 已取消 10未支付 20 拼团进行中 30拼团成功 40拼团失败
	Status int32 `protobuf:"varint,18,opt,name=status,proto3" json:"status"`
	//拼团商品列表
	ProductList []*GroupOrderProduct `protobuf:"bytes,19,rep,name=product_list,json=productList,proto3" json:"product_list"`
	//店铺财务编码
	ShopId string `protobuf:"bytes,20,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	//是否虚拟订单 0否 1是
	IsVirtual int32 `protobuf:"varint,21,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单)
	OrderPayType         string   `protobuf:"bytes,22,opt,name=order_pay_type,json=orderPayType,proto3" json:"order_pay_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupOrderRequest) Reset()         { *m = CreateGroupOrderRequest{} }
func (m *CreateGroupOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CreateGroupOrderRequest) ProtoMessage()    {}
func (*CreateGroupOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{12}
}

func (m *CreateGroupOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupOrderRequest.Unmarshal(m, b)
}
func (m *CreateGroupOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupOrderRequest.Marshal(b, m, deterministic)
}
func (m *CreateGroupOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupOrderRequest.Merge(m, src)
}
func (m *CreateGroupOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CreateGroupOrderRequest.Size(m)
}
func (m *CreateGroupOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupOrderRequest proto.InternalMessageInfo

func (m *CreateGroupOrderRequest) GetIsPinHead() int32 {
	if m != nil {
		return m.IsPinHead
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetPinHeadOrderSn() string {
	if m != nil {
		return m.PinHeadOrderSn
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetGroupBuyId() int64 {
	if m != nil {
		return m.GroupBuyId
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetMaxParticipantNumber() int32 {
	if m != nil {
		return m.MaxParticipantNumber
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetParticipantNumber() int32 {
	if m != nil {
		return m.ParticipantNumber
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetPortrait() string {
	if m != nil {
		return m.Portrait
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetOpenName() string {
	if m != nil {
		return m.OpenName
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetBusinessJson() string {
	if m != nil {
		return m.BusinessJson
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetSubmitJson() string {
	if m != nil {
		return m.SubmitJson
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetTotalPrice() int32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetProductList() []*GroupOrderProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *CreateGroupOrderRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *CreateGroupOrderRequest) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *CreateGroupOrderRequest) GetOrderPayType() string {
	if m != nil {
		return m.OrderPayType
	}
	return ""
}

type GroupOrderProduct struct {
	//商品SKUID
	SkuId string `protobuf:"bytes,1,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//商品类别（1-实物商品，2-虚拟商品，3-组合商品）
	ProductType int32 `protobuf:"varint,2,opt,name=product_type,json=productType,proto3" json:"product_type"`
	//父商品skuid
	ParentSkuId string `protobuf:"bytes,3,opt,name=parent_sku_id,json=parentSkuId,proto3" json:"parent_sku_id"`
	//商品名称
	ProductName string `protobuf:"bytes,4,opt,name=product_name,json=productName,proto3" json:"product_name"`
	//商品图片
	ProductImage string `protobuf:"bytes,5,opt,name=product_image,json=productImage,proto3" json:"product_image"`
	//规格值
	SpecName string `protobuf:"bytes,6,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	//商品原单价
	MarkingPrice int32 `protobuf:"varint,7,opt,name=marking_price,json=markingPrice,proto3" json:"marking_price"`
	//商品拼团单价
	PinPrice int32 `protobuf:"varint,8,opt,name=pin_price,json=pinPrice,proto3" json:"pin_price"`
	//商品均摊后实际支付单价
	PayPrice int32 `protobuf:"varint,9,opt,name=pay_price,json=payPrice,proto3" json:"pay_price"`
	//数量
	Number int32 `protobuf:"varint,10,opt,name=number,proto3" json:"number"`
	//单人购买的最大数量
	MaxNumber int32 `protobuf:"varint,11,opt,name=max_number,json=maxNumber,proto3" json:"max_number"`
	//子商品列表
	ChildProduct         []*GroupOrderProduct `protobuf:"bytes,12,rep,name=childProduct,proto3" json:"childProduct"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GroupOrderProduct) Reset()         { *m = GroupOrderProduct{} }
func (m *GroupOrderProduct) String() string { return proto.CompactTextString(m) }
func (*GroupOrderProduct) ProtoMessage()    {}
func (*GroupOrderProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{13}
}

func (m *GroupOrderProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOrderProduct.Unmarshal(m, b)
}
func (m *GroupOrderProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOrderProduct.Marshal(b, m, deterministic)
}
func (m *GroupOrderProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOrderProduct.Merge(m, src)
}
func (m *GroupOrderProduct) XXX_Size() int {
	return xxx_messageInfo_GroupOrderProduct.Size(m)
}
func (m *GroupOrderProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOrderProduct.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOrderProduct proto.InternalMessageInfo

func (m *GroupOrderProduct) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *GroupOrderProduct) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *GroupOrderProduct) GetParentSkuId() string {
	if m != nil {
		return m.ParentSkuId
	}
	return ""
}

func (m *GroupOrderProduct) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *GroupOrderProduct) GetProductImage() string {
	if m != nil {
		return m.ProductImage
	}
	return ""
}

func (m *GroupOrderProduct) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *GroupOrderProduct) GetMarkingPrice() int32 {
	if m != nil {
		return m.MarkingPrice
	}
	return 0
}

func (m *GroupOrderProduct) GetPinPrice() int32 {
	if m != nil {
		return m.PinPrice
	}
	return 0
}

func (m *GroupOrderProduct) GetPayPrice() int32 {
	if m != nil {
		return m.PayPrice
	}
	return 0
}

func (m *GroupOrderProduct) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *GroupOrderProduct) GetMaxNumber() int32 {
	if m != nil {
		return m.MaxNumber
	}
	return 0
}

func (m *GroupOrderProduct) GetChildProduct() []*GroupOrderProduct {
	if m != nil {
		return m.ChildProduct
	}
	return nil
}

type CreateGroupOrderResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//拼团订单号
	PinOrderSn           string   `protobuf:"bytes,4,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupOrderResponse) Reset()         { *m = CreateGroupOrderResponse{} }
func (m *CreateGroupOrderResponse) String() string { return proto.CompactTextString(m) }
func (*CreateGroupOrderResponse) ProtoMessage()    {}
func (*CreateGroupOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{14}
}

func (m *CreateGroupOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupOrderResponse.Unmarshal(m, b)
}
func (m *CreateGroupOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupOrderResponse.Marshal(b, m, deterministic)
}
func (m *CreateGroupOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupOrderResponse.Merge(m, src)
}
func (m *CreateGroupOrderResponse) XXX_Size() int {
	return xxx_messageInfo_CreateGroupOrderResponse.Size(m)
}
func (m *CreateGroupOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupOrderResponse proto.InternalMessageInfo

func (m *CreateGroupOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreateGroupOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreateGroupOrderResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CreateGroupOrderResponse) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

type GroupOrderPayRequest struct {
	//付款码
	BarCode string `protobuf:"bytes,1,opt,name=bar_code,json=barCode,proto3" json:"bar_code"`
	//订单号
	PinOrderSn string `protobuf:"bytes,2,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//微信用户标识 JSAPI 支付时必传
	OpenId               string   `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupOrderPayRequest) Reset()         { *m = GroupOrderPayRequest{} }
func (m *GroupOrderPayRequest) String() string { return proto.CompactTextString(m) }
func (*GroupOrderPayRequest) ProtoMessage()    {}
func (*GroupOrderPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{15}
}

func (m *GroupOrderPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOrderPayRequest.Unmarshal(m, b)
}
func (m *GroupOrderPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOrderPayRequest.Marshal(b, m, deterministic)
}
func (m *GroupOrderPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOrderPayRequest.Merge(m, src)
}
func (m *GroupOrderPayRequest) XXX_Size() int {
	return xxx_messageInfo_GroupOrderPayRequest.Size(m)
}
func (m *GroupOrderPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOrderPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOrderPayRequest proto.InternalMessageInfo

func (m *GroupOrderPayRequest) GetBarCode() string {
	if m != nil {
		return m.BarCode
	}
	return ""
}

func (m *GroupOrderPayRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *GroupOrderPayRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

type CancelGroupOrderRequest struct {
	//拼团订单号
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//原因备注
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelGroupOrderRequest) Reset()         { *m = CancelGroupOrderRequest{} }
func (m *CancelGroupOrderRequest) String() string { return proto.CompactTextString(m) }
func (*CancelGroupOrderRequest) ProtoMessage()    {}
func (*CancelGroupOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{16}
}

func (m *CancelGroupOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelGroupOrderRequest.Unmarshal(m, b)
}
func (m *CancelGroupOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelGroupOrderRequest.Marshal(b, m, deterministic)
}
func (m *CancelGroupOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelGroupOrderRequest.Merge(m, src)
}
func (m *CancelGroupOrderRequest) XXX_Size() int {
	return xxx_messageInfo_CancelGroupOrderRequest.Size(m)
}
func (m *CancelGroupOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelGroupOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelGroupOrderRequest proto.InternalMessageInfo

func (m *CancelGroupOrderRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *CancelGroupOrderRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SetOrderSubscribeMessageRequest struct {
	//拼团订单号
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//小程序的用户id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//用户id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	//消息类型：1拼团成功 2拼团失败 3支付成功 4超时未支付取消 5退款成功
	Type                 int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOrderSubscribeMessageRequest) Reset()         { *m = SetOrderSubscribeMessageRequest{} }
func (m *SetOrderSubscribeMessageRequest) String() string { return proto.CompactTextString(m) }
func (*SetOrderSubscribeMessageRequest) ProtoMessage()    {}
func (*SetOrderSubscribeMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{17}
}

func (m *SetOrderSubscribeMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetOrderSubscribeMessageRequest.Unmarshal(m, b)
}
func (m *SetOrderSubscribeMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetOrderSubscribeMessageRequest.Marshal(b, m, deterministic)
}
func (m *SetOrderSubscribeMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOrderSubscribeMessageRequest.Merge(m, src)
}
func (m *SetOrderSubscribeMessageRequest) XXX_Size() int {
	return xxx_messageInfo_SetOrderSubscribeMessageRequest.Size(m)
}
func (m *SetOrderSubscribeMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOrderSubscribeMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetOrderSubscribeMessageRequest proto.InternalMessageInfo

func (m *SetOrderSubscribeMessageRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *SetOrderSubscribeMessageRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SetOrderSubscribeMessageRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SetOrderSubscribeMessageRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

//获取拼团商品的订单统计
type GetPinGroupProductOrderStaticsRequest struct {
	//拼团订单id
	GroupBuyId int32 `protobuf:"varint,1,opt,name=groupBuyId,proto3" json:"groupBuyId"`
	//拼团商品的skuId
	SkuId int32 `protobuf:"varint,2,opt,name=skuId,proto3" json:"skuId"`
	//渠道
	ChannelId            int32                        `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	Fields               *PinGroupProductOrderStatics `protobuf:"bytes,4,opt,name=fields,proto3" json:"fields"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetPinGroupProductOrderStaticsRequest) Reset()         { *m = GetPinGroupProductOrderStaticsRequest{} }
func (m *GetPinGroupProductOrderStaticsRequest) String() string { return proto.CompactTextString(m) }
func (*GetPinGroupProductOrderStaticsRequest) ProtoMessage()    {}
func (*GetPinGroupProductOrderStaticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{18}
}

func (m *GetPinGroupProductOrderStaticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsRequest.Unmarshal(m, b)
}
func (m *GetPinGroupProductOrderStaticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsRequest.Marshal(b, m, deterministic)
}
func (m *GetPinGroupProductOrderStaticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinGroupProductOrderStaticsRequest.Merge(m, src)
}
func (m *GetPinGroupProductOrderStaticsRequest) XXX_Size() int {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsRequest.Size(m)
}
func (m *GetPinGroupProductOrderStaticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinGroupProductOrderStaticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinGroupProductOrderStaticsRequest proto.InternalMessageInfo

func (m *GetPinGroupProductOrderStaticsRequest) GetGroupBuyId() int32 {
	if m != nil {
		return m.GroupBuyId
	}
	return 0
}

func (m *GetPinGroupProductOrderStaticsRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetPinGroupProductOrderStaticsRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPinGroupProductOrderStaticsRequest) GetFields() *PinGroupProductOrderStatics {
	if m != nil {
		return m.Fields
	}
	return nil
}

//获取拼团商品的订单统计
type GetPinGroupProductOrderStaticsResponse struct {
	//成功的团里的去重人数
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error                string                       `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	Data                 *PinGroupProductOrderStatics `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetPinGroupProductOrderStaticsResponse) Reset() {
	*m = GetPinGroupProductOrderStaticsResponse{}
}
func (m *GetPinGroupProductOrderStaticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetPinGroupProductOrderStaticsResponse) ProtoMessage()    {}
func (*GetPinGroupProductOrderStaticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{19}
}

func (m *GetPinGroupProductOrderStaticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsResponse.Unmarshal(m, b)
}
func (m *GetPinGroupProductOrderStaticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsResponse.Marshal(b, m, deterministic)
}
func (m *GetPinGroupProductOrderStaticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinGroupProductOrderStaticsResponse.Merge(m, src)
}
func (m *GetPinGroupProductOrderStaticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetPinGroupProductOrderStaticsResponse.Size(m)
}
func (m *GetPinGroupProductOrderStaticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinGroupProductOrderStaticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinGroupProductOrderStaticsResponse proto.InternalMessageInfo

func (m *GetPinGroupProductOrderStaticsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPinGroupProductOrderStaticsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPinGroupProductOrderStaticsResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPinGroupProductOrderStaticsResponse) GetData() *PinGroupProductOrderStatics {
	if m != nil {
		return m.Data
	}
	return nil
}

//订单统计字段
type PinGroupProductOrderStatics struct {
	//总订单数记录数 包括所有的数据库记录 0或1 0表示统计 1表示不统计 返回时表示该统计的结果值
	TotalOrderCount int32 `protobuf:"varint,1,opt,name=totalOrderCount,proto3" json:"totalOrderCount"`
	//成功的团里的去重人数 请求是传 0或1 0表示统计 1表示不统计 返回时表示该统计的结果值
	SuccessDistinctUserCount int32 `protobuf:"varint,2,opt,name=successDistinctUserCount,proto3" json:"successDistinctUserCount"`
	//成功的团里的真人订单数（去掉虚拟的模拟的订单） 请求是传 0 或1 0表示统计 1表示不统计 返回时表示该统计的结果值
	SuccessRealOrderCount int32    `protobuf:"varint,3,opt,name=successRealOrderCount,proto3" json:"successRealOrderCount"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PinGroupProductOrderStatics) Reset()         { *m = PinGroupProductOrderStatics{} }
func (m *PinGroupProductOrderStatics) String() string { return proto.CompactTextString(m) }
func (*PinGroupProductOrderStatics) ProtoMessage()    {}
func (*PinGroupProductOrderStatics) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{20}
}

func (m *PinGroupProductOrderStatics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PinGroupProductOrderStatics.Unmarshal(m, b)
}
func (m *PinGroupProductOrderStatics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PinGroupProductOrderStatics.Marshal(b, m, deterministic)
}
func (m *PinGroupProductOrderStatics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PinGroupProductOrderStatics.Merge(m, src)
}
func (m *PinGroupProductOrderStatics) XXX_Size() int {
	return xxx_messageInfo_PinGroupProductOrderStatics.Size(m)
}
func (m *PinGroupProductOrderStatics) XXX_DiscardUnknown() {
	xxx_messageInfo_PinGroupProductOrderStatics.DiscardUnknown(m)
}

var xxx_messageInfo_PinGroupProductOrderStatics proto.InternalMessageInfo

func (m *PinGroupProductOrderStatics) GetTotalOrderCount() int32 {
	if m != nil {
		return m.TotalOrderCount
	}
	return 0
}

func (m *PinGroupProductOrderStatics) GetSuccessDistinctUserCount() int32 {
	if m != nil {
		return m.SuccessDistinctUserCount
	}
	return 0
}

func (m *PinGroupProductOrderStatics) GetSuccessRealOrderCount() int32 {
	if m != nil {
		return m.SuccessRealOrderCount
	}
	return 0
}

type PinOrderPayRequest struct {
	//订单id
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//微信用户标识 JSAPI 支付时必传
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	TransType            int32    `protobuf:"varint,3,opt,name=trans_type,json=transType,proto3" json:"trans_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PinOrderPayRequest) Reset()         { *m = PinOrderPayRequest{} }
func (m *PinOrderPayRequest) String() string { return proto.CompactTextString(m) }
func (*PinOrderPayRequest) ProtoMessage()    {}
func (*PinOrderPayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{21}
}

func (m *PinOrderPayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PinOrderPayRequest.Unmarshal(m, b)
}
func (m *PinOrderPayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PinOrderPayRequest.Marshal(b, m, deterministic)
}
func (m *PinOrderPayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PinOrderPayRequest.Merge(m, src)
}
func (m *PinOrderPayRequest) XXX_Size() int {
	return xxx_messageInfo_PinOrderPayRequest.Size(m)
}
func (m *PinOrderPayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PinOrderPayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PinOrderPayRequest proto.InternalMessageInfo

func (m *PinOrderPayRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *PinOrderPayRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *PinOrderPayRequest) GetTransType() int32 {
	if m != nil {
		return m.TransType
	}
	return 0
}

type GroupOrderListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单
	Details []*PinGroupOrderListInfo `protobuf:"bytes,4,rep,name=details,proto3" json:"details"`
	// 总数
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupOrderListResponse) Reset()         { *m = GroupOrderListResponse{} }
func (m *GroupOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GroupOrderListResponse) ProtoMessage()    {}
func (*GroupOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{22}
}

func (m *GroupOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOrderListResponse.Unmarshal(m, b)
}
func (m *GroupOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOrderListResponse.Marshal(b, m, deterministic)
}
func (m *GroupOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOrderListResponse.Merge(m, src)
}
func (m *GroupOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GroupOrderListResponse.Size(m)
}
func (m *GroupOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOrderListResponse proto.InternalMessageInfo

func (m *GroupOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GroupOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GroupOrderListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GroupOrderListResponse) GetDetails() []*PinGroupOrderListInfo {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *GroupOrderListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type PinGroupOrderListInfo struct {
	// 订单索引ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 订单编号
	PinOrderSn string `protobuf:"bytes,2,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	// 订单状态：0已取消 10未支付 20拼团进行中 30拼团成功 40拼团失败
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	// 开始时间
	StartTime string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 需要人数
	NeedNumber int32 `protobuf:"varint,6,opt,name=need_number,json=needNumber,proto3" json:"need_number"`
	// 是否团长 1是 0 否
	IsPinHead int32 `protobuf:"varint,7,opt,name=is_pin_head,json=isPinHead,proto3" json:"is_pin_head"`
	//最大参与人数
	MaxParticipantNumber int32 `protobuf:"varint,8,opt,name=max_participant_number,json=maxParticipantNumber,proto3" json:"max_participant_number"`
	//参与人数
	ParticipantNumber int32 `protobuf:"varint,9,opt,name=participant_number,json=participantNumber,proto3" json:"participant_number"`
	// 商品skuid
	SkuId string `protobuf:"bytes,10,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	// 商品名称
	ProductName string `protobuf:"bytes,11,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 商品图片
	ProductImage string `protobuf:"bytes,12,opt,name=product_image,json=productImage,proto3" json:"product_image"`
	// 规格名称
	SpecName string `protobuf:"bytes,13,opt,name=spec_name,json=specName,proto3" json:"spec_name"`
	// 拼团价
	PinPrice int64 `protobuf:"varint,14,opt,name=pin_price,json=pinPrice,proto3" json:"pin_price"`
	// 商品原价
	MarkingPrice int64 `protobuf:"varint,15,opt,name=marking_price,json=markingPrice,proto3" json:"marking_price"`
	//结束时间 时间戳
	Etime int64 `protobuf:"varint,16,opt,name=etime,proto3" json:"etime"`
	// 当前时间
	NowTime string `protobuf:"bytes,17,opt,name=now_time,json=nowTime,proto3" json:"now_time"`
	// 创建时间
	CreateTime string `protobuf:"bytes,18,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	// 支付结束时间
	PayEndTime string `protobuf:"bytes,19,opt,name=pay_end_time,json=payEndTime,proto3" json:"pay_end_time"`
	// 是否虚拟订单 0 否 1 是
	IsVirtual int32 `protobuf:"varint,20,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	//团状态：0拼主已创建但未支付 10拼团进行中 20 拼团成功 30 拼团失败
	GroupStatus int32 `protobuf:"varint,21,opt,name=group_status,json=groupStatus,proto3" json:"group_status"`
	//数量
	Number int32 `protobuf:"varint,22,opt,name=number,proto3" json:"number"`
	//拼团父订单号
	ParentPinOrderSn     string   `protobuf:"bytes,23,opt,name=parent_pin_order_sn,json=parentPinOrderSn,proto3" json:"parent_pin_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PinGroupOrderListInfo) Reset()         { *m = PinGroupOrderListInfo{} }
func (m *PinGroupOrderListInfo) String() string { return proto.CompactTextString(m) }
func (*PinGroupOrderListInfo) ProtoMessage()    {}
func (*PinGroupOrderListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{23}
}

func (m *PinGroupOrderListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PinGroupOrderListInfo.Unmarshal(m, b)
}
func (m *PinGroupOrderListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PinGroupOrderListInfo.Marshal(b, m, deterministic)
}
func (m *PinGroupOrderListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PinGroupOrderListInfo.Merge(m, src)
}
func (m *PinGroupOrderListInfo) XXX_Size() int {
	return xxx_messageInfo_PinGroupOrderListInfo.Size(m)
}
func (m *PinGroupOrderListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PinGroupOrderListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PinGroupOrderListInfo proto.InternalMessageInfo

func (m *PinGroupOrderListInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetNeedNumber() int32 {
	if m != nil {
		return m.NeedNumber
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetIsPinHead() int32 {
	if m != nil {
		return m.IsPinHead
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetMaxParticipantNumber() int32 {
	if m != nil {
		return m.MaxParticipantNumber
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetParticipantNumber() int32 {
	if m != nil {
		return m.ParticipantNumber
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetProductImage() string {
	if m != nil {
		return m.ProductImage
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetSpecName() string {
	if m != nil {
		return m.SpecName
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetPinPrice() int64 {
	if m != nil {
		return m.PinPrice
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetMarkingPrice() int64 {
	if m != nil {
		return m.MarkingPrice
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetEtime() int64 {
	if m != nil {
		return m.Etime
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetNowTime() string {
	if m != nil {
		return m.NowTime
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetPayEndTime() string {
	if m != nil {
		return m.PayEndTime
	}
	return ""
}

func (m *PinGroupOrderListInfo) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetGroupStatus() int32 {
	if m != nil {
		return m.GroupStatus
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetNumber() int32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *PinGroupOrderListInfo) GetParentPinOrderSn() string {
	if m != nil {
		return m.ParentPinOrderSn
	}
	return ""
}

type GetCanParticipantOrderListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单
	Data []*CanParticipantOrder `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	// 总数
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCanParticipantOrderListResponse) Reset()         { *m = GetCanParticipantOrderListResponse{} }
func (m *GetCanParticipantOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCanParticipantOrderListResponse) ProtoMessage()    {}
func (*GetCanParticipantOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{24}
}

func (m *GetCanParticipantOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCanParticipantOrderListResponse.Unmarshal(m, b)
}
func (m *GetCanParticipantOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCanParticipantOrderListResponse.Marshal(b, m, deterministic)
}
func (m *GetCanParticipantOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCanParticipantOrderListResponse.Merge(m, src)
}
func (m *GetCanParticipantOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCanParticipantOrderListResponse.Size(m)
}
func (m *GetCanParticipantOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCanParticipantOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCanParticipantOrderListResponse proto.InternalMessageInfo

func (m *GetCanParticipantOrderListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetCanParticipantOrderListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetCanParticipantOrderListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetCanParticipantOrderListResponse) GetData() []*CanParticipantOrder {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetCanParticipantOrderListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type CanParticipantOrder struct {
	// 用户昵称
	OpenName string `protobuf:"bytes,1,opt,name=open_name,json=openName,proto3" json:"open_name"`
	// 头像
	Portrait string `protobuf:"bytes,2,opt,name=portrait,proto3" json:"portrait"`
	// 最大参与人数
	MaxParticipantNumber int32 `protobuf:"varint,3,opt,name=max_participant_number,json=maxParticipantNumber,proto3" json:"max_participant_number"`
	// 参与人数
	ParticipantNumber int32 `protobuf:"varint,4,opt,name=participant_number,json=participantNumber,proto3" json:"participant_number"`
	// 开始时间
	StartTime string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间
	EndTime string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 拼团商品SKUID
	PinSkuId string `protobuf:"bytes,7,opt,name=pin_sku_id,json=pinSkuId,proto3" json:"pin_sku_id"`
	// 订单编号
	PinOrderSn string `protobuf:"bytes,8,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	// 拼团订单父订单号
	ParentPinOrderSn string `protobuf:"bytes,9,opt,name=parent_pin_order_sn,json=parentPinOrderSn,proto3" json:"parent_pin_order_sn"`
	// 拼团活动id
	GroupBuyId           int32    `protobuf:"varint,10,opt,name=group_buy_id,json=groupBuyId,proto3" json:"group_buy_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CanParticipantOrder) Reset()         { *m = CanParticipantOrder{} }
func (m *CanParticipantOrder) String() string { return proto.CompactTextString(m) }
func (*CanParticipantOrder) ProtoMessage()    {}
func (*CanParticipantOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{25}
}

func (m *CanParticipantOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CanParticipantOrder.Unmarshal(m, b)
}
func (m *CanParticipantOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CanParticipantOrder.Marshal(b, m, deterministic)
}
func (m *CanParticipantOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CanParticipantOrder.Merge(m, src)
}
func (m *CanParticipantOrder) XXX_Size() int {
	return xxx_messageInfo_CanParticipantOrder.Size(m)
}
func (m *CanParticipantOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_CanParticipantOrder.DiscardUnknown(m)
}

var xxx_messageInfo_CanParticipantOrder proto.InternalMessageInfo

func (m *CanParticipantOrder) GetOpenName() string {
	if m != nil {
		return m.OpenName
	}
	return ""
}

func (m *CanParticipantOrder) GetPortrait() string {
	if m != nil {
		return m.Portrait
	}
	return ""
}

func (m *CanParticipantOrder) GetMaxParticipantNumber() int32 {
	if m != nil {
		return m.MaxParticipantNumber
	}
	return 0
}

func (m *CanParticipantOrder) GetParticipantNumber() int32 {
	if m != nil {
		return m.ParticipantNumber
	}
	return 0
}

func (m *CanParticipantOrder) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *CanParticipantOrder) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *CanParticipantOrder) GetPinSkuId() string {
	if m != nil {
		return m.PinSkuId
	}
	return ""
}

func (m *CanParticipantOrder) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *CanParticipantOrder) GetParentPinOrderSn() string {
	if m != nil {
		return m.ParentPinOrderSn
	}
	return ""
}

func (m *CanParticipantOrder) GetGroupBuyId() int32 {
	if m != nil {
		return m.GroupBuyId
	}
	return 0
}

type NewSubscribeMessageRequest struct {
	// 拼团订单ID
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 小程序用户ID
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	// 用户ID
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 微信订阅消息
	WxInfo               []*WxSubscribeMessage `protobuf:"bytes,4,rep,name=wx_info,json=wxInfo,proto3" json:"wx_info"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *NewSubscribeMessageRequest) Reset()         { *m = NewSubscribeMessageRequest{} }
func (m *NewSubscribeMessageRequest) String() string { return proto.CompactTextString(m) }
func (*NewSubscribeMessageRequest) ProtoMessage()    {}
func (*NewSubscribeMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{26}
}

func (m *NewSubscribeMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewSubscribeMessageRequest.Unmarshal(m, b)
}
func (m *NewSubscribeMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewSubscribeMessageRequest.Marshal(b, m, deterministic)
}
func (m *NewSubscribeMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewSubscribeMessageRequest.Merge(m, src)
}
func (m *NewSubscribeMessageRequest) XXX_Size() int {
	return xxx_messageInfo_NewSubscribeMessageRequest.Size(m)
}
func (m *NewSubscribeMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NewSubscribeMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NewSubscribeMessageRequest proto.InternalMessageInfo

func (m *NewSubscribeMessageRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *NewSubscribeMessageRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *NewSubscribeMessageRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *NewSubscribeMessageRequest) GetWxInfo() []*WxSubscribeMessage {
	if m != nil {
		return m.WxInfo
	}
	return nil
}

type WxSubscribeMessage struct {
	// 微信模板ID
	TemplateId string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 消息类型：1拼团成功 2拼团失败 3支付成功 4超时未支付取消 5退款成功
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WxSubscribeMessage) Reset()         { *m = WxSubscribeMessage{} }
func (m *WxSubscribeMessage) String() string { return proto.CompactTextString(m) }
func (*WxSubscribeMessage) ProtoMessage()    {}
func (*WxSubscribeMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{27}
}

func (m *WxSubscribeMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WxSubscribeMessage.Unmarshal(m, b)
}
func (m *WxSubscribeMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WxSubscribeMessage.Marshal(b, m, deterministic)
}
func (m *WxSubscribeMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WxSubscribeMessage.Merge(m, src)
}
func (m *WxSubscribeMessage) XXX_Size() int {
	return xxx_messageInfo_WxSubscribeMessage.Size(m)
}
func (m *WxSubscribeMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_WxSubscribeMessage.DiscardUnknown(m)
}

var xxx_messageInfo_WxSubscribeMessage proto.InternalMessageInfo

func (m *WxSubscribeMessage) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *WxSubscribeMessage) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type SubscribeMessageListRequest struct {
	// 拼团订单ID
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 小程序用户ID
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id"`
	//当前页码
	PageIndex int32 `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页行数
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 微信模板ID,多个英文逗号隔开
	TemplateId           string   `protobuf:"bytes,5,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeMessageListRequest) Reset()         { *m = SubscribeMessageListRequest{} }
func (m *SubscribeMessageListRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeMessageListRequest) ProtoMessage()    {}
func (*SubscribeMessageListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{28}
}

func (m *SubscribeMessageListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeMessageListRequest.Unmarshal(m, b)
}
func (m *SubscribeMessageListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeMessageListRequest.Marshal(b, m, deterministic)
}
func (m *SubscribeMessageListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeMessageListRequest.Merge(m, src)
}
func (m *SubscribeMessageListRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeMessageListRequest.Size(m)
}
func (m *SubscribeMessageListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeMessageListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeMessageListRequest proto.InternalMessageInfo

func (m *SubscribeMessageListRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *SubscribeMessageListRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *SubscribeMessageListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *SubscribeMessageListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *SubscribeMessageListRequest) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

type SubscribeMessageListResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//订单
	Data                 []*OrderSubscribeMessage `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SubscribeMessageListResponse) Reset()         { *m = SubscribeMessageListResponse{} }
func (m *SubscribeMessageListResponse) String() string { return proto.CompactTextString(m) }
func (*SubscribeMessageListResponse) ProtoMessage()    {}
func (*SubscribeMessageListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{29}
}

func (m *SubscribeMessageListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeMessageListResponse.Unmarshal(m, b)
}
func (m *SubscribeMessageListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeMessageListResponse.Marshal(b, m, deterministic)
}
func (m *SubscribeMessageListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeMessageListResponse.Merge(m, src)
}
func (m *SubscribeMessageListResponse) XXX_Size() int {
	return xxx_messageInfo_SubscribeMessageListResponse.Size(m)
}
func (m *SubscribeMessageListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeMessageListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeMessageListResponse proto.InternalMessageInfo

func (m *SubscribeMessageListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SubscribeMessageListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SubscribeMessageListResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *SubscribeMessageListResponse) GetData() []*OrderSubscribeMessage {
	if m != nil {
		return m.Data
	}
	return nil
}

type OrderSubscribeMessage struct {
	// 索引ID
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 拼团订单ID
	OrderSn string `protobuf:"bytes,2,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 微信模板ID
	TemplateId string `protobuf:"bytes,3,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 消息类型：1拼团成功 2拼团失败 3订单状态 4售后单状态 5退款成功 6售后单审核不通过
	Type                 int32    `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	CreateTime           string   `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderSubscribeMessage) Reset()         { *m = OrderSubscribeMessage{} }
func (m *OrderSubscribeMessage) String() string { return proto.CompactTextString(m) }
func (*OrderSubscribeMessage) ProtoMessage()    {}
func (*OrderSubscribeMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{30}
}

func (m *OrderSubscribeMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderSubscribeMessage.Unmarshal(m, b)
}
func (m *OrderSubscribeMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderSubscribeMessage.Marshal(b, m, deterministic)
}
func (m *OrderSubscribeMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderSubscribeMessage.Merge(m, src)
}
func (m *OrderSubscribeMessage) XXX_Size() int {
	return xxx_messageInfo_OrderSubscribeMessage.Size(m)
}
func (m *OrderSubscribeMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderSubscribeMessage.DiscardUnknown(m)
}

var xxx_messageInfo_OrderSubscribeMessage proto.InternalMessageInfo

func (m *OrderSubscribeMessage) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OrderSubscribeMessage) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *OrderSubscribeMessage) GetTemplateId() string {
	if m != nil {
		return m.TemplateId
	}
	return ""
}

func (m *OrderSubscribeMessage) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *OrderSubscribeMessage) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type PinOrderRefundRequest struct {
	//订单id
	PinOrderSn           string   `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PinOrderRefundRequest) Reset()         { *m = PinOrderRefundRequest{} }
func (m *PinOrderRefundRequest) String() string { return proto.CompactTextString(m) }
func (*PinOrderRefundRequest) ProtoMessage()    {}
func (*PinOrderRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{31}
}

func (m *PinOrderRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PinOrderRefundRequest.Unmarshal(m, b)
}
func (m *PinOrderRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PinOrderRefundRequest.Marshal(b, m, deterministic)
}
func (m *PinOrderRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PinOrderRefundRequest.Merge(m, src)
}
func (m *PinOrderRefundRequest) XXX_Size() int {
	return xxx_messageInfo_PinOrderRefundRequest.Size(m)
}
func (m *PinOrderRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PinOrderRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PinOrderRefundRequest proto.InternalMessageInfo

func (m *PinOrderRefundRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

type RefundNotifyRequest struct {
	//拼团单号
	PinOrderSn string `protobuf:"bytes,1,opt,name=pin_order_sn,json=pinOrderSn,proto3" json:"pin_order_sn"`
	//退款流水号
	TransactionNo string `protobuf:"bytes,2,opt,name=transaction_no,json=transactionNo,proto3" json:"transaction_no"`
	//退款金额（分）
	RefundAmount         int32    `protobuf:"varint,3,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount"`
	ResCode              string   `protobuf:"bytes,4,opt,name=res_code,json=resCode,proto3" json:"res_code"`
	ResMessage           string   `protobuf:"bytes,5,opt,name=res_message,json=resMessage,proto3" json:"res_message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundNotifyRequest) Reset()         { *m = RefundNotifyRequest{} }
func (m *RefundNotifyRequest) String() string { return proto.CompactTextString(m) }
func (*RefundNotifyRequest) ProtoMessage()    {}
func (*RefundNotifyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{32}
}

func (m *RefundNotifyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundNotifyRequest.Unmarshal(m, b)
}
func (m *RefundNotifyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundNotifyRequest.Marshal(b, m, deterministic)
}
func (m *RefundNotifyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundNotifyRequest.Merge(m, src)
}
func (m *RefundNotifyRequest) XXX_Size() int {
	return xxx_messageInfo_RefundNotifyRequest.Size(m)
}
func (m *RefundNotifyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundNotifyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefundNotifyRequest proto.InternalMessageInfo

func (m *RefundNotifyRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

func (m *RefundNotifyRequest) GetTransactionNo() string {
	if m != nil {
		return m.TransactionNo
	}
	return ""
}

func (m *RefundNotifyRequest) GetRefundAmount() int32 {
	if m != nil {
		return m.RefundAmount
	}
	return 0
}

func (m *RefundNotifyRequest) GetResCode() string {
	if m != nil {
		return m.ResCode
	}
	return ""
}

func (m *RefundNotifyRequest) GetResMessage() string {
	if m != nil {
		return m.ResMessage
	}
	return ""
}

type UnifiedOrderReturnData struct {
	//微信appid
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId"`
	//随机字符串，不长于 32位
	NonceStr string `protobuf:"bytes,2,opt,name=nonceStr,proto3" json:"nonceStr"`
	//订单详情扩展字符串
	Package string `protobuf:"bytes,3,opt,name=package,proto3" json:"package"`
	//签名
	PaySign string `protobuf:"bytes,4,opt,name=paySign,proto3" json:"paySign"`
	//签名方式
	SignType string `protobuf:"bytes,5,opt,name=signType,proto3" json:"signType"`
	//时间戳
	TimeStamp            string   `protobuf:"bytes,6,opt,name=timeStamp,proto3" json:"timeStamp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderReturnData) Reset()         { *m = UnifiedOrderReturnData{} }
func (m *UnifiedOrderReturnData) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderReturnData) ProtoMessage()    {}
func (*UnifiedOrderReturnData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{33}
}

func (m *UnifiedOrderReturnData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderReturnData.Unmarshal(m, b)
}
func (m *UnifiedOrderReturnData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderReturnData.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderReturnData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderReturnData.Merge(m, src)
}
func (m *UnifiedOrderReturnData) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderReturnData.Size(m)
}
func (m *UnifiedOrderReturnData) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderReturnData.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderReturnData proto.InternalMessageInfo

func (m *UnifiedOrderReturnData) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetNonceStr() string {
	if m != nil {
		return m.NonceStr
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetPackage() string {
	if m != nil {
		return m.Package
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetPaySign() string {
	if m != nil {
		return m.PaySign
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetSignType() string {
	if m != nil {
		return m.SignType
	}
	return ""
}

func (m *UnifiedOrderReturnData) GetTimeStamp() string {
	if m != nil {
		return m.TimeStamp
	}
	return ""
}

type UnifiedOrderResponse struct {
	Code    int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string                    `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Details *UnifiedOrderResponseData `protobuf:"bytes,3,opt,name=details,proto3" json:"details"`
	//下单时间(13位，毫秒)
	CreateTime int64 `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	//支付方式 1：微信 JSAPI 2:微信扫码C扫B 8:储蓄卡支付
	PayType              int32    `protobuf:"varint,5,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderResponse) Reset()         { *m = UnifiedOrderResponse{} }
func (m *UnifiedOrderResponse) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderResponse) ProtoMessage()    {}
func (*UnifiedOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{34}
}

func (m *UnifiedOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderResponse.Unmarshal(m, b)
}
func (m *UnifiedOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderResponse.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderResponse.Merge(m, src)
}
func (m *UnifiedOrderResponse) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderResponse.Size(m)
}
func (m *UnifiedOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderResponse proto.InternalMessageInfo

func (m *UnifiedOrderResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UnifiedOrderResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UnifiedOrderResponse) GetDetails() *UnifiedOrderResponseData {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *UnifiedOrderResponse) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UnifiedOrderResponse) GetPayType() int32 {
	if m != nil {
		return m.PayType
	}
	return 0
}

type UnifiedOrderResponseData struct {
	//当支付方式为wx_jsapi返回
	WxJsapi *UnifiedOrderReturnData `protobuf:"bytes,1,opt,name=wx_jsapi,json=wxJsapi,proto3" json:"wx_jsapi"`
	OrderId string                  `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	//当支付方式为WX_NATIVE返回
	WxNative *UnifiedOrderWxNative `protobuf:"bytes,3,opt,name=wx_native,json=wxNative,proto3" json:"wx_native"`
	//当支付方式为WX_JSAPP返回
	WxJsApp *UnifiedOrderWxJsApp `protobuf:"bytes,4,opt,name=wx_js_app,json=wxJsApp,proto3" json:"wx_js_app"`
	//当支付方式是网银
	Bank string `protobuf:"bytes,5,opt,name=bank,proto3" json:"bank"`
	// 支付宝小程序交易单号
	AliTradeNo string `protobuf:"bytes,6,opt,name=aliTradeNo,proto3" json:"aliTradeNo"`
	// 百度支付订单信息
	BdOrderInfo          *UnifiedOrderBdPay `protobuf:"bytes,7,opt,name=bd_order_info,json=bdOrderInfo,proto3" json:"bd_order_info"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UnifiedOrderResponseData) Reset()         { *m = UnifiedOrderResponseData{} }
func (m *UnifiedOrderResponseData) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderResponseData) ProtoMessage()    {}
func (*UnifiedOrderResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{34, 0}
}

func (m *UnifiedOrderResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderResponseData.Unmarshal(m, b)
}
func (m *UnifiedOrderResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderResponseData.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderResponseData.Merge(m, src)
}
func (m *UnifiedOrderResponseData) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderResponseData.Size(m)
}
func (m *UnifiedOrderResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderResponseData proto.InternalMessageInfo

func (m *UnifiedOrderResponseData) GetWxJsapi() *UnifiedOrderReturnData {
	if m != nil {
		return m.WxJsapi
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetWxNative() *UnifiedOrderWxNative {
	if m != nil {
		return m.WxNative
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetWxJsApp() *UnifiedOrderWxJsApp {
	if m != nil {
		return m.WxJsApp
	}
	return nil
}

func (m *UnifiedOrderResponseData) GetBank() string {
	if m != nil {
		return m.Bank
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetAliTradeNo() string {
	if m != nil {
		return m.AliTradeNo
	}
	return ""
}

func (m *UnifiedOrderResponseData) GetBdOrderInfo() *UnifiedOrderBdPay {
	if m != nil {
		return m.BdOrderInfo
	}
	return nil
}

type UnifiedOrderWxJsApp struct {
	//transType 为 WX_JSAPP/AL_JSAPP 时返回
	JsAppId string `protobuf:"bytes,1,opt,name=jsAppId,proto3" json:"jsAppId"`
	//transType 为 WX_JSAPP/AL_JSAPP 时返回
	JsAppUrl             string   `protobuf:"bytes,2,opt,name=jsAppUrl,proto3" json:"jsAppUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderWxJsApp) Reset()         { *m = UnifiedOrderWxJsApp{} }
func (m *UnifiedOrderWxJsApp) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderWxJsApp) ProtoMessage()    {}
func (*UnifiedOrderWxJsApp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{35}
}

func (m *UnifiedOrderWxJsApp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Unmarshal(m, b)
}
func (m *UnifiedOrderWxJsApp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderWxJsApp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderWxJsApp.Merge(m, src)
}
func (m *UnifiedOrderWxJsApp) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderWxJsApp.Size(m)
}
func (m *UnifiedOrderWxJsApp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderWxJsApp.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderWxJsApp proto.InternalMessageInfo

func (m *UnifiedOrderWxJsApp) GetJsAppId() string {
	if m != nil {
		return m.JsAppId
	}
	return ""
}

func (m *UnifiedOrderWxJsApp) GetJsAppUrl() string {
	if m != nil {
		return m.JsAppUrl
	}
	return ""
}

type UnifiedOrderWxNative struct {
	//支付链接
	PayUrl               string   `protobuf:"bytes,1,opt,name=payUrl,proto3" json:"payUrl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedOrderWxNative) Reset()         { *m = UnifiedOrderWxNative{} }
func (m *UnifiedOrderWxNative) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderWxNative) ProtoMessage()    {}
func (*UnifiedOrderWxNative) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{36}
}

func (m *UnifiedOrderWxNative) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderWxNative.Unmarshal(m, b)
}
func (m *UnifiedOrderWxNative) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderWxNative.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderWxNative) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderWxNative.Merge(m, src)
}
func (m *UnifiedOrderWxNative) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderWxNative.Size(m)
}
func (m *UnifiedOrderWxNative) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderWxNative.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderWxNative proto.InternalMessageInfo

func (m *UnifiedOrderWxNative) GetPayUrl() string {
	if m != nil {
		return m.PayUrl
	}
	return ""
}

// 百度支付信息
type UnifiedOrderBdPay struct {
	DealId               string                    `protobuf:"bytes,1,opt,name=dealId,proto3" json:"dealId"`
	AppKey               string                    `protobuf:"bytes,2,opt,name=appKey,proto3" json:"appKey"`
	TotalAmount          string                    `protobuf:"bytes,3,opt,name=totalAmount,proto3" json:"totalAmount"`
	TpOrderId            string                    `protobuf:"bytes,4,opt,name=tpOrderId,proto3" json:"tpOrderId"`
	NotifyUrl            string                    `protobuf:"bytes,5,opt,name=notifyUrl,proto3" json:"notifyUrl"`
	DealTitle            string                    `protobuf:"bytes,6,opt,name=dealTitle,proto3" json:"dealTitle"`
	SignFieldsRange      string                    `protobuf:"bytes,7,opt,name=signFieldsRange,proto3" json:"signFieldsRange"`
	RsaSign              string                    `protobuf:"bytes,8,opt,name=rsaSign,proto3" json:"rsaSign"`
	BizInfo              *UnifiedOrderBdPayBizInfo `protobuf:"bytes,9,opt,name=bizInfo,proto3" json:"bizInfo"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UnifiedOrderBdPay) Reset()         { *m = UnifiedOrderBdPay{} }
func (m *UnifiedOrderBdPay) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderBdPay) ProtoMessage()    {}
func (*UnifiedOrderBdPay) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{37}
}

func (m *UnifiedOrderBdPay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderBdPay.Unmarshal(m, b)
}
func (m *UnifiedOrderBdPay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderBdPay.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderBdPay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderBdPay.Merge(m, src)
}
func (m *UnifiedOrderBdPay) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderBdPay.Size(m)
}
func (m *UnifiedOrderBdPay) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderBdPay.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderBdPay proto.InternalMessageInfo

func (m *UnifiedOrderBdPay) GetDealId() string {
	if m != nil {
		return m.DealId
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetAppKey() string {
	if m != nil {
		return m.AppKey
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetTotalAmount() string {
	if m != nil {
		return m.TotalAmount
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetTpOrderId() string {
	if m != nil {
		return m.TpOrderId
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetNotifyUrl() string {
	if m != nil {
		return m.NotifyUrl
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetDealTitle() string {
	if m != nil {
		return m.DealTitle
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetSignFieldsRange() string {
	if m != nil {
		return m.SignFieldsRange
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetRsaSign() string {
	if m != nil {
		return m.RsaSign
	}
	return ""
}

func (m *UnifiedOrderBdPay) GetBizInfo() *UnifiedOrderBdPayBizInfo {
	if m != nil {
		return m.BizInfo
	}
	return nil
}

type UnifiedOrderBdPayBizInfo struct {
	TpData               *BizInfoTpData `protobuf:"bytes,1,opt,name=tpData,proto3" json:"tpData"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UnifiedOrderBdPayBizInfo) Reset()         { *m = UnifiedOrderBdPayBizInfo{} }
func (m *UnifiedOrderBdPayBizInfo) String() string { return proto.CompactTextString(m) }
func (*UnifiedOrderBdPayBizInfo) ProtoMessage()    {}
func (*UnifiedOrderBdPayBizInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{38}
}

func (m *UnifiedOrderBdPayBizInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Unmarshal(m, b)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Marshal(b, m, deterministic)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedOrderBdPayBizInfo.Merge(m, src)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_Size() int {
	return xxx_messageInfo_UnifiedOrderBdPayBizInfo.Size(m)
}
func (m *UnifiedOrderBdPayBizInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedOrderBdPayBizInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedOrderBdPayBizInfo proto.InternalMessageInfo

func (m *UnifiedOrderBdPayBizInfo) GetTpData() *BizInfoTpData {
	if m != nil {
		return m.TpData
	}
	return nil
}

type BizInfoTpData struct {
	DealId               string   `protobuf:"bytes,1,opt,name=dealId,proto3" json:"dealId"`
	AppKey               string   `protobuf:"bytes,2,opt,name=appKey,proto3" json:"appKey"`
	TotalAmount          string   `protobuf:"bytes,3,opt,name=totalAmount,proto3" json:"totalAmount"`
	TpOrderId            string   `protobuf:"bytes,4,opt,name=tpOrderId,proto3" json:"tpOrderId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BizInfoTpData) Reset()         { *m = BizInfoTpData{} }
func (m *BizInfoTpData) String() string { return proto.CompactTextString(m) }
func (*BizInfoTpData) ProtoMessage()    {}
func (*BizInfoTpData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{39}
}

func (m *BizInfoTpData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BizInfoTpData.Unmarshal(m, b)
}
func (m *BizInfoTpData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BizInfoTpData.Marshal(b, m, deterministic)
}
func (m *BizInfoTpData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BizInfoTpData.Merge(m, src)
}
func (m *BizInfoTpData) XXX_Size() int {
	return xxx_messageInfo_BizInfoTpData.Size(m)
}
func (m *BizInfoTpData) XXX_DiscardUnknown() {
	xxx_messageInfo_BizInfoTpData.DiscardUnknown(m)
}

var xxx_messageInfo_BizInfoTpData proto.InternalMessageInfo

func (m *BizInfoTpData) GetDealId() string {
	if m != nil {
		return m.DealId
	}
	return ""
}

func (m *BizInfoTpData) GetAppKey() string {
	if m != nil {
		return m.AppKey
	}
	return ""
}

func (m *BizInfoTpData) GetTotalAmount() string {
	if m != nil {
		return m.TotalAmount
	}
	return ""
}

func (m *BizInfoTpData) GetTpOrderId() string {
	if m != nil {
		return m.TpOrderId
	}
	return ""
}

type StopGroupRequest struct {
	//拼团活动ID
	GroupId int32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	//商品skuid
	SkuId int32 `protobuf:"varint,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	//是否模拟成团 1 是 0 否
	IsMock               int32    `protobuf:"varint,3,opt,name=is_mock,json=isMock,proto3" json:"is_mock"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopGroupRequest) Reset()         { *m = StopGroupRequest{} }
func (m *StopGroupRequest) String() string { return proto.CompactTextString(m) }
func (*StopGroupRequest) ProtoMessage()    {}
func (*StopGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{40}
}

func (m *StopGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopGroupRequest.Unmarshal(m, b)
}
func (m *StopGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopGroupRequest.Marshal(b, m, deterministic)
}
func (m *StopGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopGroupRequest.Merge(m, src)
}
func (m *StopGroupRequest) XXX_Size() int {
	return xxx_messageInfo_StopGroupRequest.Size(m)
}
func (m *StopGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StopGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StopGroupRequest proto.InternalMessageInfo

func (m *StopGroupRequest) GetGroupId() int32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *StopGroupRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *StopGroupRequest) GetIsMock() int32 {
	if m != nil {
		return m.IsMock
	}
	return 0
}

//某个活动下某个商品被用户购买了多少件请求参数
type GetUserOrderCountRequest struct {
	//用户id userId
	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	//用户 openId
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId"`
	//拼团活动id
	Gid int32 `protobuf:"varint,3,opt,name=gid,proto3" json:"gid"`
	//商品skuId
	SkuId int32 `protobuf:"varint,4,opt,name=skuId,proto3" json:"skuId"`
	//渠道id 1阿闻本地 5阿闻商城
	ChannelId            int32    `protobuf:"varint,5,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOrderCountRequest) Reset()         { *m = GetUserOrderCountRequest{} }
func (m *GetUserOrderCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderCountRequest) ProtoMessage()    {}
func (*GetUserOrderCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{41}
}

func (m *GetUserOrderCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderCountRequest.Unmarshal(m, b)
}
func (m *GetUserOrderCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderCountRequest.Marshal(b, m, deterministic)
}
func (m *GetUserOrderCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderCountRequest.Merge(m, src)
}
func (m *GetUserOrderCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderCountRequest.Size(m)
}
func (m *GetUserOrderCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderCountRequest proto.InternalMessageInfo

func (m *GetUserOrderCountRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *GetUserOrderCountRequest) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetUserOrderCountRequest) GetGid() int32 {
	if m != nil {
		return m.Gid
	}
	return 0
}

func (m *GetUserOrderCountRequest) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func (m *GetUserOrderCountRequest) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

//某个活动下某个商品被用户购买了多少件返回参数
type GetUserOrderCountResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//买了多少件
	Data                 int32    `protobuf:"varint,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOrderCountResponse) Reset()         { *m = GetUserOrderCountResponse{} }
func (m *GetUserOrderCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderCountResponse) ProtoMessage()    {}
func (*GetUserOrderCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{42}
}

func (m *GetUserOrderCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderCountResponse.Unmarshal(m, b)
}
func (m *GetUserOrderCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderCountResponse.Marshal(b, m, deterministic)
}
func (m *GetUserOrderCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderCountResponse.Merge(m, src)
}
func (m *GetUserOrderCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderCountResponse.Size(m)
}
func (m *GetUserOrderCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderCountResponse proto.InternalMessageInfo

func (m *GetUserOrderCountResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetUserOrderCountResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetUserOrderCountResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetUserOrderCountResponse) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

//获取拼团信息请求参数
type GetPinOrderMainInfoRequest struct {
	//团号
	PinOrderSn           string   `protobuf:"bytes,1,opt,name=pinOrderSn,proto3" json:"pinOrderSn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPinOrderMainInfoRequest) Reset()         { *m = GetPinOrderMainInfoRequest{} }
func (m *GetPinOrderMainInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetPinOrderMainInfoRequest) ProtoMessage()    {}
func (*GetPinOrderMainInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{43}
}

func (m *GetPinOrderMainInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinOrderMainInfoRequest.Unmarshal(m, b)
}
func (m *GetPinOrderMainInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinOrderMainInfoRequest.Marshal(b, m, deterministic)
}
func (m *GetPinOrderMainInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinOrderMainInfoRequest.Merge(m, src)
}
func (m *GetPinOrderMainInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetPinOrderMainInfoRequest.Size(m)
}
func (m *GetPinOrderMainInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinOrderMainInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinOrderMainInfoRequest proto.InternalMessageInfo

func (m *GetPinOrderMainInfoRequest) GetPinOrderSn() string {
	if m != nil {
		return m.PinOrderSn
	}
	return ""
}

//获取拼团信息返回参数
type GetPinOrderMainInfoResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//错误信息
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error"`
	//买了多少件
	Data                 *PinOrderMainData `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPinOrderMainInfoResponse) Reset()         { *m = GetPinOrderMainInfoResponse{} }
func (m *GetPinOrderMainInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetPinOrderMainInfoResponse) ProtoMessage()    {}
func (*GetPinOrderMainInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{44}
}

func (m *GetPinOrderMainInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinOrderMainInfoResponse.Unmarshal(m, b)
}
func (m *GetPinOrderMainInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinOrderMainInfoResponse.Marshal(b, m, deterministic)
}
func (m *GetPinOrderMainInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinOrderMainInfoResponse.Merge(m, src)
}
func (m *GetPinOrderMainInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetPinOrderMainInfoResponse.Size(m)
}
func (m *GetPinOrderMainInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinOrderMainInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinOrderMainInfoResponse proto.InternalMessageInfo

func (m *GetPinOrderMainInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPinOrderMainInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GetPinOrderMainInfoResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *GetPinOrderMainInfoResponse) GetData() *PinOrderMainData {
	if m != nil {
		return m.Data
	}
	return nil
}

//拼团信息，当前只返回了两个字段 后续如果有需要 往这里面加字段
type PinOrderMainData struct {
	//拼团活动ID
	GroupBuyId int32 `protobuf:"varint,1,opt,name=groupBuyId,proto3" json:"groupBuyId"`
	//拼团结束时间 = 拼团开始时间+团活动的有效时间
	EndTime string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`
	//拼团的skuId
	SkuId                int32    `protobuf:"varint,3,opt,name=skuId,proto3" json:"skuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PinOrderMainData) Reset()         { *m = PinOrderMainData{} }
func (m *PinOrderMainData) String() string { return proto.CompactTextString(m) }
func (*PinOrderMainData) ProtoMessage()    {}
func (*PinOrderMainData) Descriptor() ([]byte, []int) {
	return fileDescriptor_5a661fdd1a714d8f, []int{45}
}

func (m *PinOrderMainData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PinOrderMainData.Unmarshal(m, b)
}
func (m *PinOrderMainData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PinOrderMainData.Marshal(b, m, deterministic)
}
func (m *PinOrderMainData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PinOrderMainData.Merge(m, src)
}
func (m *PinOrderMainData) XXX_Size() int {
	return xxx_messageInfo_PinOrderMainData.Size(m)
}
func (m *PinOrderMainData) XXX_DiscardUnknown() {
	xxx_messageInfo_PinOrderMainData.DiscardUnknown(m)
}

var xxx_messageInfo_PinOrderMainData proto.InternalMessageInfo

func (m *PinOrderMainData) GetGroupBuyId() int32 {
	if m != nil {
		return m.GroupBuyId
	}
	return 0
}

func (m *PinOrderMainData) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PinOrderMainData) GetSkuId() int32 {
	if m != nil {
		return m.SkuId
	}
	return 0
}

func init() {
	proto.RegisterType((*BaseResponse)(nil), "ap.BaseResponse")
	proto.RegisterType((*CancelPinOrderResponse)(nil), "ap.CancelPinOrderResponse")
	proto.RegisterType((*OrderPayNotifyRequest)(nil), "ap.OrderPayNotifyRequest")
	proto.RegisterType((*GetGroupListRequest)(nil), "ap.GetGroupListRequest")
	proto.RegisterType((*GetGroupListResponse)(nil), "ap.GetGroupListResponse")
	proto.RegisterType((*GetGroupDetailRequest)(nil), "ap.GetGroupDetailRequest")
	proto.RegisterType((*GetGroupDetailResponse)(nil), "ap.GetGroupDetailResponse")
	proto.RegisterType((*GroupDetail)(nil), "ap.GroupDetail")
	proto.RegisterType((*GroupMembers)(nil), "ap.GroupMembers")
	proto.RegisterType((*GetGroupParticipantRequest)(nil), "ap.GetGroupParticipantRequest")
	proto.RegisterType((*GetGroupParticipantResponse)(nil), "ap.GetGroupParticipantResponse")
	proto.RegisterType((*Participant)(nil), "ap.Participant")
	proto.RegisterType((*CreateGroupOrderRequest)(nil), "ap.CreateGroupOrderRequest")
	proto.RegisterType((*GroupOrderProduct)(nil), "ap.GroupOrderProduct")
	proto.RegisterType((*CreateGroupOrderResponse)(nil), "ap.CreateGroupOrderResponse")
	proto.RegisterType((*GroupOrderPayRequest)(nil), "ap.GroupOrderPayRequest")
	proto.RegisterType((*CancelGroupOrderRequest)(nil), "ap.CancelGroupOrderRequest")
	proto.RegisterType((*SetOrderSubscribeMessageRequest)(nil), "ap.SetOrderSubscribeMessageRequest")
	proto.RegisterType((*GetPinGroupProductOrderStaticsRequest)(nil), "ap.GetPinGroupProductOrderStaticsRequest")
	proto.RegisterType((*GetPinGroupProductOrderStaticsResponse)(nil), "ap.GetPinGroupProductOrderStaticsResponse")
	proto.RegisterType((*PinGroupProductOrderStatics)(nil), "ap.PinGroupProductOrderStatics")
	proto.RegisterType((*PinOrderPayRequest)(nil), "ap.PinOrderPayRequest")
	proto.RegisterType((*GroupOrderListResponse)(nil), "ap.GroupOrderListResponse")
	proto.RegisterType((*PinGroupOrderListInfo)(nil), "ap.PinGroupOrderListInfo")
	proto.RegisterType((*GetCanParticipantOrderListResponse)(nil), "ap.GetCanParticipantOrderListResponse")
	proto.RegisterType((*CanParticipantOrder)(nil), "ap.CanParticipantOrder")
	proto.RegisterType((*NewSubscribeMessageRequest)(nil), "ap.NewSubscribeMessageRequest")
	proto.RegisterType((*WxSubscribeMessage)(nil), "ap.WxSubscribeMessage")
	proto.RegisterType((*SubscribeMessageListRequest)(nil), "ap.SubscribeMessageListRequest")
	proto.RegisterType((*SubscribeMessageListResponse)(nil), "ap.SubscribeMessageListResponse")
	proto.RegisterType((*OrderSubscribeMessage)(nil), "ap.OrderSubscribeMessage")
	proto.RegisterType((*PinOrderRefundRequest)(nil), "ap.PinOrderRefundRequest")
	proto.RegisterType((*RefundNotifyRequest)(nil), "ap.RefundNotifyRequest")
	proto.RegisterType((*UnifiedOrderReturnData)(nil), "ap.UnifiedOrderReturnData")
	proto.RegisterType((*UnifiedOrderResponse)(nil), "ap.UnifiedOrderResponse")
	proto.RegisterType((*UnifiedOrderResponseData)(nil), "ap.UnifiedOrderResponse.data")
	proto.RegisterType((*UnifiedOrderWxJsApp)(nil), "ap.UnifiedOrderWxJsApp")
	proto.RegisterType((*UnifiedOrderWxNative)(nil), "ap.UnifiedOrderWxNative")
	proto.RegisterType((*UnifiedOrderBdPay)(nil), "ap.UnifiedOrderBdPay")
	proto.RegisterType((*UnifiedOrderBdPayBizInfo)(nil), "ap.UnifiedOrderBdPayBizInfo")
	proto.RegisterType((*BizInfoTpData)(nil), "ap.BizInfoTpData")
	proto.RegisterType((*StopGroupRequest)(nil), "ap.StopGroupRequest")
	proto.RegisterType((*GetUserOrderCountRequest)(nil), "ap.GetUserOrderCountRequest")
	proto.RegisterType((*GetUserOrderCountResponse)(nil), "ap.GetUserOrderCountResponse")
	proto.RegisterType((*GetPinOrderMainInfoRequest)(nil), "ap.GetPinOrderMainInfoRequest")
	proto.RegisterType((*GetPinOrderMainInfoResponse)(nil), "ap.GetPinOrderMainInfoResponse")
	proto.RegisterType((*PinOrderMainData)(nil), "ap.PinOrderMainData")
}

func init() { proto.RegisterFile("ap/activity_pin_model.proto", fileDescriptor_5a661fdd1a714d8f) }

var fileDescriptor_5a661fdd1a714d8f = []byte{
	// 2721 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x5a, 0xcb, 0x8f, 0x24, 0x47,
	0xd1, 0x57, 0xf5, 0xbb, 0xa3, 0x7b, 0x66, 0x67, 0x6a, 0x5e, 0x35, 0xfb, 0xf8, 0x76, 0x5c, 0xfe,
	0xfc, 0x69, 0xfd, 0x21, 0xaf, 0x25, 0xaf, 0x8d, 0x31, 0xe2, 0xb2, 0xbb, 0x36, 0xcb, 0xd8, 0xec,
	0x30, 0xea, 0xde, 0xc5, 0x27, 0x54, 0xca, 0xee, 0xca, 0xed, 0xcd, 0x9d, 0xee, 0xac, 0xa2, 0xb2,
	0xda, 0x33, 0x6d, 0x09, 0x01, 0x47, 0xe0, 0x02, 0x37, 0xe4, 0x03, 0x82, 0x93, 0x4f, 0x70, 0x00,
	0x84, 0x40, 0x1c, 0xf8, 0x93, 0x7c, 0x47, 0x08, 0xa1, 0x88, 0xcc, 0xac, 0xae, 0xaa, 0xee, 0x9e,
	0xd9, 0xb5, 0xc7, 0xe6, 0xd6, 0x19, 0x91, 0x95, 0x19, 0x19, 0xcf, 0x5f, 0x64, 0x36, 0x5c, 0x63,
	0xf1, 0xeb, 0x6c, 0x98, 0x8a, 0x8f, 0x44, 0x3a, 0x0b, 0x62, 0x21, 0x83, 0x49, 0x14, 0xf2, 0xf1,
	0xed, 0x38, 0x89, 0xd2, 0xc8, 0xad, 0xb0, 0xd8, 0xef, 0x41, 0xf7, 0x1e, 0x53, 0xbc, 0xc7, 0x55,
	0x1c, 0x49, 0xc5, 0x5d, 0x17, 0x6a, 0xc3, 0x28, 0xe4, 0x9e, 0x73, 0xe0, 0xdc, 0xaa, 0xf7, 0xe8,
	0xb7, 0xeb, 0x41, 0x73, 0xc2, 0x95, 0x62, 0x23, 0xee, 0x55, 0x0e, 0x9c, 0x5b, 0xed, 0x9e, 0x1d,
	0xba, 0xdb, 0x50, 0xe7, 0x49, 0x12, 0x25, 0x5e, 0x95, 0xe8, 0x7a, 0xe0, 0xff, 0x08, 0x76, 0xef,
	0x33, 0x39, 0xe4, 0xe3, 0x63, 0x21, 0xbf, 0x97, 0x84, 0x3c, 0xb9, 0xcc, 0xd5, 0xdd, 0x9b, 0xd0,
	0x19, 0x26, 0x9c, 0xa5, 0x3c, 0x48, 0xc5, 0x84, 0x7b, 0xb5, 0x03, 0xe7, 0x56, 0xb5, 0x07, 0x9a,
	0xf4, 0x48, 0x4c, 0xb8, 0xff, 0x1b, 0x07, 0x76, 0x68, 0xdb, 0x63, 0x36, 0x3b, 0x8a, 0x52, 0xf1,
	0x64, 0xd6, 0xe3, 0x3f, 0x9c, 0x72, 0x95, 0xba, 0xfb, 0xd0, 0x8a, 0x90, 0x11, 0x28, 0x49, 0x22,
	0xb4, 0x7b, 0x4d, 0x1a, 0xf7, 0xa5, 0xbb, 0x03, 0x8d, 0x98, 0xcd, 0x90, 0xa1, 0x85, 0xa8, 0xc7,
	0x6c, 0xd6, 0x97, 0xf8, 0x05, 0x92, 0x51, 0x6b, 0x24, 0x45, 0xbd, 0xd7, 0x8c, 0xd9, 0xec, 0x21,
	0xca, 0x6d, 0x58, 0x99, 0x10, 0x6d, 0x62, 0xa1, 0x04, 0xee, 0x0d, 0x00, 0x64, 0xb1, 0x49, 0x34,
	0x95, 0xa9, 0x57, 0xa7, 0xef, 0xda, 0x31, 0x9b, 0xdd, 0x25, 0x82, 0xff, 0x89, 0x03, 0x5b, 0x0f,
	0x78, 0xfa, 0x20, 0x89, 0xa6, 0xf1, 0x77, 0x85, 0x4a, 0xad, 0x78, 0x7b, 0xd0, 0x8c, 0x62, 0x2e,
	0x03, 0x11, 0x1a, 0xe9, 0x1a, 0x38, 0x3c, 0x0c, 0x91, 0x31, 0x55, 0x3c, 0x41, 0x86, 0x96, 0xae,
	0x81, 0xc3, 0xc3, 0x10, 0xa5, 0x56, 0x27, 0x53, 0xa4, 0x1b, 0x15, 0xa9, 0x93, 0xe9, 0x61, 0xa8,
	0xf7, 0x1f, 0xf1, 0x40, 0xc8, 0x90, 0x9f, 0x91, 0x70, 0xb4, 0xff, 0x88, 0x1f, 0x22, 0xc1, 0xbd,
	0x06, 0x34, 0x08, 0x94, 0xf8, 0x98, 0x1b, 0xe9, 0x5a, 0x48, 0xe8, 0x8b, 0x8f, 0xb9, 0xff, 0x5b,
	0x07, 0xb6, 0x8b, 0xc2, 0x5d, 0xae, 0xed, 0xd2, 0x28, 0x65, 0xe3, 0x60, 0x48, 0x9a, 0xd1, 0x92,
	0x01, 0x91, 0xee, 0x23, 0xc5, 0x7d, 0x19, 0x6a, 0x21, 0x4b, 0x99, 0x57, 0x3f, 0xa8, 0xde, 0xea,
	0xbc, 0x71, 0xe5, 0x36, 0x8b, 0x6f, 0x93, 0x24, 0xef, 0xf2, 0x94, 0x89, 0x71, 0x8f, 0x98, 0xfe,
	0x04, 0x76, 0xac, 0x84, 0x86, 0x6e, 0x14, 0x78, 0x00, 0x5d, 0xf4, 0xf1, 0x92, 0x8d, 0x21, 0x36,
	0x6e, 0xd8, 0x97, 0xab, 0x35, 0xb9, 0x0f, 0x2d, 0xa1, 0x02, 0xf5, 0x94, 0x25, 0x99, 0xa1, 0x85,
	0xea, 0xe3, 0xd0, 0xff, 0xa9, 0x03, 0xbb, 0xe5, 0xfd, 0x2e, 0x51, 0x27, 0xf6, 0xc8, 0xa8, 0x8c,
	0x95, 0x47, 0xfe, 0xac, 0x09, 0x9d, 0x1c, 0xf5, 0x39, 0x4e, 0x3a, 0x77, 0x8d, 0x4a, 0xde, 0x35,
	0x76, 0xa1, 0xa1, 0x52, 0x96, 0x4e, 0x95, 0x39, 0xa5, 0x19, 0xa1, 0xcb, 0xa8, 0x94, 0x25, 0x69,
	0xde, 0x9f, 0xdb, 0x44, 0x21, 0x8f, 0xde, 0x87, 0x16, 0x97, 0xa1, 0x66, 0xd6, 0xf5, 0xa9, 0xb8,
	0x0c, 0x89, 0x55, 0x8a, 0xc7, 0x86, 0x96, 0x64, 0x1e, 0x8f, 0xee, 0x5b, 0xb0, 0x36, 0x42, 0xd1,
	0x83, 0x09, 0x9f, 0x0c, 0x78, 0xa2, 0xbc, 0x26, 0x19, 0x77, 0x23, 0x3b, 0xe9, 0x43, 0x4d, 0xef,
	0x75, 0x47, 0xb9, 0x91, 0xfb, 0x26, 0xec, 0x4e, 0xd8, 0x59, 0x10, 0xb3, 0x24, 0x15, 0x43, 0x11,
	0x33, 0x99, 0x06, 0x72, 0x8a, 0x2c, 0xaf, 0x45, 0x92, 0x6f, 0x4f, 0xd8, 0xd9, 0xf1, 0x9c, 0x79,
	0x44, 0x3c, 0xf7, 0x35, 0x70, 0x97, 0x7c, 0xd1, 0xa6, 0x2f, 0x36, 0xe3, 0x85, 0xe9, 0x2f, 0x41,
	0x37, 0x4e, 0xa2, 0x70, 0x3a, 0x4c, 0x03, 0xc9, 0x26, 0xdc, 0x03, 0x92, 0xbe, 0x63, 0x68, 0x47,
	0x6c, 0xc2, 0xdd, 0x97, 0x61, 0xcd, 0x4e, 0x11, 0x13, 0xb4, 0x6a, 0x87, 0xe6, 0xd8, 0xef, 0x0e,
	0x91, 0x86, 0x21, 0xa5, 0x62, 0x3e, 0xd4, 0x8b, 0x74, 0x69, 0x42, 0x0b, 0x09, 0xb4, 0x02, 0xc6,
	0x9b, 0x90, 0x41, 0x9c, 0x88, 0x21, 0xf7, 0xd6, 0x28, 0x5f, 0xb5, 0x62, 0x21, 0x8f, 0x71, 0x8c,
	0xcb, 0x4f, 0x58, 0x72, 0x22, 0xe4, 0xc8, 0x4c, 0x58, 0xa7, 0x09, 0x5d, 0x43, 0xd4, 0x93, 0xfe,
	0x07, 0x3a, 0x42, 0x51, 0xfe, 0x7e, 0xca, 0x59, 0xe8, 0x5d, 0xd1, 0x11, 0x2d, 0xd4, 0xb1, 0x90,
	0xdf, 0xe1, 0x2c, 0xc4, 0x1d, 0x28, 0x73, 0xd0, 0xf6, 0x1b, 0x7a, 0x7b, 0x24, 0xd0, 0xf6, 0x57,
	0xa1, 0x15, 0x47, 0x49, 0x9a, 0x30, 0x91, 0x7a, 0x9b, 0x9a, 0x67, 0xc7, 0xee, 0xab, 0xb0, 0x69,
	0x57, 0x9d, 0x3b, 0x93, 0x4b, 0x93, 0xd6, 0x63, 0xbd, 0xb8, 0x75, 0xa8, 0xff, 0xcf, 0x4d, 0xcd,
	0xd6, 0xdb, 0xa2, 0xa9, 0x57, 0xcc, 0xd4, 0x63, 0xbb, 0xec, 0x01, 0x74, 0x85, 0x0a, 0x58, 0x18,
	0x06, 0x64, 0x52, 0x6f, 0x5b, 0x07, 0xba, 0x50, 0x77, 0xc3, 0x90, 0x4c, 0x8e, 0xfe, 0x26, 0x54,
	0xf0, 0x91, 0x48, 0xd2, 0x29, 0x1b, 0x7b, 0x3b, 0xf6, 0x40, 0xdf, 0xd7, 0x04, 0x5c, 0x40, 0xfb,
	0xcc, 0x60, 0x3a, 0x43, 0x1f, 0xde, 0xd5, 0x0b, 0x10, 0xed, 0xde, 0x74, 0x76, 0x48, 0x47, 0x16,
	0x0a, 0xf3, 0x48, 0x1c, 0x49, 0x6f, 0x4f, 0x27, 0x31, 0xa1, 0xee, 0xd3, 0x18, 0xdd, 0x55, 0x46,
	0xa7, 0xda, 0x21, 0x3d, 0xed, 0xae, 0x32, 0x3a, 0x25, 0x6f, 0xf4, 0x61, 0x8d, 0xa2, 0x3c, 0x10,
	0x93, 0x51, 0x30, 0x4d, 0xc6, 0xde, 0xbe, 0x36, 0x39, 0x11, 0x0f, 0x27, 0xa3, 0xc7, 0xc9, 0xd8,
	0x7d, 0x05, 0xd6, 0xd5, 0x74, 0x38, 0xe4, 0x2a, 0xdb, 0xe0, 0x2a, 0x4d, 0x5a, 0x33, 0x54, 0xb3,
	0xcb, 0x4b, 0x56, 0x48, 0x13, 0x51, 0xd7, 0x48, 0x8a, 0x0e, 0xd1, 0xfa, 0x3a, 0xac, 0x4c, 0x9c,
	0xea, 0x69, 0x22, 0xf4, 0xae, 0xeb, 0x73, 0xc4, 0x42, 0x92, 0x1a, 0x74, 0x40, 0x1a, 0x27, 0xbd,
	0xa1, 0x03, 0x52, 0x8f, 0xb0, 0x48, 0x74, 0xf3, 0xd1, 0xb1, 0xba, 0x3a, 0x14, 0x8c, 0x5f, 0x29,
	0x19, 0x3f, 0x97, 0xf0, 0xaa, 0x85, 0x84, 0x97, 0xf7, 0x8a, 0x5a, 0xc9, 0x2b, 0x4a, 0x21, 0x5d,
	0x2f, 0x87, 0xb4, 0xff, 0x73, 0x07, 0xae, 0xda, 0x94, 0x98, 0x8b, 0x41, 0x9b, 0x87, 0xe7, 0xb9,
	0xc7, 0xc9, 0xe7, 0x9e, 0x7d, 0x68, 0x65, 0x8a, 0xa8, 0x90, 0x97, 0x37, 0x47, 0x46, 0x0b, 0x5f,
	0xa4, 0x62, 0x7d, 0xea, 0xc0, 0xb5, 0xa5, 0xc2, 0xfc, 0x97, 0x0b, 0x57, 0x5e, 0x16, 0x9d, 0xc5,
	0x7f, 0xef, 0x40, 0x27, 0x47, 0xfd, 0x2a, 0x4d, 0xea, 0x42, 0x2d, 0x9d, 0xc5, 0x56, 0x79, 0xf4,
	0xfb, 0xc2, 0xcc, 0xed, 0xff, 0xb3, 0x0e, 0x7b, 0xf7, 0x69, 0x48, 0xca, 0x35, 0x58, 0x4e, 0xdb,
	0xb8, 0x94, 0x92, 0x9c, 0x72, 0x4a, 0x5a, 0x9a, 0x59, 0x2a, 0x4b, 0x33, 0x4b, 0x39, 0xd8, 0xab,
	0x1a, 0xd2, 0xe5, 0x82, 0x7d, 0x75, 0x2d, 0xa8, 0xbd, 0x70, 0x2d, 0xa8, 0xaf, 0xaa, 0x05, 0x37,
	0x00, 0x86, 0x4f, 0x99, 0x94, 0x7c, 0x8c, 0x42, 0x34, 0xf4, 0x81, 0x0c, 0xa5, 0xa4, 0xdd, 0x66,
	0x49, 0xbb, 0x39, 0x43, 0xb6, 0x56, 0x1b, 0xb2, 0xbd, 0xda, 0x90, 0xb0, 0x02, 0xd6, 0x75, 0x4a,
	0xb0, 0x2e, 0x57, 0xa3, 0xbb, 0xe7, 0xd5, 0xe8, 0xb5, 0x62, 0x8d, 0x7e, 0x19, 0xd6, 0x06, 0x53,
	0x25, 0x24, 0x66, 0xb4, 0x67, 0x2a, 0x92, 0x54, 0x64, 0xda, 0xbd, 0xae, 0x25, 0xbe, 0xaf, 0x22,
	0x89, 0xee, 0xa0, 0xa6, 0x83, 0x89, 0x48, 0xf5, 0x94, 0x2b, 0xda, 0x1d, 0x34, 0xc9, 0x4e, 0xd0,
	0x41, 0xa0, 0x0b, 0xd5, 0x46, 0x2e, 0x08, 0x74, 0x99, 0xa2, 0x30, 0x9d, 0x19, 0xf6, 0xa6, 0x0d,
	0xd3, 0x99, 0x66, 0xce, 0x91, 0x87, 0x5b, 0x40, 0x1e, 0xdf, 0x98, 0x97, 0xe0, 0xb1, 0x50, 0x58,
	0x52, 0x30, 0x82, 0x76, 0x32, 0x74, 0xa0, 0xa1, 0xbc, 0x9e, 0x91, 0x55, 0x66, 0x44, 0xa6, 0xa8,
	0x3f, 0xf5, 0x34, 0xa2, 0x74, 0xb2, 0xad, 0xf5, 0x87, 0x43, 0xad, 0xa8, 0xf3, 0x8a, 0xcb, 0xff,
	0xc2, 0xba, 0xf6, 0x48, 0xc2, 0xef, 0x18, 0x15, 0xbb, 0x5a, 0x1d, 0x91, 0xe9, 0x1a, 0x1e, 0xcd,
	0x62, 0xee, 0x7f, 0x5a, 0x85, 0xcd, 0x05, 0x01, 0x56, 0xa5, 0xb6, 0x1c, 0x8e, 0xa0, 0x05, 0x2b,
	0xba, 0x14, 0x18, 0x1a, 0xae, 0x87, 0x85, 0x27, 0x66, 0x09, 0x97, 0x69, 0x50, 0x80, 0xec, 0x1d,
	0x4d, 0xec, 0x97, 0x97, 0x21, 0x8f, 0xa9, 0x3d, 0x07, 0x1c, 0xa9, 0x5f, 0x04, 0x47, 0x1a, 0x25,
	0x38, 0xb2, 0x80, 0x38, 0x9a, 0x24, 0x6c, 0x11, 0x71, 0x14, 0x30, 0x4b, 0xcb, 0x98, 0xd2, 0x62,
	0x96, 0x82, 0x9d, 0xdb, 0x8b, 0x76, 0x36, 0x91, 0x06, 0xf9, 0x82, 0x86, 0x46, 0xc1, 0x18, 0x36,
	0xbc, 0x8e, 0x36, 0xca, 0x84, 0x9d, 0x99, 0xe8, 0x7b, 0x07, 0xba, 0xc3, 0xa7, 0x62, 0x1c, 0x1a,
	0x45, 0x7b, 0xdd, 0xf3, 0xdc, 0xa0, 0x30, 0xd5, 0xff, 0x89, 0x03, 0xde, 0x62, 0x9a, 0xba, 0xc4,
	0xec, 0x5f, 0x46, 0xdb, 0xb5, 0x32, 0xda, 0xf6, 0xc7, 0xb0, 0x9d, 0x93, 0x92, 0xe5, 0x3b, 0xce,
	0x01, 0x4b, 0x82, 0x4c, 0x82, 0x76, 0xaf, 0x39, 0x60, 0xc9, 0x7d, 0x14, 0xa2, 0xbc, 0x68, 0x65,
	0x59, 0xb3, 0x62, 0xb3, 0x4a, 0x35, 0x9f, 0x55, 0xfc, 0x3e, 0xec, 0xe9, 0x06, 0x7b, 0x31, 0x2d,
	0x5f, 0xdc, 0x18, 0xec, 0x42, 0x23, 0xe1, 0x0c, 0x23, 0xdc, 0x74, 0x40, 0x7a, 0xe4, 0xff, 0xcc,
	0x81, 0x9b, 0x7d, 0x9e, 0xea, 0x69, 0xd3, 0x81, 0x1a, 0x26, 0x62, 0xc0, 0x1f, 0x6a, 0xbd, 0xbc,
	0x50, 0x83, 0x65, 0x65, 0xae, 0xac, 0xea, 0x61, 0x8b, 0x55, 0xcb, 0x56, 0xa6, 0xda, 0xbc, 0x32,
	0xf9, 0x7f, 0x76, 0xe0, 0x95, 0x07, 0x3c, 0x3d, 0x36, 0x30, 0xc9, 0x18, 0x5a, 0xef, 0x91, 0xb2,
	0x54, 0x0c, 0xd5, 0xbc, 0x0c, 0xe5, 0xea, 0x84, 0x31, 0x72, 0xbe, 0x72, 0x6c, 0x83, 0x8e, 0x50,
	0x13, 0x91, 0x26, 0x5c, 0xaf, 0xc3, 0x3c, 0xb1, 0x9b, 0x46, 0x28, 0x97, 0xe9, 0xdf, 0x86, 0xc6,
	0x13, 0xc1, 0xc7, 0xa1, 0x32, 0x3d, 0xd9, 0x4d, 0xaa, 0xe6, 0xe7, 0xc8, 0x62, 0xa6, 0xfb, 0xbf,
	0x73, 0xe0, 0xff, 0x2e, 0x12, 0xfb, 0x12, 0xdd, 0xf2, 0x4e, 0xa1, 0x73, 0xbc, 0x50, 0x4a, 0x8d,
	0x41, 0xfe, 0xe6, 0xc0, 0xb5, 0x73, 0x66, 0xb9, 0xb7, 0xe0, 0x0a, 0x65, 0x74, 0x22, 0x12, 0xb6,
	0x31, 0x32, 0x96, 0xc9, 0xee, 0x37, 0xc1, 0x33, 0x78, 0xf8, 0x5d, 0xa1, 0x52, 0x21, 0x87, 0xe9,
	0x63, 0x65, 0x3f, 0xd1, 0xda, 0x5e, 0xc9, 0x77, 0xdf, 0x84, 0x1d, 0xc3, 0xeb, 0xf1, 0xc2, 0x5e,
	0xda, 0x18, 0xcb, 0x99, 0xbe, 0x04, 0xd7, 0x5e, 0x29, 0xe5, 0x62, 0xec, 0x0b, 0x38, 0xe5, 0x0d,
	0x80, 0x34, 0x61, 0x52, 0xe9, 0xa4, 0x6d, 0x1c, 0x81, 0x28, 0x54, 0x02, 0xfe, 0x88, 0x9d, 0x7f,
	0x16, 0x62, 0x97, 0x7e, 0x1b, 0x72, 0x07, 0x9a, 0x21, 0xb5, 0xf3, 0xe8, 0x68, 0x98, 0xed, 0xf6,
	0xf3, 0x26, 0xcc, 0xf6, 0x3c, 0x94, 0x4f, 0xa2, 0x9e, 0x9d, 0x59, 0x46, 0xa2, 0xf5, 0x32, 0x12,
	0xf5, 0xff, 0x55, 0x87, 0x9d, 0xa5, 0x6b, 0xb8, 0xeb, 0x50, 0x31, 0x75, 0xab, 0xda, 0xab, 0x88,
	0xf0, 0x39, 0x32, 0xd0, 0x97, 0x72, 0x5b, 0x20, 0x39, 0x0f, 0x6d, 0x19, 0xd0, 0x28, 0x0b, 0x90,
	0x64, 0xea, 0x40, 0x09, 0x57, 0x36, 0xcb, 0xb8, 0xf2, 0x2b, 0xb9, 0x16, 0x98, 0x57, 0x79, 0x58,
	0x51, 0xe5, 0xa9, 0xb2, 0x76, 0x9e, 0xa3, 0x3c, 0x77, 0x2f, 0x2a, 0xcf, 0x6b, 0xe7, 0xdd, 0x16,
	0xac, 0x5f, 0x74, 0x5b, 0x70, 0x65, 0xc9, 0x6d, 0x01, 0x7a, 0x1b, 0xe9, 0x7e, 0x83, 0x98, 0x7a,
	0x50, 0xe8, 0x89, 0x37, 0x8b, 0x3d, 0x71, 0xa9, 0x11, 0x70, 0x17, 0xae, 0x70, 0xd0, 0x53, 0xd8,
	0x2c, 0xc8, 0x8c, 0xba, 0x65, 0x3c, 0x85, 0xcd, 0xde, 0x33, 0x76, 0x2d, 0x42, 0xae, 0xed, 0x32,
	0xe4, 0x2a, 0xb7, 0xca, 0x3b, 0x8b, 0xad, 0xf2, 0x1c, 0x37, 0xec, 0x16, 0x70, 0xc3, 0x6b, 0xb0,
	0x65, 0x70, 0x53, 0xc1, 0x59, 0xf7, 0x48, 0x84, 0x0d, 0xcd, 0x3a, 0x9e, 0x57, 0xe2, 0xbf, 0x38,
	0xe0, 0x3f, 0xe0, 0xe9, 0x7d, 0x26, 0x73, 0x5e, 0xf0, 0xe5, 0xc4, 0xef, 0xd7, 0xb2, 0xfc, 0x8b,
	0xc1, 0xbb, 0x87, 0xc1, 0xbb, 0x64, 0x5b, 0x9d, 0x77, 0x2f, 0x8e, 0xdb, 0x7f, 0x57, 0x60, 0x6b,
	0xc9, 0xe7, 0xc5, 0x16, 0xc2, 0x39, 0xe7, 0x6e, 0xa7, 0x52, 0x6a, 0x4a, 0x56, 0x47, 0x4a, 0xf5,
	0x85, 0x23, 0xa5, 0x76, 0x4e, 0xd3, 0x94, 0xcb, 0x04, 0xf5, 0xf3, 0x32, 0x41, 0xa3, 0x98, 0x09,
	0xae, 0x03, 0x66, 0x1a, 0x0b, 0x86, 0x6d, 0x47, 0x25, 0xa4, 0x46, 0xc2, 0xe5, 0xdc, 0xd4, 0x5a,
	0xc8, 0x4d, 0x2b, 0xfc, 0xa2, 0xbd, 0xdc, 0x2f, 0x16, 0x9a, 0x4c, 0x28, 0x43, 0x05, 0xff, 0x13,
	0x07, 0xae, 0x1e, 0xf1, 0xd3, 0x55, 0xd8, 0xe7, 0x9c, 0xc7, 0x83, 0x17, 0x07, 0x3d, 0xaf, 0x43,
	0xf3, 0xf4, 0x2c, 0x10, 0xf2, 0x49, 0x64, 0xbc, 0x67, 0x17, 0xbd, 0xe7, 0xc3, 0xb3, 0x85, 0xcd,
	0x1b, 0xa7, 0x67, 0x98, 0xbb, 0xfd, 0x43, 0x70, 0x17, 0xb9, 0xe4, 0x54, 0x7c, 0x12, 0x8f, 0x31,
	0x74, 0xb3, 0x96, 0x04, 0x2c, 0x29, 0x07, 0xae, 0x2a, 0x39, 0x70, 0xf5, 0x07, 0x07, 0xae, 0x95,
	0x57, 0xca, 0x3f, 0x43, 0x7c, 0x9e, 0x83, 0x16, 0xef, 0x6f, 0xaa, 0xe7, 0xde, 0xdf, 0xd4, 0x8a,
	0xf7, 0x37, 0xe5, 0x43, 0xd4, 0xcb, 0x87, 0xf0, 0x7f, 0xe5, 0xc0, 0xf5, 0xe5, 0x02, 0x5f, 0x62,
	0x30, 0xbf, 0x56, 0x08, 0x66, 0xaa, 0xc4, 0xcb, 0xa1, 0xb0, 0x86, 0x51, 0xbf, 0xb6, 0x8f, 0x4c,
	0x0b, 0x36, 0x99, 0x57, 0xd9, 0x3a, 0x55, 0xd9, 0xbc, 0x3a, 0x2b, 0x45, 0x75, 0x96, 0x4e, 0x5e,
	0x5d, 0x69, 0xbe, 0xda, 0xea, 0x5b, 0x9b, 0xc5, 0xcb, 0xb9, 0x77, 0xa8, 0xfe, 0x9b, 0xae, 0xe0,
	0xc9, 0x54, 0x86, 0xcf, 0x0d, 0x94, 0xfc, 0x7f, 0x38, 0xb0, 0xa5, 0xbf, 0x29, 0x3e, 0x9c, 0x5d,
	0x0c, 0xb1, 0x5e, 0x81, 0x75, 0xc2, 0x4d, 0x6c, 0x98, 0x8a, 0x48, 0x06, 0x32, 0x32, 0x67, 0x5d,
	0xcb, 0x51, 0x8f, 0x22, 0xac, 0x5f, 0x09, 0xad, 0x6f, 0x1f, 0xc7, 0xb4, 0xab, 0x74, 0x35, 0x51,
	0xbf, 0x8f, 0xa1, 0xc6, 0x12, 0xae, 0x74, 0xd3, 0x64, 0x5e, 0xd6, 0x12, 0xae, 0xa8, 0x69, 0xba,
	0x09, 0x1d, 0x64, 0x59, 0xcb, 0x9a, 0xc3, 0x27, 0x5c, 0x19, 0xed, 0xfb, 0x7f, 0x75, 0x60, 0xf7,
	0xb1, 0x14, 0x4f, 0x04, 0x0f, 0x8d, 0x06, 0xd2, 0x69, 0x22, 0xdf, 0xc5, 0x0c, 0xbc, 0x0d, 0x75,
	0x16, 0xc7, 0x87, 0x59, 0xe7, 0x4e, 0x03, 0xcc, 0xa0, 0x32, 0x92, 0x43, 0xde, 0x4f, 0x13, 0x9b,
	0x41, 0xed, 0x18, 0x7d, 0x28, 0x66, 0xc3, 0x13, 0xdc, 0xa9, 0x6a, 0x5f, 0xf8, 0x68, 0xa8, 0x39,
	0xb3, 0xbe, 0x18, 0xc9, 0xdc, 0xdb, 0x1f, 0x0e, 0x71, 0x3d, 0x25, 0x46, 0xf2, 0x91, 0xbd, 0x6c,
	0xc3, 0xd2, 0x6e, 0xc6, 0xd8, 0x76, 0xa0, 0xcd, 0xfa, 0x29, 0x9b, 0xc4, 0x26, 0x1d, 0xce, 0x09,
	0xfe, 0x67, 0x55, 0xd8, 0x2e, 0x8a, 0xfe, 0xb9, 0xdc, 0xfb, 0xed, 0x39, 0xaa, 0xac, 0x52, 0x63,
	0x70, 0x03, 0x7d, 0x79, 0xd9, 0xc2, 0xb7, 0xd1, 0x93, 0x0b, 0xc8, 0xf2, 0xdc, 0x87, 0xd5, 0xec,
	0xc5, 0x73, 0x7e, 0x8f, 0x48, 0x2f, 0x9e, 0xb3, 0x98, 0x5f, 0xfd, 0x53, 0x45, 0x87, 0x8f, 0xfb,
	0x16, 0xb4, 0x4e, 0xcf, 0x82, 0x67, 0x8a, 0xc5, 0x82, 0xe4, 0xed, 0xbc, 0x71, 0x75, 0x71, 0x7b,
	0x6b, 0x92, 0x5e, 0xf3, 0xf4, 0xec, 0x7d, 0x9c, 0x3a, 0x0f, 0x92, 0x2c, 0xb3, 0xe8, 0x20, 0x39,
	0x0c, 0xdd, 0xb7, 0xa0, 0x7d, 0x7a, 0x16, 0x48, 0x96, 0x8a, 0x8f, 0xb8, 0x39, 0x91, 0x57, 0x5e,
	0xf2, 0xc3, 0xb3, 0x23, 0xe2, 0xf7, 0x5a, 0xa7, 0xe6, 0x97, 0x7b, 0x87, 0x3e, 0x7b, 0xa6, 0x02,
	0x16, 0xc7, 0xa6, 0x43, 0xda, 0x5b, 0xfc, 0xec, 0x7d, 0x75, 0x37, 0x8e, 0xb5, 0x18, 0x77, 0xe3,
	0x18, 0x35, 0x3d, 0x60, 0xf2, 0xc4, 0x18, 0x8e, 0x7e, 0x63, 0x87, 0xc9, 0xc6, 0xe2, 0x51, 0xc2,
	0x42, 0x7e, 0x14, 0xd9, 0x4b, 0xd2, 0x39, 0xc5, 0x7d, 0x07, 0xd6, 0x06, 0xf6, 0x8a, 0x93, 0x12,
	0x7a, 0x93, 0x36, 0xdb, 0x29, 0x6f, 0x76, 0x2f, 0xc4, 0x9e, 0xa5, 0x33, 0xd0, 0x23, 0x4a, 0xea,
	0x1f, 0xc0, 0xd6, 0x12, 0x71, 0xd0, 0xb6, 0xcf, 0xf0, 0x47, 0xe6, 0xaa, 0x76, 0x88, 0xce, 0x45,
	0x3f, 0x1f, 0x27, 0x63, 0xeb, 0xac, 0x76, 0xec, 0xdf, 0x2e, 0x7a, 0x8f, 0x55, 0x09, 0xe2, 0xaa,
	0x98, 0xcd, 0xf0, 0x0b, 0x73, 0xc7, 0xac, 0x47, 0xfe, 0xdf, 0x2b, 0xb0, 0xb9, 0x20, 0x1f, 0xce,
	0x0e, 0x39, 0x1b, 0x67, 0x5b, 0x9b, 0x11, 0xd2, 0x59, 0x1c, 0x7f, 0xc0, 0x67, 0x36, 0xf1, 0xeb,
	0x91, 0x7b, 0x60, 0x60, 0xcd, 0xdd, 0x79, 0x38, 0xb7, 0x7b, 0x79, 0x12, 0x39, 0xbd, 0x6e, 0x44,
	0x0e, 0x43, 0xdb, 0x2a, 0x64, 0x04, 0xe4, 0x4a, 0x4a, 0x35, 0x28, 0xa0, 0x81, 0x0f, 0x19, 0x01,
	0xb9, 0xb8, 0xff, 0x23, 0x91, 0x8e, 0x2d, 0x7e, 0x98, 0x13, 0xb0, 0x55, 0xc5, 0xd0, 0xfa, 0x36,
	0x35, 0xdf, 0x3d, 0x26, 0x47, 0xdc, 0xc0, 0x88, 0x32, 0x19, 0x35, 0x9a, 0x28, 0x46, 0xe1, 0xda,
	0x32, 0x09, 0x45, 0x0f, 0xdd, 0xaf, 0x43, 0x73, 0x20, 0x3e, 0x46, 0x6b, 0x10, 0x72, 0xe8, 0xbc,
	0x71, 0x7d, 0xa9, 0xdd, 0xee, 0xe9, 0x39, 0x3d, 0x3b, 0xd9, 0x7f, 0x0f, 0xbc, 0x55, 0x93, 0xdc,
	0x57, 0xa1, 0x91, 0xc6, 0xe8, 0xdf, 0x26, 0x02, 0x36, 0x71, 0x49, 0xc3, 0x7c, 0x44, 0x8c, 0x9e,
	0x99, 0xe0, 0xff, 0x18, 0xd6, 0x0a, 0x8c, 0xaf, 0x5a, 0xff, 0xfe, 0x0f, 0x60, 0xa3, 0x9f, 0x46,
	0x31, 0x75, 0x8b, 0x39, 0x00, 0x90, 0xbd, 0xd3, 0xe8, 0x9c, 0x93, 0xbd, 0xd3, 0x14, 0x5f, 0x95,
	0xb3, 0xfb, 0x94, 0x3d, 0x68, 0x0a, 0x15, 0x4c, 0xa2, 0xe1, 0x89, 0x6d, 0x14, 0x85, 0x7a, 0x18,
	0x0d, 0x4f, 0xfc, 0x5f, 0x3a, 0xe0, 0x3d, 0xe0, 0xd4, 0xf8, 0xcf, 0xfb, 0x78, 0xbb, 0xcf, 0x2e,
	0x18, 0x38, 0x64, 0xcf, 0x6a, 0xc0, 0xd1, 0x2e, 0x18, 0x58, 0x51, 0x02, 0x19, 0x1b, 0x50, 0x1d,
	0x09, 0x7b, 0x5f, 0x83, 0x3f, 0xe7, 0xb7, 0x3b, 0xb5, 0x95, 0xb7, 0x3b, 0xf5, 0xd2, 0xed, 0x8e,
	0xaf, 0x60, 0x7f, 0x89, 0x44, 0x97, 0x88, 0x24, 0xdc, 0xdc, 0xb5, 0x4c, 0xdd, 0xc0, 0x85, 0x6f,
	0xd1, 0x7b, 0x99, 0x2d, 0xcb, 0x0f, 0x99, 0x90, 0xe4, 0x4e, 0xf3, 0x4b, 0xac, 0x79, 0x29, 0x5d,
	0x52, 0x96, 0x7f, 0xa1, 0x5f, 0xb8, 0x16, 0x3f, 0xbf, 0x44, 0xa9, 0x6f, 0x15, 0x2e, 0x93, 0xb6,
	0xcd, 0x4d, 0x44, 0xb6, 0x1f, 0x79, 0xad, 0x3e, 0xcb, 0x00, 0x36, 0xca, 0x9c, 0x0b, 0xaf, 0xe1,
	0x3c, 0xb0, 0xb8, 0xdf, 0x4a, 0x63, 0xdb, 0x80, 0xcc, 0x84, 0xd5, 0x9c, 0x09, 0x07, 0x0d, 0xfa,
	0x87, 0xd2, 0x9d, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xaa, 0xcd, 0xec, 0x5a, 0xc0, 0x24, 0x00,
	0x00,
}
