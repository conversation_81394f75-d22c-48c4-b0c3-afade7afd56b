// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ctc/contentCenter.proto

package ctc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ContentResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContentResponse) Reset()         { *m = ContentResponse{} }
func (m *ContentResponse) String() string { return proto.CompactTextString(m) }
func (*ContentResponse) ProtoMessage()    {}
func (*ContentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{0}
}

func (m *ContentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContentResponse.Unmarshal(m, b)
}
func (m *ContentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContentResponse.Marshal(b, m, deterministic)
}
func (m *ContentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContentResponse.Merge(m, src)
}
func (m *ContentResponse) XXX_Size() int {
	return xxx_messageInfo_ContentResponse.Size(m)
}
func (m *ContentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ContentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ContentResponse proto.InternalMessageInfo

func (m *ContentResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ContentResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 文章保存
type ArticleSaveRequest struct {
	// 模板id
	TemplateId int32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 一级分类id
	CategoryFirst int32 `protobuf:"varint,2,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	// 二级分类id
	CategorySecond int32 `protobuf:"varint,3,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	// 三级分类id
	CategoryThird int32 `protobuf:"varint,4,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title"`
	// 内容
	Content string `protobuf:"bytes,8,opt,name=content,proto3" json:"content"`
	// 医生code，接口中doctor_code
	DoctorCode string `protobuf:"bytes,9,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 执业证书编号
	DoctorCertNo string `protobuf:"bytes,10,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 标签集合
	TagJson string `protobuf:"bytes,11,opt,name=tag_json,json=tagJson,proto3" json:"tag_json"`
	// 分发渠道，1-百度小程序
	DisChannel []int32 `protobuf:"varint,12,rep,packed,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	// 是否显示广告，1-显示，0-不显示
	IsShowAds int32 `protobuf:"varint,13,opt,name=is_show_ads,json=isShowAds,proto3" json:"is_show_ads"`
	// 在线问诊入口图片地址
	OnlineAskUrl string `protobuf:"bytes,14,opt,name=online_ask_url,json=onlineAskUrl,proto3" json:"online_ask_url"`
	// 文章类型：1-图文，2-视频
	ArticleType int32 `protobuf:"varint,16,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 文章id
	ArticleId int32 `protobuf:"varint,17,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 状态，1-保存发布，2-保存草稿
	Status int32 `protobuf:"varint,18,opt,name=status,proto3" json:"status"`
	// 主体id
	OrgId                int32    `protobuf:"varint,19,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleSaveRequest) Reset()         { *m = ArticleSaveRequest{} }
func (m *ArticleSaveRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleSaveRequest) ProtoMessage()    {}
func (*ArticleSaveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{1}
}

func (m *ArticleSaveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleSaveRequest.Unmarshal(m, b)
}
func (m *ArticleSaveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleSaveRequest.Marshal(b, m, deterministic)
}
func (m *ArticleSaveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleSaveRequest.Merge(m, src)
}
func (m *ArticleSaveRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleSaveRequest.Size(m)
}
func (m *ArticleSaveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleSaveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleSaveRequest proto.InternalMessageInfo

func (m *ArticleSaveRequest) GetTemplateId() int32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *ArticleSaveRequest) GetCategoryFirst() int32 {
	if m != nil {
		return m.CategoryFirst
	}
	return 0
}

func (m *ArticleSaveRequest) GetCategorySecond() int32 {
	if m != nil {
		return m.CategorySecond
	}
	return 0
}

func (m *ArticleSaveRequest) GetCategoryThird() int32 {
	if m != nil {
		return m.CategoryThird
	}
	return 0
}

func (m *ArticleSaveRequest) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ArticleSaveRequest) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *ArticleSaveRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleSaveRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ArticleSaveRequest) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ArticleSaveRequest) GetDoctorCertNo() string {
	if m != nil {
		return m.DoctorCertNo
	}
	return ""
}

func (m *ArticleSaveRequest) GetTagJson() string {
	if m != nil {
		return m.TagJson
	}
	return ""
}

func (m *ArticleSaveRequest) GetDisChannel() []int32 {
	if m != nil {
		return m.DisChannel
	}
	return nil
}

func (m *ArticleSaveRequest) GetIsShowAds() int32 {
	if m != nil {
		return m.IsShowAds
	}
	return 0
}

func (m *ArticleSaveRequest) GetOnlineAskUrl() string {
	if m != nil {
		return m.OnlineAskUrl
	}
	return ""
}

func (m *ArticleSaveRequest) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

func (m *ArticleSaveRequest) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleSaveRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ArticleSaveRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleTagData struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleTagData) Reset()         { *m = ArticleTagData{} }
func (m *ArticleTagData) String() string { return proto.CompactTextString(m) }
func (*ArticleTagData) ProtoMessage()    {}
func (*ArticleTagData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{2}
}

func (m *ArticleTagData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleTagData.Unmarshal(m, b)
}
func (m *ArticleTagData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleTagData.Marshal(b, m, deterministic)
}
func (m *ArticleTagData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleTagData.Merge(m, src)
}
func (m *ArticleTagData) XXX_Size() int {
	return xxx_messageInfo_ArticleTagData.Size(m)
}
func (m *ArticleTagData) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleTagData.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleTagData proto.InternalMessageInfo

// 文章列表-后台
type ArticleListRequest struct {
	Title          string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	DoctorName     string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	ArticleType    int32  `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	CategoryFirst  int32  `protobuf:"varint,4,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	CategorySecond int32  `protobuf:"varint,5,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	CategoryThird  int32  `protobuf:"varint,6,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	DisChannel     int32  `protobuf:"varint,7,opt,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	PageIndex      int32  `protobuf:"varint,8,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	PageSize       int32  `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Status         int32  `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	// 标签数据过滤，json字符串 [{"name":"年龄","tags":"幼年"}]
	Tags string `protobuf:"bytes,11,opt,name=tags,proto3" json:"tags"`
	//随机值
	RandNum int32 `protobuf:"varint,12,opt,name=randNum,proto3" json:"randNum"`
	// 主体id
	OrgId                int32    `protobuf:"varint,13,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleListRequest) Reset()         { *m = ArticleListRequest{} }
func (m *ArticleListRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleListRequest) ProtoMessage()    {}
func (*ArticleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{3}
}

func (m *ArticleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleListRequest.Unmarshal(m, b)
}
func (m *ArticleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleListRequest.Marshal(b, m, deterministic)
}
func (m *ArticleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleListRequest.Merge(m, src)
}
func (m *ArticleListRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleListRequest.Size(m)
}
func (m *ArticleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleListRequest proto.InternalMessageInfo

func (m *ArticleListRequest) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleListRequest) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ArticleListRequest) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

func (m *ArticleListRequest) GetCategoryFirst() int32 {
	if m != nil {
		return m.CategoryFirst
	}
	return 0
}

func (m *ArticleListRequest) GetCategorySecond() int32 {
	if m != nil {
		return m.CategorySecond
	}
	return 0
}

func (m *ArticleListRequest) GetCategoryThird() int32 {
	if m != nil {
		return m.CategoryThird
	}
	return 0
}

func (m *ArticleListRequest) GetDisChannel() int32 {
	if m != nil {
		return m.DisChannel
	}
	return 0
}

func (m *ArticleListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ArticleListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ArticleListRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ArticleListRequest) GetTags() string {
	if m != nil {
		return m.Tags
	}
	return ""
}

func (m *ArticleListRequest) GetRandNum() int32 {
	if m != nil {
		return m.RandNum
	}
	return 0
}

func (m *ArticleListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleListResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ArticleListData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	TotalCount           int64              `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ArticleListResponse) Reset()         { *m = ArticleListResponse{} }
func (m *ArticleListResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleListResponse) ProtoMessage()    {}
func (*ArticleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{4}
}

func (m *ArticleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleListResponse.Unmarshal(m, b)
}
func (m *ArticleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleListResponse.Marshal(b, m, deterministic)
}
func (m *ArticleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleListResponse.Merge(m, src)
}
func (m *ArticleListResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleListResponse.Size(m)
}
func (m *ArticleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleListResponse proto.InternalMessageInfo

func (m *ArticleListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleListResponse) GetData() []*ArticleListData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ArticleListResponse) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type ArticleListData struct {
	// 文章id
	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章分类，1-图文，2-视频
	ArticleType int32 `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 医生姓名
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 最后发布时间
	LastPublishTime string `protobuf:"bytes,6,opt,name=last_publish_time,json=lastPublishTime,proto3" json:"last_publish_time"`
	// 最后更新时间
	UpdatedAt string `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 最后操作人
	LastOperator string `protobuf:"bytes,8,opt,name=last_operator,json=lastOperator,proto3" json:"last_operator"`
	// 文章状态，0-未发布，1-已发布，2-下架
	Status               string   `protobuf:"bytes,9,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleListData) Reset()         { *m = ArticleListData{} }
func (m *ArticleListData) String() string { return proto.CompactTextString(m) }
func (*ArticleListData) ProtoMessage()    {}
func (*ArticleListData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{5}
}

func (m *ArticleListData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleListData.Unmarshal(m, b)
}
func (m *ArticleListData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleListData.Marshal(b, m, deterministic)
}
func (m *ArticleListData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleListData.Merge(m, src)
}
func (m *ArticleListData) XXX_Size() int {
	return xxx_messageInfo_ArticleListData.Size(m)
}
func (m *ArticleListData) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleListData.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleListData proto.InternalMessageInfo

func (m *ArticleListData) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleListData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleListData) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

func (m *ArticleListData) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ArticleListData) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ArticleListData) GetLastPublishTime() string {
	if m != nil {
		return m.LastPublishTime
	}
	return ""
}

func (m *ArticleListData) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *ArticleListData) GetLastOperator() string {
	if m != nil {
		return m.LastOperator
	}
	return ""
}

func (m *ArticleListData) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type ArticleSearchConditionRequest struct {
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from"`
	// 主体id
	OrgId                int32    `protobuf:"varint,2,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleSearchConditionRequest) Reset()         { *m = ArticleSearchConditionRequest{} }
func (m *ArticleSearchConditionRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleSearchConditionRequest) ProtoMessage()    {}
func (*ArticleSearchConditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{6}
}

func (m *ArticleSearchConditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleSearchConditionRequest.Unmarshal(m, b)
}
func (m *ArticleSearchConditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleSearchConditionRequest.Marshal(b, m, deterministic)
}
func (m *ArticleSearchConditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleSearchConditionRequest.Merge(m, src)
}
func (m *ArticleSearchConditionRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleSearchConditionRequest.Size(m)
}
func (m *ArticleSearchConditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleSearchConditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleSearchConditionRequest proto.InternalMessageInfo

func (m *ArticleSearchConditionRequest) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *ArticleSearchConditionRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleSearchConditionResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	ArticleType          []*ArticleArr           `protobuf:"bytes,3,rep,name=article_type,json=articleType,proto3" json:"article_type"`
	DisChannel           []*ArticleArr           `protobuf:"bytes,4,rep,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	Status               []*ArticleArr           `protobuf:"bytes,5,rep,name=status,proto3" json:"status"`
	ArticleCategory      []*FirstArticleCategory `protobuf:"bytes,6,rep,name=article_category,json=articleCategory,proto3" json:"article_category"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ArticleSearchConditionResponse) Reset()         { *m = ArticleSearchConditionResponse{} }
func (m *ArticleSearchConditionResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleSearchConditionResponse) ProtoMessage()    {}
func (*ArticleSearchConditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{7}
}

func (m *ArticleSearchConditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleSearchConditionResponse.Unmarshal(m, b)
}
func (m *ArticleSearchConditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleSearchConditionResponse.Marshal(b, m, deterministic)
}
func (m *ArticleSearchConditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleSearchConditionResponse.Merge(m, src)
}
func (m *ArticleSearchConditionResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleSearchConditionResponse.Size(m)
}
func (m *ArticleSearchConditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleSearchConditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleSearchConditionResponse proto.InternalMessageInfo

func (m *ArticleSearchConditionResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleSearchConditionResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleSearchConditionResponse) GetArticleType() []*ArticleArr {
	if m != nil {
		return m.ArticleType
	}
	return nil
}

func (m *ArticleSearchConditionResponse) GetDisChannel() []*ArticleArr {
	if m != nil {
		return m.DisChannel
	}
	return nil
}

func (m *ArticleSearchConditionResponse) GetStatus() []*ArticleArr {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *ArticleSearchConditionResponse) GetArticleCategory() []*FirstArticleCategory {
	if m != nil {
		return m.ArticleCategory
	}
	return nil
}

type ArticleArr struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleArr) Reset()         { *m = ArticleArr{} }
func (m *ArticleArr) String() string { return proto.CompactTextString(m) }
func (*ArticleArr) ProtoMessage()    {}
func (*ArticleArr) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{8}
}

func (m *ArticleArr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleArr.Unmarshal(m, b)
}
func (m *ArticleArr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleArr.Marshal(b, m, deterministic)
}
func (m *ArticleArr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleArr.Merge(m, src)
}
func (m *ArticleArr) XXX_Size() int {
	return xxx_messageInfo_ArticleArr.Size(m)
}
func (m *ArticleArr) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleArr.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleArr proto.InternalMessageInfo

func (m *ArticleArr) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ArticleArr) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 小程序列表
type ArticleListMiniResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ArticleListMiniData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	TotalCount           int64                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ArticleListMiniResponse) Reset()         { *m = ArticleListMiniResponse{} }
func (m *ArticleListMiniResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleListMiniResponse) ProtoMessage()    {}
func (*ArticleListMiniResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{9}
}

func (m *ArticleListMiniResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleListMiniResponse.Unmarshal(m, b)
}
func (m *ArticleListMiniResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleListMiniResponse.Marshal(b, m, deterministic)
}
func (m *ArticleListMiniResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleListMiniResponse.Merge(m, src)
}
func (m *ArticleListMiniResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleListMiniResponse.Size(m)
}
func (m *ArticleListMiniResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleListMiniResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleListMiniResponse proto.InternalMessageInfo

func (m *ArticleListMiniResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleListMiniResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleListMiniResponse) GetData() []*ArticleListMiniData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ArticleListMiniResponse) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type ArticleListMiniData struct {
	// 文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章分类，1-图文，2-视频
	ArticleType int32 `protobuf:"varint,3,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 医生姓名
	DoctorName string `protobuf:"bytes,4,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 医生职称
	DoctorPositionName string `protobuf:"bytes,5,opt,name=doctor_position_name,json=doctorPositionName,proto3" json:"doctor_position_name"`
	// 医生所属医院
	Hospital string `protobuf:"bytes,6,opt,name=hospital,proto3" json:"hospital"`
	// 医生执业证书编号
	DoctorCertNo string `protobuf:"bytes,7,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,8,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,9,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 医生头像
	HeadImg string `protobuf:"bytes,10,opt,name=head_img,json=headImg,proto3" json:"head_img"`
	// 最后发布日期
	PublishTime string `protobuf:"bytes,11,opt,name=publish_time,json=publishTime,proto3" json:"publish_time"`
	//更新时间
	UpdatedAt            string   `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleListMiniData) Reset()         { *m = ArticleListMiniData{} }
func (m *ArticleListMiniData) String() string { return proto.CompactTextString(m) }
func (*ArticleListMiniData) ProtoMessage()    {}
func (*ArticleListMiniData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{10}
}

func (m *ArticleListMiniData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleListMiniData.Unmarshal(m, b)
}
func (m *ArticleListMiniData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleListMiniData.Marshal(b, m, deterministic)
}
func (m *ArticleListMiniData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleListMiniData.Merge(m, src)
}
func (m *ArticleListMiniData) XXX_Size() int {
	return xxx_messageInfo_ArticleListMiniData.Size(m)
}
func (m *ArticleListMiniData) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleListMiniData.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleListMiniData proto.InternalMessageInfo

func (m *ArticleListMiniData) GetArticleId() int64 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleListMiniData) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleListMiniData) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

func (m *ArticleListMiniData) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ArticleListMiniData) GetDoctorPositionName() string {
	if m != nil {
		return m.DoctorPositionName
	}
	return ""
}

func (m *ArticleListMiniData) GetHospital() string {
	if m != nil {
		return m.Hospital
	}
	return ""
}

func (m *ArticleListMiniData) GetDoctorCertNo() string {
	if m != nil {
		return m.DoctorCertNo
	}
	return ""
}

func (m *ArticleListMiniData) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ArticleListMiniData) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *ArticleListMiniData) GetHeadImg() string {
	if m != nil {
		return m.HeadImg
	}
	return ""
}

func (m *ArticleListMiniData) GetPublishTime() string {
	if m != nil {
		return m.PublishTime
	}
	return ""
}

func (m *ArticleListMiniData) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

// 文章详情
type ArticleDetailRequest struct {
	ArticleId            int32    `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	From                 string   `protobuf:"bytes,2,opt,name=from,proto3" json:"from"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleDetailRequest) Reset()         { *m = ArticleDetailRequest{} }
func (m *ArticleDetailRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleDetailRequest) ProtoMessage()    {}
func (*ArticleDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{11}
}

func (m *ArticleDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleDetailRequest.Unmarshal(m, b)
}
func (m *ArticleDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleDetailRequest.Marshal(b, m, deterministic)
}
func (m *ArticleDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleDetailRequest.Merge(m, src)
}
func (m *ArticleDetailRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleDetailRequest.Size(m)
}
func (m *ArticleDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleDetailRequest proto.InternalMessageInfo

func (m *ArticleDetailRequest) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleDetailRequest) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

type ArticleDetailResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 文章详情信息
	Detail               *ArticleDetail `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ArticleDetailResponse) Reset()         { *m = ArticleDetailResponse{} }
func (m *ArticleDetailResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleDetailResponse) ProtoMessage()    {}
func (*ArticleDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{12}
}

func (m *ArticleDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleDetailResponse.Unmarshal(m, b)
}
func (m *ArticleDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleDetailResponse.Marshal(b, m, deterministic)
}
func (m *ArticleDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleDetailResponse.Merge(m, src)
}
func (m *ArticleDetailResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleDetailResponse.Size(m)
}
func (m *ArticleDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleDetailResponse proto.InternalMessageInfo

func (m *ArticleDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleDetailResponse) GetDetail() *ArticleDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

type ArticleDetail struct {
	// 模板id
	TemplateId int32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id"`
	// 一级分类id
	CategoryFirst int32 `protobuf:"varint,2,opt,name=category_first,json=categoryFirst,proto3" json:"category_first"`
	// 二级分类id
	CategorySecond int32 `protobuf:"varint,3,opt,name=category_second,json=categorySecond,proto3" json:"category_second"`
	// 三级分类id
	CategoryThird int32 `protobuf:"varint,4,opt,name=category_third,json=categoryThird,proto3" json:"category_third"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	// 标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title"`
	// 内容
	Content string `protobuf:"bytes,8,opt,name=content,proto3" json:"content"`
	// 医生code，接口中doctor_code
	DoctorCode string `protobuf:"bytes,9,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 执业证书编号
	DoctorCertNo string `protobuf:"bytes,10,opt,name=doctor_cert_no,json=doctorCertNo,proto3" json:"doctor_cert_no"`
	// 标签集合
	TagJson string `protobuf:"bytes,11,opt,name=tag_json,json=tagJson,proto3" json:"tag_json"`
	// 分发渠道，1-百度小程序,2-阿闻小程序，4-阿闻app
	DisChannel    int32   `protobuf:"varint,12,opt,name=dis_channel,json=disChannel,proto3" json:"dis_channel"`
	DisChannelArr []int32 `protobuf:"varint,24,rep,packed,name=dis_channel_arr,json=disChannelArr,proto3" json:"dis_channel_arr"`
	// 是否显示广告，1-显示，0-不显示
	IsShowAds int32 `protobuf:"varint,13,opt,name=is_show_ads,json=isShowAds,proto3" json:"is_show_ads"`
	// 在线问诊入口图片地址
	OnlineAskUrl string `protobuf:"bytes,14,opt,name=online_ask_url,json=onlineAskUrl,proto3" json:"online_ask_url"`
	// 文章类型：1-图文，2-视频
	ArticleType int32 `protobuf:"varint,16,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	// 文章id
	ArticleId int32 `protobuf:"varint,17,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 最后更新时间
	UpdatedAt string `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	//医生名称
	DoctorName string `protobuf:"bytes,20,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,21,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生头像
	DoctorImg string `protobuf:"bytes,22,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院名称
	HospitalName         string   `protobuf:"bytes,23,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleDetail) Reset()         { *m = ArticleDetail{} }
func (m *ArticleDetail) String() string { return proto.CompactTextString(m) }
func (*ArticleDetail) ProtoMessage()    {}
func (*ArticleDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{13}
}

func (m *ArticleDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleDetail.Unmarshal(m, b)
}
func (m *ArticleDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleDetail.Marshal(b, m, deterministic)
}
func (m *ArticleDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleDetail.Merge(m, src)
}
func (m *ArticleDetail) XXX_Size() int {
	return xxx_messageInfo_ArticleDetail.Size(m)
}
func (m *ArticleDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleDetail proto.InternalMessageInfo

func (m *ArticleDetail) GetTemplateId() int32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *ArticleDetail) GetCategoryFirst() int32 {
	if m != nil {
		return m.CategoryFirst
	}
	return 0
}

func (m *ArticleDetail) GetCategorySecond() int32 {
	if m != nil {
		return m.CategorySecond
	}
	return 0
}

func (m *ArticleDetail) GetCategoryThird() int32 {
	if m != nil {
		return m.CategoryThird
	}
	return 0
}

func (m *ArticleDetail) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ArticleDetail) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *ArticleDetail) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleDetail) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ArticleDetail) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ArticleDetail) GetDoctorCertNo() string {
	if m != nil {
		return m.DoctorCertNo
	}
	return ""
}

func (m *ArticleDetail) GetTagJson() string {
	if m != nil {
		return m.TagJson
	}
	return ""
}

func (m *ArticleDetail) GetDisChannel() int32 {
	if m != nil {
		return m.DisChannel
	}
	return 0
}

func (m *ArticleDetail) GetDisChannelArr() []int32 {
	if m != nil {
		return m.DisChannelArr
	}
	return nil
}

func (m *ArticleDetail) GetIsShowAds() int32 {
	if m != nil {
		return m.IsShowAds
	}
	return 0
}

func (m *ArticleDetail) GetOnlineAskUrl() string {
	if m != nil {
		return m.OnlineAskUrl
	}
	return ""
}

func (m *ArticleDetail) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

func (m *ArticleDetail) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleDetail) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *ArticleDetail) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ArticleDetail) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *ArticleDetail) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *ArticleDetail) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

// 文章发布、下架
type ArticleStatusRequest struct {
	ArticleId            int32    `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	Status               int32    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleStatusRequest) Reset()         { *m = ArticleStatusRequest{} }
func (m *ArticleStatusRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleStatusRequest) ProtoMessage()    {}
func (*ArticleStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{14}
}

func (m *ArticleStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleStatusRequest.Unmarshal(m, b)
}
func (m *ArticleStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleStatusRequest.Marshal(b, m, deterministic)
}
func (m *ArticleStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleStatusRequest.Merge(m, src)
}
func (m *ArticleStatusRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleStatusRequest.Size(m)
}
func (m *ArticleStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleStatusRequest proto.InternalMessageInfo

func (m *ArticleStatusRequest) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleStatusRequest) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 文章操作历史查询
type ArticleEditHistoryListRequest struct {
	// 文章id
	ArticleId int32 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 创建人id
	CreateId string `protobuf:"bytes,3,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 页
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 页大小
	PageSize             int32    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleEditHistoryListRequest) Reset()         { *m = ArticleEditHistoryListRequest{} }
func (m *ArticleEditHistoryListRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleEditHistoryListRequest) ProtoMessage()    {}
func (*ArticleEditHistoryListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{15}
}

func (m *ArticleEditHistoryListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleEditHistoryListRequest.Unmarshal(m, b)
}
func (m *ArticleEditHistoryListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleEditHistoryListRequest.Marshal(b, m, deterministic)
}
func (m *ArticleEditHistoryListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleEditHistoryListRequest.Merge(m, src)
}
func (m *ArticleEditHistoryListRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleEditHistoryListRequest.Size(m)
}
func (m *ArticleEditHistoryListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleEditHistoryListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleEditHistoryListRequest proto.InternalMessageInfo

func (m *ArticleEditHistoryListRequest) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleEditHistoryListRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ArticleEditHistoryListRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *ArticleEditHistoryListRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ArticleEditHistoryListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ArticleEditHistoryListResponse struct {
	Code    int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*ArticleEditRecord `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总条数
	TotalCount           int32    `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleEditHistoryListResponse) Reset()         { *m = ArticleEditHistoryListResponse{} }
func (m *ArticleEditHistoryListResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleEditHistoryListResponse) ProtoMessage()    {}
func (*ArticleEditHistoryListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{16}
}

func (m *ArticleEditHistoryListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleEditHistoryListResponse.Unmarshal(m, b)
}
func (m *ArticleEditHistoryListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleEditHistoryListResponse.Marshal(b, m, deterministic)
}
func (m *ArticleEditHistoryListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleEditHistoryListResponse.Merge(m, src)
}
func (m *ArticleEditHistoryListResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleEditHistoryListResponse.Size(m)
}
func (m *ArticleEditHistoryListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleEditHistoryListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleEditHistoryListResponse proto.InternalMessageInfo

func (m *ArticleEditHistoryListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleEditHistoryListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleEditHistoryListResponse) GetData() []*ArticleEditRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ArticleEditHistoryListResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type ArticleEditRecord struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 文章id
	ArticleId int32 `protobuf:"varint,2,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	// 操作类型 默认0,1- 创建并保存草稿，2-编辑并保存草稿，3-发布，4-编辑并发布，5-下架
	Type int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type"`
	// 操作内容
	OperationData string `protobuf:"bytes,4,opt,name=operation_data,json=operationData,proto3" json:"operation_data"`
	// 操作人编号
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 操作人名称
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 操作时间
	CreatedAt            string   `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleEditRecord) Reset()         { *m = ArticleEditRecord{} }
func (m *ArticleEditRecord) String() string { return proto.CompactTextString(m) }
func (*ArticleEditRecord) ProtoMessage()    {}
func (*ArticleEditRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{17}
}

func (m *ArticleEditRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleEditRecord.Unmarshal(m, b)
}
func (m *ArticleEditRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleEditRecord.Marshal(b, m, deterministic)
}
func (m *ArticleEditRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleEditRecord.Merge(m, src)
}
func (m *ArticleEditRecord) XXX_Size() int {
	return xxx_messageInfo_ArticleEditRecord.Size(m)
}
func (m *ArticleEditRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleEditRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleEditRecord proto.InternalMessageInfo

func (m *ArticleEditRecord) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ArticleEditRecord) GetArticleId() int32 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleEditRecord) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ArticleEditRecord) GetOperationData() string {
	if m != nil {
		return m.OperationData
	}
	return ""
}

func (m *ArticleEditRecord) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *ArticleEditRecord) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *ArticleEditRecord) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

// 文章推荐
type ArticleRecommendRequest struct {
	// 文章id
	ArticleId            int64    `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleRecommendRequest) Reset()         { *m = ArticleRecommendRequest{} }
func (m *ArticleRecommendRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleRecommendRequest) ProtoMessage()    {}
func (*ArticleRecommendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{18}
}

func (m *ArticleRecommendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleRecommendRequest.Unmarshal(m, b)
}
func (m *ArticleRecommendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleRecommendRequest.Marshal(b, m, deterministic)
}
func (m *ArticleRecommendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleRecommendRequest.Merge(m, src)
}
func (m *ArticleRecommendRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleRecommendRequest.Size(m)
}
func (m *ArticleRecommendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleRecommendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleRecommendRequest proto.InternalMessageInfo

func (m *ArticleRecommendRequest) GetArticleId() int64 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

type ArticleRecommendResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 总数
	TotalCount           int32               `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	Data                 []*ArticleRecommend `protobuf:"bytes,5,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ArticleRecommendResponse) Reset()         { *m = ArticleRecommendResponse{} }
func (m *ArticleRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleRecommendResponse) ProtoMessage()    {}
func (*ArticleRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{19}
}

func (m *ArticleRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleRecommendResponse.Unmarshal(m, b)
}
func (m *ArticleRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleRecommendResponse.Marshal(b, m, deterministic)
}
func (m *ArticleRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleRecommendResponse.Merge(m, src)
}
func (m *ArticleRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleRecommendResponse.Size(m)
}
func (m *ArticleRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleRecommendResponse proto.InternalMessageInfo

func (m *ArticleRecommendResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleRecommendResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleRecommendResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ArticleRecommendResponse) GetData() []*ArticleRecommend {
	if m != nil {
		return m.Data
	}
	return nil
}

type ArticleRecommend struct {
	// 文章id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 文章标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	// 文章封面
	Cover string `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover"`
	// 医生编码
	DoctorCode string `protobuf:"bytes,4,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	// 医生名称
	DoctorName string `protobuf:"bytes,5,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	// 证书
	Certificate string `protobuf:"bytes,6,opt,name=certificate,proto3" json:"certificate"`
	// 医院名称
	HospitalName string `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	// 医生职称
	DoctorLevel string `protobuf:"bytes,8,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	// 医生头像
	DoctorImg string `protobuf:"bytes,9,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	// 文章类型 1-图文，2-视频
	ArticleType          int32    `protobuf:"varint,10,opt,name=article_type,json=articleType,proto3" json:"article_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleRecommend) Reset()         { *m = ArticleRecommend{} }
func (m *ArticleRecommend) String() string { return proto.CompactTextString(m) }
func (*ArticleRecommend) ProtoMessage()    {}
func (*ArticleRecommend) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{20}
}

func (m *ArticleRecommend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleRecommend.Unmarshal(m, b)
}
func (m *ArticleRecommend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleRecommend.Marshal(b, m, deterministic)
}
func (m *ArticleRecommend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleRecommend.Merge(m, src)
}
func (m *ArticleRecommend) XXX_Size() int {
	return xxx_messageInfo_ArticleRecommend.Size(m)
}
func (m *ArticleRecommend) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleRecommend.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleRecommend proto.InternalMessageInfo

func (m *ArticleRecommend) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ArticleRecommend) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ArticleRecommend) GetCover() string {
	if m != nil {
		return m.Cover
	}
	return ""
}

func (m *ArticleRecommend) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ArticleRecommend) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ArticleRecommend) GetCertificate() string {
	if m != nil {
		return m.Certificate
	}
	return ""
}

func (m *ArticleRecommend) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

func (m *ArticleRecommend) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *ArticleRecommend) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *ArticleRecommend) GetArticleType() int32 {
	if m != nil {
		return m.ArticleType
	}
	return 0
}

// 类目新增编辑
type ArticleCategorySaveRequest struct {
	// 类目id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 类目名称
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// 上级id
	ParentId int32 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	// 是否可用 0 不可 , 1可用
	Valid int32 `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	// 创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 创建人名字
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 类目层级 1 一级 ， 2 二级 ， 3 三级
	Level int32 `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	// 主体id
	OrgId                int32    `protobuf:"varint,8,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCategorySaveRequest) Reset()         { *m = ArticleCategorySaveRequest{} }
func (m *ArticleCategorySaveRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleCategorySaveRequest) ProtoMessage()    {}
func (*ArticleCategorySaveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{21}
}

func (m *ArticleCategorySaveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategorySaveRequest.Unmarshal(m, b)
}
func (m *ArticleCategorySaveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategorySaveRequest.Marshal(b, m, deterministic)
}
func (m *ArticleCategorySaveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategorySaveRequest.Merge(m, src)
}
func (m *ArticleCategorySaveRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleCategorySaveRequest.Size(m)
}
func (m *ArticleCategorySaveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategorySaveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategorySaveRequest proto.InternalMessageInfo

func (m *ArticleCategorySaveRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ArticleCategorySaveRequest) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *ArticleCategorySaveRequest) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *ArticleCategorySaveRequest) GetValid() int32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func (m *ArticleCategorySaveRequest) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *ArticleCategorySaveRequest) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *ArticleCategorySaveRequest) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ArticleCategorySaveRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleCategorySaveResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 int32    `protobuf:"varint,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCategorySaveResponse) Reset()         { *m = ArticleCategorySaveResponse{} }
func (m *ArticleCategorySaveResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleCategorySaveResponse) ProtoMessage()    {}
func (*ArticleCategorySaveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{22}
}

func (m *ArticleCategorySaveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategorySaveResponse.Unmarshal(m, b)
}
func (m *ArticleCategorySaveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategorySaveResponse.Marshal(b, m, deterministic)
}
func (m *ArticleCategorySaveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategorySaveResponse.Merge(m, src)
}
func (m *ArticleCategorySaveResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleCategorySaveResponse.Size(m)
}
func (m *ArticleCategorySaveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategorySaveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategorySaveResponse proto.InternalMessageInfo

func (m *ArticleCategorySaveResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleCategorySaveResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleCategorySaveResponse) GetData() int32 {
	if m != nil {
		return m.Data
	}
	return 0
}

// 类目删除
type ArticleCategoryDeleteRequest struct {
	// 类目id
	CategoryId           int32    `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	Level                int32    `protobuf:"varint,2,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCategoryDeleteRequest) Reset()         { *m = ArticleCategoryDeleteRequest{} }
func (m *ArticleCategoryDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleCategoryDeleteRequest) ProtoMessage()    {}
func (*ArticleCategoryDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{23}
}

func (m *ArticleCategoryDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategoryDeleteRequest.Unmarshal(m, b)
}
func (m *ArticleCategoryDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategoryDeleteRequest.Marshal(b, m, deterministic)
}
func (m *ArticleCategoryDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategoryDeleteRequest.Merge(m, src)
}
func (m *ArticleCategoryDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleCategoryDeleteRequest.Size(m)
}
func (m *ArticleCategoryDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategoryDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategoryDeleteRequest proto.InternalMessageInfo

func (m *ArticleCategoryDeleteRequest) GetCategoryId() int32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *ArticleCategoryDeleteRequest) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type ArticleCategoryDeleteResponse struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCategoryDeleteResponse) Reset()         { *m = ArticleCategoryDeleteResponse{} }
func (m *ArticleCategoryDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleCategoryDeleteResponse) ProtoMessage()    {}
func (*ArticleCategoryDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{24}
}

func (m *ArticleCategoryDeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategoryDeleteResponse.Unmarshal(m, b)
}
func (m *ArticleCategoryDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategoryDeleteResponse.Marshal(b, m, deterministic)
}
func (m *ArticleCategoryDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategoryDeleteResponse.Merge(m, src)
}
func (m *ArticleCategoryDeleteResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleCategoryDeleteResponse.Size(m)
}
func (m *ArticleCategoryDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategoryDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategoryDeleteResponse proto.InternalMessageInfo

func (m *ArticleCategoryDeleteResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleCategoryDeleteResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 类目查询
type ArticleCategoryListRequest struct {
	// 主体id
	OrgId                int32    `protobuf:"varint,1,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCategoryListRequest) Reset()         { *m = ArticleCategoryListRequest{} }
func (m *ArticleCategoryListRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleCategoryListRequest) ProtoMessage()    {}
func (*ArticleCategoryListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{25}
}

func (m *ArticleCategoryListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategoryListRequest.Unmarshal(m, b)
}
func (m *ArticleCategoryListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategoryListRequest.Marshal(b, m, deterministic)
}
func (m *ArticleCategoryListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategoryListRequest.Merge(m, src)
}
func (m *ArticleCategoryListRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleCategoryListRequest.Size(m)
}
func (m *ArticleCategoryListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategoryListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategoryListRequest proto.InternalMessageInfo

func (m *ArticleCategoryListRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleCategoryListResponse struct {
	Code                 int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                  `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*FirstArticleCategory `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ArticleCategoryListResponse) Reset()         { *m = ArticleCategoryListResponse{} }
func (m *ArticleCategoryListResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleCategoryListResponse) ProtoMessage()    {}
func (*ArticleCategoryListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{26}
}

func (m *ArticleCategoryListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCategoryListResponse.Unmarshal(m, b)
}
func (m *ArticleCategoryListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCategoryListResponse.Marshal(b, m, deterministic)
}
func (m *ArticleCategoryListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCategoryListResponse.Merge(m, src)
}
func (m *ArticleCategoryListResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleCategoryListResponse.Size(m)
}
func (m *ArticleCategoryListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCategoryListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCategoryListResponse proto.InternalMessageInfo

func (m *ArticleCategoryListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleCategoryListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleCategoryListResponse) GetData() []*FirstArticleCategory {
	if m != nil {
		return m.Data
	}
	return nil
}

// 一级类目
type FirstArticleCategory struct {
	// 类目id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 类目名称
	CategoryName string `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	// 上级id
	ParentId int32 `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	// 是否可用
	Valid int32 `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	// 创建人id
	CreateId string `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	// 创建人姓名
	CreateName string `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	// 类目层级
	Level int32 `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	// 子类目
	Child                []*SecondArticleCategory `protobuf:"bytes,8,rep,name=child,proto3" json:"child"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *FirstArticleCategory) Reset()         { *m = FirstArticleCategory{} }
func (m *FirstArticleCategory) String() string { return proto.CompactTextString(m) }
func (*FirstArticleCategory) ProtoMessage()    {}
func (*FirstArticleCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{27}
}

func (m *FirstArticleCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FirstArticleCategory.Unmarshal(m, b)
}
func (m *FirstArticleCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FirstArticleCategory.Marshal(b, m, deterministic)
}
func (m *FirstArticleCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FirstArticleCategory.Merge(m, src)
}
func (m *FirstArticleCategory) XXX_Size() int {
	return xxx_messageInfo_FirstArticleCategory.Size(m)
}
func (m *FirstArticleCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_FirstArticleCategory.DiscardUnknown(m)
}

var xxx_messageInfo_FirstArticleCategory proto.InternalMessageInfo

func (m *FirstArticleCategory) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FirstArticleCategory) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *FirstArticleCategory) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *FirstArticleCategory) GetValid() int32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func (m *FirstArticleCategory) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *FirstArticleCategory) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *FirstArticleCategory) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FirstArticleCategory) GetChild() []*SecondArticleCategory {
	if m != nil {
		return m.Child
	}
	return nil
}

// 二级类目
type SecondArticleCategory struct {
	Id                   int32                   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName         string                  `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId             int32                   `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid                int32                   `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId             string                  `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName           string                  `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level                int32                   `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	Child                []*ThirdArticleCategory `protobuf:"bytes,8,rep,name=child,proto3" json:"child"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SecondArticleCategory) Reset()         { *m = SecondArticleCategory{} }
func (m *SecondArticleCategory) String() string { return proto.CompactTextString(m) }
func (*SecondArticleCategory) ProtoMessage()    {}
func (*SecondArticleCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{28}
}

func (m *SecondArticleCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecondArticleCategory.Unmarshal(m, b)
}
func (m *SecondArticleCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecondArticleCategory.Marshal(b, m, deterministic)
}
func (m *SecondArticleCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecondArticleCategory.Merge(m, src)
}
func (m *SecondArticleCategory) XXX_Size() int {
	return xxx_messageInfo_SecondArticleCategory.Size(m)
}
func (m *SecondArticleCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_SecondArticleCategory.DiscardUnknown(m)
}

var xxx_messageInfo_SecondArticleCategory proto.InternalMessageInfo

func (m *SecondArticleCategory) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SecondArticleCategory) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *SecondArticleCategory) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *SecondArticleCategory) GetValid() int32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func (m *SecondArticleCategory) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *SecondArticleCategory) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *SecondArticleCategory) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SecondArticleCategory) GetChild() []*ThirdArticleCategory {
	if m != nil {
		return m.Child
	}
	return nil
}

// 三级类目
type ThirdArticleCategory struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName         string   `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId             int32    `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid                int32    `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId             string   `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName           string   `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level                int32    `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ThirdArticleCategory) Reset()         { *m = ThirdArticleCategory{} }
func (m *ThirdArticleCategory) String() string { return proto.CompactTextString(m) }
func (*ThirdArticleCategory) ProtoMessage()    {}
func (*ThirdArticleCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{29}
}

func (m *ThirdArticleCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThirdArticleCategory.Unmarshal(m, b)
}
func (m *ThirdArticleCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThirdArticleCategory.Marshal(b, m, deterministic)
}
func (m *ThirdArticleCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThirdArticleCategory.Merge(m, src)
}
func (m *ThirdArticleCategory) XXX_Size() int {
	return xxx_messageInfo_ThirdArticleCategory.Size(m)
}
func (m *ThirdArticleCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_ThirdArticleCategory.DiscardUnknown(m)
}

var xxx_messageInfo_ThirdArticleCategory proto.InternalMessageInfo

func (m *ThirdArticleCategory) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ThirdArticleCategory) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *ThirdArticleCategory) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *ThirdArticleCategory) GetValid() int32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func (m *ThirdArticleCategory) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *ThirdArticleCategory) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *ThirdArticleCategory) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type QueryDoctorListRequest struct {
	//关键字搜索，现只支持医生名称
	Keyword              string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryDoctorListRequest) Reset()         { *m = QueryDoctorListRequest{} }
func (m *QueryDoctorListRequest) String() string { return proto.CompactTextString(m) }
func (*QueryDoctorListRequest) ProtoMessage()    {}
func (*QueryDoctorListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{30}
}

func (m *QueryDoctorListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryDoctorListRequest.Unmarshal(m, b)
}
func (m *QueryDoctorListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryDoctorListRequest.Marshal(b, m, deterministic)
}
func (m *QueryDoctorListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryDoctorListRequest.Merge(m, src)
}
func (m *QueryDoctorListRequest) XXX_Size() int {
	return xxx_messageInfo_QueryDoctorListRequest.Size(m)
}
func (m *QueryDoctorListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryDoctorListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryDoctorListRequest proto.InternalMessageInfo

func (m *QueryDoctorListRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type QueryDoctorListResponse struct {
	Code                 int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string        `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*ScrmDoctor `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QueryDoctorListResponse) Reset()         { *m = QueryDoctorListResponse{} }
func (m *QueryDoctorListResponse) String() string { return proto.CompactTextString(m) }
func (*QueryDoctorListResponse) ProtoMessage()    {}
func (*QueryDoctorListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{31}
}

func (m *QueryDoctorListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryDoctorListResponse.Unmarshal(m, b)
}
func (m *QueryDoctorListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryDoctorListResponse.Marshal(b, m, deterministic)
}
func (m *QueryDoctorListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryDoctorListResponse.Merge(m, src)
}
func (m *QueryDoctorListResponse) XXX_Size() int {
	return xxx_messageInfo_QueryDoctorListResponse.Size(m)
}
func (m *QueryDoctorListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryDoctorListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryDoctorListResponse proto.InternalMessageInfo

func (m *QueryDoctorListResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryDoctorListResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryDoctorListResponse) GetData() []*ScrmDoctor {
	if m != nil {
		return m.Data
	}
	return nil
}

type ScrmDoctor struct {
	//医生编号
	DoctorCode string `protobuf:"bytes,1,opt,name=doctor_code,json=doctorCode,proto3" json:"doctor_code"`
	//医生名称
	DoctorName string `protobuf:"bytes,2,opt,name=doctor_name,json=doctorName,proto3" json:"doctor_name"`
	//医生称号（职级、岗位）
	DoctorLevel string `protobuf:"bytes,3,opt,name=doctor_level,json=doctorLevel,proto3" json:"doctor_level"`
	//医生性别
	DoctorSex string `protobuf:"bytes,4,opt,name=doctor_sex,json=doctorSex,proto3" json:"doctor_sex"`
	//医生头像
	DoctorImg string `protobuf:"bytes,5,opt,name=doctor_img,json=doctorImg,proto3" json:"doctor_img"`
	//医院编号
	HospitalCode string `protobuf:"bytes,6,opt,name=hospital_code,json=hospitalCode,proto3" json:"hospital_code"`
	//医院名称
	HospitalName         string   `protobuf:"bytes,7,opt,name=hospital_name,json=hospitalName,proto3" json:"hospital_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScrmDoctor) Reset()         { *m = ScrmDoctor{} }
func (m *ScrmDoctor) String() string { return proto.CompactTextString(m) }
func (*ScrmDoctor) ProtoMessage()    {}
func (*ScrmDoctor) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{32}
}

func (m *ScrmDoctor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScrmDoctor.Unmarshal(m, b)
}
func (m *ScrmDoctor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScrmDoctor.Marshal(b, m, deterministic)
}
func (m *ScrmDoctor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScrmDoctor.Merge(m, src)
}
func (m *ScrmDoctor) XXX_Size() int {
	return xxx_messageInfo_ScrmDoctor.Size(m)
}
func (m *ScrmDoctor) XXX_DiscardUnknown() {
	xxx_messageInfo_ScrmDoctor.DiscardUnknown(m)
}

var xxx_messageInfo_ScrmDoctor proto.InternalMessageInfo

func (m *ScrmDoctor) GetDoctorCode() string {
	if m != nil {
		return m.DoctorCode
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorName() string {
	if m != nil {
		return m.DoctorName
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorLevel() string {
	if m != nil {
		return m.DoctorLevel
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorSex() string {
	if m != nil {
		return m.DoctorSex
	}
	return ""
}

func (m *ScrmDoctor) GetDoctorImg() string {
	if m != nil {
		return m.DoctorImg
	}
	return ""
}

func (m *ScrmDoctor) GetHospitalCode() string {
	if m != nil {
		return m.HospitalCode
	}
	return ""
}

func (m *ScrmDoctor) GetHospitalName() string {
	if m != nil {
		return m.HospitalName
	}
	return ""
}

type ArticleCensusRequest struct {
	//开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	//结束时间
	EndTime string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	//访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	//页码
	PageIndex int32 `protobuf:"varint,4,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	//每页条数，默认20
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//关键字 现支持文章标题
	Keyword string `protobuf:"bytes,6,opt,name=keyword,proto3" json:"keyword"`
	// 主体id
	OrgId                int32    `protobuf:"varint,7,opt,name=orgId,proto3" json:"orgId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCensusRequest) Reset()         { *m = ArticleCensusRequest{} }
func (m *ArticleCensusRequest) String() string { return proto.CompactTextString(m) }
func (*ArticleCensusRequest) ProtoMessage()    {}
func (*ArticleCensusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{33}
}

func (m *ArticleCensusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCensusRequest.Unmarshal(m, b)
}
func (m *ArticleCensusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCensusRequest.Marshal(b, m, deterministic)
}
func (m *ArticleCensusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCensusRequest.Merge(m, src)
}
func (m *ArticleCensusRequest) XXX_Size() int {
	return xxx_messageInfo_ArticleCensusRequest.Size(m)
}
func (m *ArticleCensusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCensusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCensusRequest proto.InternalMessageInfo

func (m *ArticleCensusRequest) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *ArticleCensusRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *ArticleCensusRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *ArticleCensusRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ArticleCensusRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ArticleCensusRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ArticleCensusRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleCensusResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//总条数
	TotalCount int32 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	//文章访问统计
	Data                 []*ArticleCensus `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ArticleCensusResponse) Reset()         { *m = ArticleCensusResponse{} }
func (m *ArticleCensusResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleCensusResponse) ProtoMessage()    {}
func (*ArticleCensusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{34}
}

func (m *ArticleCensusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCensusResponse.Unmarshal(m, b)
}
func (m *ArticleCensusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCensusResponse.Marshal(b, m, deterministic)
}
func (m *ArticleCensusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCensusResponse.Merge(m, src)
}
func (m *ArticleCensusResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleCensusResponse.Size(m)
}
func (m *ArticleCensusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCensusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCensusResponse proto.InternalMessageInfo

func (m *ArticleCensusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleCensusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleCensusResponse) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ArticleCensusResponse) GetData() []*ArticleCensus {
	if m != nil {
		return m.Data
	}
	return nil
}

type ArticleCensus struct {
	//文章id
	ArticleId int64 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id"`
	//文章标题
	ArticleTitle string `protobuf:"bytes,2,opt,name=article_title,json=articleTitle,proto3" json:"article_title"`
	//独立访问量
	UniqueVisitor int32 `protobuf:"varint,3,opt,name=unique_visitor,json=uniqueVisitor,proto3" json:"unique_visitor"`
	//页面访问量
	PageView int32 `protobuf:"varint,4,opt,name=page_view,json=pageView,proto3" json:"page_view"`
	//视频播放量
	VideoView            int32    `protobuf:"varint,5,opt,name=video_view,json=videoView,proto3" json:"video_view"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleCensus) Reset()         { *m = ArticleCensus{} }
func (m *ArticleCensus) String() string { return proto.CompactTextString(m) }
func (*ArticleCensus) ProtoMessage()    {}
func (*ArticleCensus) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{35}
}

func (m *ArticleCensus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleCensus.Unmarshal(m, b)
}
func (m *ArticleCensus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleCensus.Marshal(b, m, deterministic)
}
func (m *ArticleCensus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleCensus.Merge(m, src)
}
func (m *ArticleCensus) XXX_Size() int {
	return xxx_messageInfo_ArticleCensus.Size(m)
}
func (m *ArticleCensus) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleCensus.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleCensus proto.InternalMessageInfo

func (m *ArticleCensus) GetArticleId() int64 {
	if m != nil {
		return m.ArticleId
	}
	return 0
}

func (m *ArticleCensus) GetArticleTitle() string {
	if m != nil {
		return m.ArticleTitle
	}
	return ""
}

func (m *ArticleCensus) GetUniqueVisitor() int32 {
	if m != nil {
		return m.UniqueVisitor
	}
	return 0
}

func (m *ArticleCensus) GetPageView() int32 {
	if m != nil {
		return m.PageView
	}
	return 0
}

func (m *ArticleCensus) GetVideoView() int32 {
	if m != nil {
		return m.VideoView
	}
	return 0
}

type BoehringereCensusRequest struct {
	//开始时间
	BeginTime string `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	//结束时间
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoehringereCensusRequest) Reset()         { *m = BoehringereCensusRequest{} }
func (m *BoehringereCensusRequest) String() string { return proto.CompactTextString(m) }
func (*BoehringereCensusRequest) ProtoMessage()    {}
func (*BoehringereCensusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{36}
}

func (m *BoehringereCensusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoehringereCensusRequest.Unmarshal(m, b)
}
func (m *BoehringereCensusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoehringereCensusRequest.Marshal(b, m, deterministic)
}
func (m *BoehringereCensusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoehringereCensusRequest.Merge(m, src)
}
func (m *BoehringereCensusRequest) XXX_Size() int {
	return xxx_messageInfo_BoehringereCensusRequest.Size(m)
}
func (m *BoehringereCensusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BoehringereCensusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BoehringereCensusRequest proto.InternalMessageInfo

func (m *BoehringereCensusRequest) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *BoehringereCensusRequest) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type BoehringereCensusResponse struct {
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	//文章访问统计
	Data                 []*BoehringereCensus `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BoehringereCensusResponse) Reset()         { *m = BoehringereCensusResponse{} }
func (m *BoehringereCensusResponse) String() string { return proto.CompactTextString(m) }
func (*BoehringereCensusResponse) ProtoMessage()    {}
func (*BoehringereCensusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{37}
}

func (m *BoehringereCensusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoehringereCensusResponse.Unmarshal(m, b)
}
func (m *BoehringereCensusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoehringereCensusResponse.Marshal(b, m, deterministic)
}
func (m *BoehringereCensusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoehringereCensusResponse.Merge(m, src)
}
func (m *BoehringereCensusResponse) XXX_Size() int {
	return xxx_messageInfo_BoehringereCensusResponse.Size(m)
}
func (m *BoehringereCensusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BoehringereCensusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BoehringereCensusResponse proto.InternalMessageInfo

func (m *BoehringereCensusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BoehringereCensusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *BoehringereCensusResponse) GetData() []*BoehringereCensus {
	if m != nil {
		return m.Data
	}
	return nil
}

type BoehringereCensus struct {
	//类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	//类型子级（0为标题，1为内容一，以此类推）
	TypeChild int32 `protobuf:"varint,2,opt,name=type_child,json=typeChild,proto3" json:"type_child"`
	//访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	//点击量
	TotalCount           int32    `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoehringereCensus) Reset()         { *m = BoehringereCensus{} }
func (m *BoehringereCensus) String() string { return proto.CompactTextString(m) }
func (*BoehringereCensus) ProtoMessage()    {}
func (*BoehringereCensus) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{38}
}

func (m *BoehringereCensus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoehringereCensus.Unmarshal(m, b)
}
func (m *BoehringereCensus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoehringereCensus.Marshal(b, m, deterministic)
}
func (m *BoehringereCensus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoehringereCensus.Merge(m, src)
}
func (m *BoehringereCensus) XXX_Size() int {
	return xxx_messageInfo_BoehringereCensus.Size(m)
}
func (m *BoehringereCensus) XXX_DiscardUnknown() {
	xxx_messageInfo_BoehringereCensus.DiscardUnknown(m)
}

var xxx_messageInfo_BoehringereCensus proto.InternalMessageInfo

func (m *BoehringereCensus) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BoehringereCensus) GetTypeChild() int32 {
	if m != nil {
		return m.TypeChild
	}
	return 0
}

func (m *BoehringereCensus) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *BoehringereCensus) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type AddVisitRecordRequest struct {
	//访问内容id（type=1时为文章id）
	ContentId int32 `protobuf:"varint,1,opt,name=content_id,json=contentId,proto3" json:"content_id"`
	// 类型 1：文章 2：视频
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	// 访问渠道 1：阿闻小程序 2：百度小程序  3：安卓 4：IOS
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	// 用户编号或openId
	UserNo string `protobuf:"bytes,4,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// IP
	Ip                   string   `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVisitRecordRequest) Reset()         { *m = AddVisitRecordRequest{} }
func (m *AddVisitRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddVisitRecordRequest) ProtoMessage()    {}
func (*AddVisitRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{39}
}

func (m *AddVisitRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVisitRecordRequest.Unmarshal(m, b)
}
func (m *AddVisitRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVisitRecordRequest.Marshal(b, m, deterministic)
}
func (m *AddVisitRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVisitRecordRequest.Merge(m, src)
}
func (m *AddVisitRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddVisitRecordRequest.Size(m)
}
func (m *AddVisitRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVisitRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddVisitRecordRequest proto.InternalMessageInfo

func (m *AddVisitRecordRequest) GetContentId() int32 {
	if m != nil {
		return m.ContentId
	}
	return 0
}

func (m *AddVisitRecordRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddVisitRecordRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *AddVisitRecordRequest) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *AddVisitRecordRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type BoehringereChickRecord struct {
	// 类型：1:banner 2:内容区 3:产品区 4:问诊区 5:产品介绍区一 6:产品介绍区二 7:产品介绍区三 8:产品介绍区四
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	// 类型子级（0为标题，1为内容一，以此类推）
	TypeChild int32 `protobuf:"varint,2,opt,name=type_child,json=typeChild,proto3" json:"type_child"`
	// 用户编号
	UserNo string `protobuf:"bytes,3,opt,name=user_no,json=userNo,proto3" json:"user_no"`
	// IP
	Ip                   string   `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoehringereChickRecord) Reset()         { *m = BoehringereChickRecord{} }
func (m *BoehringereChickRecord) String() string { return proto.CompactTextString(m) }
func (*BoehringereChickRecord) ProtoMessage()    {}
func (*BoehringereChickRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{40}
}

func (m *BoehringereChickRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoehringereChickRecord.Unmarshal(m, b)
}
func (m *BoehringereChickRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoehringereChickRecord.Marshal(b, m, deterministic)
}
func (m *BoehringereChickRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoehringereChickRecord.Merge(m, src)
}
func (m *BoehringereChickRecord) XXX_Size() int {
	return xxx_messageInfo_BoehringereChickRecord.Size(m)
}
func (m *BoehringereChickRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BoehringereChickRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BoehringereChickRecord proto.InternalMessageInfo

func (m *BoehringereChickRecord) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BoehringereChickRecord) GetTypeChild() int32 {
	if m != nil {
		return m.TypeChild
	}
	return 0
}

func (m *BoehringereChickRecord) GetUserNo() string {
	if m != nil {
		return m.UserNo
	}
	return ""
}

func (m *BoehringereChickRecord) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type CategoryBarRequest struct {
	//0 默认查一级分类 1 二级分类
	Type int32  `protobuf:"varint,1,opt,name=type,proto3" json:"type"`
	Tags string `protobuf:"bytes,2,opt,name=tags,proto3" json:"tags"`
	// 主体id，默认1-阿闻
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CategoryBarRequest) Reset()         { *m = CategoryBarRequest{} }
func (m *CategoryBarRequest) String() string { return proto.CompactTextString(m) }
func (*CategoryBarRequest) ProtoMessage()    {}
func (*CategoryBarRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{41}
}

func (m *CategoryBarRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryBarRequest.Unmarshal(m, b)
}
func (m *CategoryBarRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryBarRequest.Marshal(b, m, deterministic)
}
func (m *CategoryBarRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryBarRequest.Merge(m, src)
}
func (m *CategoryBarRequest) XXX_Size() int {
	return xxx_messageInfo_CategoryBarRequest.Size(m)
}
func (m *CategoryBarRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryBarRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryBarRequest proto.InternalMessageInfo

func (m *CategoryBarRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CategoryBarRequest) GetTags() string {
	if m != nil {
		return m.Tags
	}
	return ""
}

func (m *CategoryBarRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type CategoryBarResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*Category `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CategoryBarResponse) Reset()         { *m = CategoryBarResponse{} }
func (m *CategoryBarResponse) String() string { return proto.CompactTextString(m) }
func (*CategoryBarResponse) ProtoMessage()    {}
func (*CategoryBarResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{42}
}

func (m *CategoryBarResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CategoryBarResponse.Unmarshal(m, b)
}
func (m *CategoryBarResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CategoryBarResponse.Marshal(b, m, deterministic)
}
func (m *CategoryBarResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CategoryBarResponse.Merge(m, src)
}
func (m *CategoryBarResponse) XXX_Size() int {
	return xxx_messageInfo_CategoryBarResponse.Size(m)
}
func (m *CategoryBarResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CategoryBarResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CategoryBarResponse proto.InternalMessageInfo

func (m *CategoryBarResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CategoryBarResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CategoryBarResponse) GetData() []*Category {
	if m != nil {
		return m.Data
	}
	return nil
}

type Category struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CategoryName         string   `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name"`
	ParentId             int32    `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Valid                int32    `protobuf:"varint,4,opt,name=valid,proto3" json:"valid"`
	CreateId             string   `protobuf:"bytes,5,opt,name=create_id,json=createId,proto3" json:"create_id"`
	CreateName           string   `protobuf:"bytes,6,opt,name=create_name,json=createName,proto3" json:"create_name"`
	Level                int32    `protobuf:"varint,7,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Category) Reset()         { *m = Category{} }
func (m *Category) String() string { return proto.CompactTextString(m) }
func (*Category) ProtoMessage()    {}
func (*Category) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{43}
}

func (m *Category) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Category.Unmarshal(m, b)
}
func (m *Category) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Category.Marshal(b, m, deterministic)
}
func (m *Category) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Category.Merge(m, src)
}
func (m *Category) XXX_Size() int {
	return xxx_messageInfo_Category.Size(m)
}
func (m *Category) XXX_DiscardUnknown() {
	xxx_messageInfo_Category.DiscardUnknown(m)
}

var xxx_messageInfo_Category proto.InternalMessageInfo

func (m *Category) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Category) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *Category) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *Category) GetValid() int32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func (m *Category) GetCreateId() string {
	if m != nil {
		return m.CreateId
	}
	return ""
}

func (m *Category) GetCreateName() string {
	if m != nil {
		return m.CreateName
	}
	return ""
}

func (m *Category) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type EmptyRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyRequest) Reset()         { *m = EmptyRequest{} }
func (m *EmptyRequest) String() string { return proto.CompactTextString(m) }
func (*EmptyRequest) ProtoMessage()    {}
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{44}
}

func (m *EmptyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyRequest.Unmarshal(m, b)
}
func (m *EmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyRequest.Marshal(b, m, deterministic)
}
func (m *EmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyRequest.Merge(m, src)
}
func (m *EmptyRequest) XXX_Size() int {
	return xxx_messageInfo_EmptyRequest.Size(m)
}
func (m *EmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyRequest proto.InternalMessageInfo

type OrgIdRequest struct {
	// 主体id，默认1-阿闻
	OrgId                int32    `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrgIdRequest) Reset()         { *m = OrgIdRequest{} }
func (m *OrgIdRequest) String() string { return proto.CompactTextString(m) }
func (*OrgIdRequest) ProtoMessage()    {}
func (*OrgIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{45}
}

func (m *OrgIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrgIdRequest.Unmarshal(m, b)
}
func (m *OrgIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrgIdRequest.Marshal(b, m, deterministic)
}
func (m *OrgIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrgIdRequest.Merge(m, src)
}
func (m *OrgIdRequest) XXX_Size() int {
	return xxx_messageInfo_OrgIdRequest.Size(m)
}
func (m *OrgIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OrgIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OrgIdRequest proto.InternalMessageInfo

func (m *OrgIdRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type ArticleTagGroup struct {
	// 标签组名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 多个标签，用逗号分割
	Tags                 string   `protobuf:"bytes,2,opt,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArticleTagGroup) Reset()         { *m = ArticleTagGroup{} }
func (m *ArticleTagGroup) String() string { return proto.CompactTextString(m) }
func (*ArticleTagGroup) ProtoMessage()    {}
func (*ArticleTagGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{46}
}

func (m *ArticleTagGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleTagGroup.Unmarshal(m, b)
}
func (m *ArticleTagGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleTagGroup.Marshal(b, m, deterministic)
}
func (m *ArticleTagGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleTagGroup.Merge(m, src)
}
func (m *ArticleTagGroup) XXX_Size() int {
	return xxx_messageInfo_ArticleTagGroup.Size(m)
}
func (m *ArticleTagGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleTagGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleTagGroup proto.InternalMessageInfo

func (m *ArticleTagGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ArticleTagGroup) GetTags() string {
	if m != nil {
		return m.Tags
	}
	return ""
}

type ArticleMiniFilterResponse struct {
	// 200正常，400错误
	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 标签组数据
	TagGroups            []*ArticleTagGroup `protobuf:"bytes,3,rep,name=tag_groups,json=tagGroups,proto3" json:"tag_groups"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ArticleMiniFilterResponse) Reset()         { *m = ArticleMiniFilterResponse{} }
func (m *ArticleMiniFilterResponse) String() string { return proto.CompactTextString(m) }
func (*ArticleMiniFilterResponse) ProtoMessage()    {}
func (*ArticleMiniFilterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{47}
}

func (m *ArticleMiniFilterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArticleMiniFilterResponse.Unmarshal(m, b)
}
func (m *ArticleMiniFilterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArticleMiniFilterResponse.Marshal(b, m, deterministic)
}
func (m *ArticleMiniFilterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArticleMiniFilterResponse.Merge(m, src)
}
func (m *ArticleMiniFilterResponse) XXX_Size() int {
	return xxx_messageInfo_ArticleMiniFilterResponse.Size(m)
}
func (m *ArticleMiniFilterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArticleMiniFilterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArticleMiniFilterResponse proto.InternalMessageInfo

func (m *ArticleMiniFilterResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ArticleMiniFilterResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ArticleMiniFilterResponse) GetTagGroups() []*ArticleTagGroup {
	if m != nil {
		return m.TagGroups
	}
	return nil
}

type GetStatisticsResponse struct {
	//文章条数
	ArticleCount int32 `protobuf:"varint,1,opt,name=articleCount,proto3" json:"articleCount"`
	//类目条数
	CategoryCount        int32    `protobuf:"varint,2,opt,name=categoryCount,proto3" json:"categoryCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStatisticsResponse) Reset()         { *m = GetStatisticsResponse{} }
func (m *GetStatisticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetStatisticsResponse) ProtoMessage()    {}
func (*GetStatisticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{48}
}

func (m *GetStatisticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatisticsResponse.Unmarshal(m, b)
}
func (m *GetStatisticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatisticsResponse.Marshal(b, m, deterministic)
}
func (m *GetStatisticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatisticsResponse.Merge(m, src)
}
func (m *GetStatisticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetStatisticsResponse.Size(m)
}
func (m *GetStatisticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatisticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatisticsResponse proto.InternalMessageInfo

func (m *GetStatisticsResponse) GetArticleCount() int32 {
	if m != nil {
		return m.ArticleCount
	}
	return 0
}

func (m *GetStatisticsResponse) GetCategoryCount() int32 {
	if m != nil {
		return m.CategoryCount
	}
	return 0
}

type AddClickRecordRequest struct {
	TrackId              string   `protobuf:"bytes,1,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	AccountId            string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	CampaignId           string   `protobuf:"bytes,3,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id"`
	UnitId               string   `protobuf:"bytes,4,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
	CreativeId           string   `protobuf:"bytes,5,opt,name=creative_id,json=creativeId,proto3" json:"creative_id"`
	Os                   int64    `protobuf:"varint,6,opt,name=os,proto3" json:"os"`
	Imei                 string   `protobuf:"bytes,7,opt,name=imei,proto3" json:"imei"`
	CallbackUrl          string   `protobuf:"bytes,8,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url"`
	Mac1                 string   `protobuf:"bytes,9,opt,name=mac1,proto3" json:"mac1"`
	Idfa                 string   `protobuf:"bytes,10,opt,name=idfa,proto3" json:"idfa"`
	Caid                 string   `protobuf:"bytes,11,opt,name=caid,proto3" json:"caid"`
	Aaid                 string   `protobuf:"bytes,12,opt,name=aaid,proto3" json:"aaid"`
	AndroidId            string   `protobuf:"bytes,13,opt,name=android_id,json=androidId,proto3" json:"android_id"`
	Oaid                 string   `protobuf:"bytes,14,opt,name=oaid,proto3" json:"oaid"`
	Ip                   string   `protobuf:"bytes,15,opt,name=ip,proto3" json:"ip"`
	Ua                   string   `protobuf:"bytes,16,opt,name=ua,proto3" json:"ua"`
	Model                string   `protobuf:"bytes,17,opt,name=model,proto3" json:"model"`
	Ts                   int64    `protobuf:"varint,18,opt,name=ts,proto3" json:"ts"`
	ShopId               int64    `protobuf:"varint,19,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	UpMid                int64    `protobuf:"varint,20,opt,name=up_mid,json=upMid,proto3" json:"up_mid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddClickRecordRequest) Reset()         { *m = AddClickRecordRequest{} }
func (m *AddClickRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddClickRecordRequest) ProtoMessage()    {}
func (*AddClickRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{49}
}

func (m *AddClickRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddClickRecordRequest.Unmarshal(m, b)
}
func (m *AddClickRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddClickRecordRequest.Marshal(b, m, deterministic)
}
func (m *AddClickRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddClickRecordRequest.Merge(m, src)
}
func (m *AddClickRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddClickRecordRequest.Size(m)
}
func (m *AddClickRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddClickRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddClickRecordRequest proto.InternalMessageInfo

func (m *AddClickRecordRequest) GetTrackId() string {
	if m != nil {
		return m.TrackId
	}
	return ""
}

func (m *AddClickRecordRequest) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *AddClickRecordRequest) GetCampaignId() string {
	if m != nil {
		return m.CampaignId
	}
	return ""
}

func (m *AddClickRecordRequest) GetUnitId() string {
	if m != nil {
		return m.UnitId
	}
	return ""
}

func (m *AddClickRecordRequest) GetCreativeId() string {
	if m != nil {
		return m.CreativeId
	}
	return ""
}

func (m *AddClickRecordRequest) GetOs() int64 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *AddClickRecordRequest) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *AddClickRecordRequest) GetCallbackUrl() string {
	if m != nil {
		return m.CallbackUrl
	}
	return ""
}

func (m *AddClickRecordRequest) GetMac1() string {
	if m != nil {
		return m.Mac1
	}
	return ""
}

func (m *AddClickRecordRequest) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *AddClickRecordRequest) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

func (m *AddClickRecordRequest) GetAaid() string {
	if m != nil {
		return m.Aaid
	}
	return ""
}

func (m *AddClickRecordRequest) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *AddClickRecordRequest) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *AddClickRecordRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AddClickRecordRequest) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *AddClickRecordRequest) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *AddClickRecordRequest) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *AddClickRecordRequest) GetShopId() int64 {
	if m != nil {
		return m.ShopId
	}
	return 0
}

func (m *AddClickRecordRequest) GetUpMid() int64 {
	if m != nil {
		return m.UpMid
	}
	return 0
}

type FindClickRecordsRequest struct {
	Imei                 string   `protobuf:"bytes,1,opt,name=imei,proto3" json:"imei"`
	Os                   int64    `protobuf:"varint,2,opt,name=os,proto3" json:"os"`
	Idfa                 string   `protobuf:"bytes,3,opt,name=idfa,proto3" json:"idfa"`
	Oaid                 string   `protobuf:"bytes,4,opt,name=oaid,proto3" json:"oaid"`
	Ip                   string   `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip"`
	Ua                   string   `protobuf:"bytes,6,opt,name=ua,proto3" json:"ua"`
	CreatedAt            string   `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FindClickRecordsRequest) Reset()         { *m = FindClickRecordsRequest{} }
func (m *FindClickRecordsRequest) String() string { return proto.CompactTextString(m) }
func (*FindClickRecordsRequest) ProtoMessage()    {}
func (*FindClickRecordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{50}
}

func (m *FindClickRecordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindClickRecordsRequest.Unmarshal(m, b)
}
func (m *FindClickRecordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindClickRecordsRequest.Marshal(b, m, deterministic)
}
func (m *FindClickRecordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindClickRecordsRequest.Merge(m, src)
}
func (m *FindClickRecordsRequest) XXX_Size() int {
	return xxx_messageInfo_FindClickRecordsRequest.Size(m)
}
func (m *FindClickRecordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindClickRecordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindClickRecordsRequest proto.InternalMessageInfo

func (m *FindClickRecordsRequest) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *FindClickRecordsRequest) GetOs() int64 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *FindClickRecordsRequest) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *FindClickRecordsRequest) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *FindClickRecordsRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *FindClickRecordsRequest) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *FindClickRecordsRequest) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

type BiliAdClickRecord struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TrackId              string   `protobuf:"bytes,2,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	AccountId            string   `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	CampaignId           string   `protobuf:"bytes,4,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id"`
	UnitId               string   `protobuf:"bytes,5,opt,name=unit_id,json=unitId,proto3" json:"unit_id"`
	CreativeId           string   `protobuf:"bytes,6,opt,name=creative_id,json=creativeId,proto3" json:"creative_id"`
	Os                   int64    `protobuf:"varint,7,opt,name=os,proto3" json:"os"`
	Imei                 string   `protobuf:"bytes,8,opt,name=imei,proto3" json:"imei"`
	Mac1                 string   `protobuf:"bytes,9,opt,name=mac1,proto3" json:"mac1"`
	Idfa                 string   `protobuf:"bytes,11,opt,name=idfa,proto3" json:"idfa"`
	Caid                 string   `protobuf:"bytes,12,opt,name=caid,proto3" json:"caid"`
	Aaid                 string   `protobuf:"bytes,13,opt,name=aaid,proto3" json:"aaid"`
	AndroidId            string   `protobuf:"bytes,14,opt,name=android_id,json=androidId,proto3" json:"android_id"`
	Oaid                 string   `protobuf:"bytes,15,opt,name=oaid,proto3" json:"oaid"`
	Ip                   string   `protobuf:"bytes,16,opt,name=ip,proto3" json:"ip"`
	Ua                   string   `protobuf:"bytes,17,opt,name=ua,proto3" json:"ua"`
	Ts                   int64    `protobuf:"varint,18,opt,name=ts,proto3" json:"ts"`
	ShopId               int64    `protobuf:"varint,19,opt,name=shop_id,json=shopId,proto3" json:"shop_id"`
	UpMid                int64    `protobuf:"varint,20,opt,name=up_mid,json=upMid,proto3" json:"up_mid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BiliAdClickRecord) Reset()         { *m = BiliAdClickRecord{} }
func (m *BiliAdClickRecord) String() string { return proto.CompactTextString(m) }
func (*BiliAdClickRecord) ProtoMessage()    {}
func (*BiliAdClickRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{51}
}

func (m *BiliAdClickRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BiliAdClickRecord.Unmarshal(m, b)
}
func (m *BiliAdClickRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BiliAdClickRecord.Marshal(b, m, deterministic)
}
func (m *BiliAdClickRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BiliAdClickRecord.Merge(m, src)
}
func (m *BiliAdClickRecord) XXX_Size() int {
	return xxx_messageInfo_BiliAdClickRecord.Size(m)
}
func (m *BiliAdClickRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BiliAdClickRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BiliAdClickRecord proto.InternalMessageInfo

func (m *BiliAdClickRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BiliAdClickRecord) GetTrackId() string {
	if m != nil {
		return m.TrackId
	}
	return ""
}

func (m *BiliAdClickRecord) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *BiliAdClickRecord) GetCampaignId() string {
	if m != nil {
		return m.CampaignId
	}
	return ""
}

func (m *BiliAdClickRecord) GetUnitId() string {
	if m != nil {
		return m.UnitId
	}
	return ""
}

func (m *BiliAdClickRecord) GetCreativeId() string {
	if m != nil {
		return m.CreativeId
	}
	return ""
}

func (m *BiliAdClickRecord) GetOs() int64 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *BiliAdClickRecord) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *BiliAdClickRecord) GetMac1() string {
	if m != nil {
		return m.Mac1
	}
	return ""
}

func (m *BiliAdClickRecord) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *BiliAdClickRecord) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

func (m *BiliAdClickRecord) GetAaid() string {
	if m != nil {
		return m.Aaid
	}
	return ""
}

func (m *BiliAdClickRecord) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *BiliAdClickRecord) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *BiliAdClickRecord) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *BiliAdClickRecord) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *BiliAdClickRecord) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *BiliAdClickRecord) GetShopId() int64 {
	if m != nil {
		return m.ShopId
	}
	return 0
}

func (m *BiliAdClickRecord) GetUpMid() int64 {
	if m != nil {
		return m.UpMid
	}
	return 0
}

type FindClickRecordsResponse struct {
	// 响应码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 返回信息
	Message              string               `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	List                 []*BiliAdClickRecord `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *FindClickRecordsResponse) Reset()         { *m = FindClickRecordsResponse{} }
func (m *FindClickRecordsResponse) String() string { return proto.CompactTextString(m) }
func (*FindClickRecordsResponse) ProtoMessage()    {}
func (*FindClickRecordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{52}
}

func (m *FindClickRecordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FindClickRecordsResponse.Unmarshal(m, b)
}
func (m *FindClickRecordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FindClickRecordsResponse.Marshal(b, m, deterministic)
}
func (m *FindClickRecordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindClickRecordsResponse.Merge(m, src)
}
func (m *FindClickRecordsResponse) XXX_Size() int {
	return xxx_messageInfo_FindClickRecordsResponse.Size(m)
}
func (m *FindClickRecordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FindClickRecordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FindClickRecordsResponse proto.InternalMessageInfo

func (m *FindClickRecordsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *FindClickRecordsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FindClickRecordsResponse) GetList() []*BiliAdClickRecord {
	if m != nil {
		return m.List
	}
	return nil
}

type SaveConversionRequest struct {
	BiliAdClickId        int64    `protobuf:"varint,1,opt,name=bili_ad_click_id,json=biliAdClickId,proto3" json:"bili_ad_click_id"`
	ConvType             string   `protobuf:"bytes,2,opt,name=conv_type,json=convType,proto3" json:"conv_type"`
	ConvTime             int64    `protobuf:"varint,3,opt,name=conv_time,json=convTime,proto3" json:"conv_time"`
	ConvValue            int64    `protobuf:"varint,4,opt,name=conv_value,json=convValue,proto3" json:"conv_value"`
	ConvCount            int64    `protobuf:"varint,5,opt,name=conv_count,json=convCount,proto3" json:"conv_count"`
	Imei                 string   `protobuf:"bytes,6,opt,name=imei,proto3" json:"imei"`
	Idfa                 string   `protobuf:"bytes,7,opt,name=idfa,proto3" json:"idfa"`
	Oaid                 string   `protobuf:"bytes,8,opt,name=oaid,proto3" json:"oaid"`
	Mac                  string   `protobuf:"bytes,9,opt,name=mac,proto3" json:"mac"`
	ClientIp             string   `protobuf:"bytes,10,opt,name=client_ip,json=clientIp,proto3" json:"client_ip"`
	Model                string   `protobuf:"bytes,11,opt,name=model,proto3" json:"model"`
	TrackId              string   `protobuf:"bytes,12,opt,name=track_id,json=trackId,proto3" json:"track_id"`
	Ua                   string   `protobuf:"bytes,13,opt,name=ua,proto3" json:"ua"`
	ResultCode           int64    `protobuf:"varint,14,opt,name=result_code,json=resultCode,proto3" json:"result_code"`
	ResultMessage        string   `protobuf:"bytes,15,opt,name=result_message,json=resultMessage,proto3" json:"result_message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveConversionRequest) Reset()         { *m = SaveConversionRequest{} }
func (m *SaveConversionRequest) String() string { return proto.CompactTextString(m) }
func (*SaveConversionRequest) ProtoMessage()    {}
func (*SaveConversionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{53}
}

func (m *SaveConversionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveConversionRequest.Unmarshal(m, b)
}
func (m *SaveConversionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveConversionRequest.Marshal(b, m, deterministic)
}
func (m *SaveConversionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveConversionRequest.Merge(m, src)
}
func (m *SaveConversionRequest) XXX_Size() int {
	return xxx_messageInfo_SaveConversionRequest.Size(m)
}
func (m *SaveConversionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveConversionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveConversionRequest proto.InternalMessageInfo

func (m *SaveConversionRequest) GetBiliAdClickId() int64 {
	if m != nil {
		return m.BiliAdClickId
	}
	return 0
}

func (m *SaveConversionRequest) GetConvType() string {
	if m != nil {
		return m.ConvType
	}
	return ""
}

func (m *SaveConversionRequest) GetConvTime() int64 {
	if m != nil {
		return m.ConvTime
	}
	return 0
}

func (m *SaveConversionRequest) GetConvValue() int64 {
	if m != nil {
		return m.ConvValue
	}
	return 0
}

func (m *SaveConversionRequest) GetConvCount() int64 {
	if m != nil {
		return m.ConvCount
	}
	return 0
}

func (m *SaveConversionRequest) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *SaveConversionRequest) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *SaveConversionRequest) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *SaveConversionRequest) GetMac() string {
	if m != nil {
		return m.Mac
	}
	return ""
}

func (m *SaveConversionRequest) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *SaveConversionRequest) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *SaveConversionRequest) GetTrackId() string {
	if m != nil {
		return m.TrackId
	}
	return ""
}

func (m *SaveConversionRequest) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *SaveConversionRequest) GetResultCode() int64 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *SaveConversionRequest) GetResultMessage() string {
	if m != nil {
		return m.ResultMessage
	}
	return ""
}

type JsonStrRequest struct {
	// json字节
	JsonByte []byte `protobuf:"bytes,1,opt,name=json_byte,json=jsonByte,proto3" json:"json_byte"`
	// 类型，1-热词，2-默认词
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JsonStrRequest) Reset()         { *m = JsonStrRequest{} }
func (m *JsonStrRequest) String() string { return proto.CompactTextString(m) }
func (*JsonStrRequest) ProtoMessage()    {}
func (*JsonStrRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{54}
}

func (m *JsonStrRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JsonStrRequest.Unmarshal(m, b)
}
func (m *JsonStrRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JsonStrRequest.Marshal(b, m, deterministic)
}
func (m *JsonStrRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JsonStrRequest.Merge(m, src)
}
func (m *JsonStrRequest) XXX_Size() int {
	return xxx_messageInfo_JsonStrRequest.Size(m)
}
func (m *JsonStrRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JsonStrRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JsonStrRequest proto.InternalMessageInfo

func (m *JsonStrRequest) GetJsonByte() []byte {
	if m != nil {
		return m.JsonByte
	}
	return nil
}

func (m *JsonStrRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *JsonStrRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

// ********* 搜索词相关接口  ****************
// 前端接口
type IndexWordRequest struct {
	// 渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏
	UserAgent string `protobuf:"bytes,1,opt,name=user_agent,json=userAgent,proto3" json:"user_agent"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexWordRequest) Reset()         { *m = IndexWordRequest{} }
func (m *IndexWordRequest) String() string { return proto.CompactTextString(m) }
func (*IndexWordRequest) ProtoMessage()    {}
func (*IndexWordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{55}
}

func (m *IndexWordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexWordRequest.Unmarshal(m, b)
}
func (m *IndexWordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexWordRequest.Marshal(b, m, deterministic)
}
func (m *IndexWordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexWordRequest.Merge(m, src)
}
func (m *IndexWordRequest) XXX_Size() int {
	return xxx_messageInfo_IndexWordRequest.Size(m)
}
func (m *IndexWordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexWordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IndexWordRequest proto.InternalMessageInfo

func (m *IndexWordRequest) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *IndexWordRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type IndexHotWordResponse struct {
	// 200正常，400错误
	Code                 int32                                    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*IndexHotWordResponse_IndexHotWordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *IndexHotWordResponse) Reset()         { *m = IndexHotWordResponse{} }
func (m *IndexHotWordResponse) String() string { return proto.CompactTextString(m) }
func (*IndexHotWordResponse) ProtoMessage()    {}
func (*IndexHotWordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{56}
}

func (m *IndexHotWordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexHotWordResponse.Unmarshal(m, b)
}
func (m *IndexHotWordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexHotWordResponse.Marshal(b, m, deterministic)
}
func (m *IndexHotWordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexHotWordResponse.Merge(m, src)
}
func (m *IndexHotWordResponse) XXX_Size() int {
	return xxx_messageInfo_IndexHotWordResponse.Size(m)
}
func (m *IndexHotWordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexHotWordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IndexHotWordResponse proto.InternalMessageInfo

func (m *IndexHotWordResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IndexHotWordResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *IndexHotWordResponse) GetData() []*IndexHotWordResponse_IndexHotWordData {
	if m != nil {
		return m.Data
	}
	return nil
}

type IndexHotWordResponse_IndexHotWordData struct {
	// 热词名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 是否推荐，1-推荐（显示火的图标），2-不推荐
	IsRecom              int32    `protobuf:"varint,2,opt,name=is_recom,json=isRecom,proto3" json:"is_recom"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexHotWordResponse_IndexHotWordData) Reset()         { *m = IndexHotWordResponse_IndexHotWordData{} }
func (m *IndexHotWordResponse_IndexHotWordData) String() string { return proto.CompactTextString(m) }
func (*IndexHotWordResponse_IndexHotWordData) ProtoMessage()    {}
func (*IndexHotWordResponse_IndexHotWordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{56, 0}
}

func (m *IndexHotWordResponse_IndexHotWordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexHotWordResponse_IndexHotWordData.Unmarshal(m, b)
}
func (m *IndexHotWordResponse_IndexHotWordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexHotWordResponse_IndexHotWordData.Marshal(b, m, deterministic)
}
func (m *IndexHotWordResponse_IndexHotWordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexHotWordResponse_IndexHotWordData.Merge(m, src)
}
func (m *IndexHotWordResponse_IndexHotWordData) XXX_Size() int {
	return xxx_messageInfo_IndexHotWordResponse_IndexHotWordData.Size(m)
}
func (m *IndexHotWordResponse_IndexHotWordData) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexHotWordResponse_IndexHotWordData.DiscardUnknown(m)
}

var xxx_messageInfo_IndexHotWordResponse_IndexHotWordData proto.InternalMessageInfo

func (m *IndexHotWordResponse_IndexHotWordData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IndexHotWordResponse_IndexHotWordData) GetIsRecom() int32 {
	if m != nil {
		return m.IsRecom
	}
	return 0
}

type IndexDefaultWordResponse struct {
	// 200正常，400错误
	Code                 int32                                            `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string                                           `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*IndexDefaultWordResponse_IndexDefaultWordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-"`
	XXX_unrecognized     []byte                                           `json:"-"`
	XXX_sizecache        int32                                            `json:"-"`
}

func (m *IndexDefaultWordResponse) Reset()         { *m = IndexDefaultWordResponse{} }
func (m *IndexDefaultWordResponse) String() string { return proto.CompactTextString(m) }
func (*IndexDefaultWordResponse) ProtoMessage()    {}
func (*IndexDefaultWordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{57}
}

func (m *IndexDefaultWordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDefaultWordResponse.Unmarshal(m, b)
}
func (m *IndexDefaultWordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDefaultWordResponse.Marshal(b, m, deterministic)
}
func (m *IndexDefaultWordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDefaultWordResponse.Merge(m, src)
}
func (m *IndexDefaultWordResponse) XXX_Size() int {
	return xxx_messageInfo_IndexDefaultWordResponse.Size(m)
}
func (m *IndexDefaultWordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDefaultWordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDefaultWordResponse proto.InternalMessageInfo

func (m *IndexDefaultWordResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *IndexDefaultWordResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *IndexDefaultWordResponse) GetData() []*IndexDefaultWordResponse_IndexDefaultWordData {
	if m != nil {
		return m.Data
	}
	return nil
}

type IndexDefaultWordResponse_IndexDefaultWordData struct {
	// 默认词名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,2,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 落地页路径
	PathUrl              string   `protobuf:"bytes,3,opt,name=path_url,json=pathUrl,proto3" json:"path_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexDefaultWordResponse_IndexDefaultWordData) Reset() {
	*m = IndexDefaultWordResponse_IndexDefaultWordData{}
}
func (m *IndexDefaultWordResponse_IndexDefaultWordData) String() string {
	return proto.CompactTextString(m)
}
func (*IndexDefaultWordResponse_IndexDefaultWordData) ProtoMessage() {}
func (*IndexDefaultWordResponse_IndexDefaultWordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{57, 0}
}

func (m *IndexDefaultWordResponse_IndexDefaultWordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData.Unmarshal(m, b)
}
func (m *IndexDefaultWordResponse_IndexDefaultWordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData.Marshal(b, m, deterministic)
}
func (m *IndexDefaultWordResponse_IndexDefaultWordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData.Merge(m, src)
}
func (m *IndexDefaultWordResponse_IndexDefaultWordData) XXX_Size() int {
	return xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData.Size(m)
}
func (m *IndexDefaultWordResponse_IndexDefaultWordData) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDefaultWordResponse_IndexDefaultWordData proto.InternalMessageInfo

func (m *IndexDefaultWordResponse_IndexDefaultWordData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IndexDefaultWordResponse_IndexDefaultWordData) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *IndexDefaultWordResponse_IndexDefaultWordData) GetPathUrl() string {
	if m != nil {
		return m.PathUrl
	}
	return ""
}

// 后台接口
type WordData struct {
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 词名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,4,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 渠道，1-微信小程序，2-app，4-百度
	Channel int32 `protobuf:"varint,5,opt,name=channel,proto3" json:"channel"`
	// 是否推荐，1-推荐，2-不推荐
	IsRecom int32 `protobuf:"varint,3,opt,name=is_recom,json=isRecom,proto3" json:"is_recom"`
	// 是否显示，1-显示，2-不显示
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	// 落地页路径
	PathUrl string `protobuf:"bytes,7,opt,name=path_url,json=pathUrl,proto3" json:"path_url"`
	// 最后更新时间
	UpdatedAt string `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WordData) Reset()         { *m = WordData{} }
func (m *WordData) String() string { return proto.CompactTextString(m) }
func (*WordData) ProtoMessage()    {}
func (*WordData) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{58}
}

func (m *WordData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WordData.Unmarshal(m, b)
}
func (m *WordData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WordData.Marshal(b, m, deterministic)
}
func (m *WordData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WordData.Merge(m, src)
}
func (m *WordData) XXX_Size() int {
	return xxx_messageInfo_WordData.Size(m)
}
func (m *WordData) XXX_DiscardUnknown() {
	xxx_messageInfo_WordData.DiscardUnknown(m)
}

var xxx_messageInfo_WordData proto.InternalMessageInfo

func (m *WordData) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WordData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WordData) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *WordData) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *WordData) GetIsRecom() int32 {
	if m != nil {
		return m.IsRecom
	}
	return 0
}

func (m *WordData) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *WordData) GetPathUrl() string {
	if m != nil {
		return m.PathUrl
	}
	return ""
}

func (m *WordData) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *WordData) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type WordResponse struct {
	Code                 int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*WordData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	TotalCount           int64       `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *WordResponse) Reset()         { *m = WordResponse{} }
func (m *WordResponse) String() string { return proto.CompactTextString(m) }
func (*WordResponse) ProtoMessage()    {}
func (*WordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{59}
}

func (m *WordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WordResponse.Unmarshal(m, b)
}
func (m *WordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WordResponse.Marshal(b, m, deterministic)
}
func (m *WordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WordResponse.Merge(m, src)
}
func (m *WordResponse) XXX_Size() int {
	return xxx_messageInfo_WordResponse.Size(m)
}
func (m *WordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WordResponse proto.InternalMessageInfo

func (m *WordResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *WordResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *WordResponse) GetData() []*WordData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *WordResponse) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type WordRequest struct {
	// 默认搜索词
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,2,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 搜索渠道
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	// 类型，1-热词，2-默认词
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	// 页码
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页条数
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WordRequest) Reset()         { *m = WordRequest{} }
func (m *WordRequest) String() string { return proto.CompactTextString(m) }
func (*WordRequest) ProtoMessage()    {}
func (*WordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{60}
}

func (m *WordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WordRequest.Unmarshal(m, b)
}
func (m *WordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WordRequest.Marshal(b, m, deterministic)
}
func (m *WordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WordRequest.Merge(m, src)
}
func (m *WordRequest) XXX_Size() int {
	return xxx_messageInfo_WordRequest.Size(m)
}
func (m *WordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WordRequest proto.InternalMessageInfo

func (m *WordRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WordRequest) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *WordRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *WordRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WordRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *WordRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *WordRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type HotWordRequest struct {
	// 搜索词
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 页码
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页条数
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotWordRequest) Reset()         { *m = HotWordRequest{} }
func (m *HotWordRequest) String() string { return proto.CompactTextString(m) }
func (*HotWordRequest) ProtoMessage()    {}
func (*HotWordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{61}
}

func (m *HotWordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotWordRequest.Unmarshal(m, b)
}
func (m *HotWordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotWordRequest.Marshal(b, m, deterministic)
}
func (m *HotWordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotWordRequest.Merge(m, src)
}
func (m *HotWordRequest) XXX_Size() int {
	return xxx_messageInfo_HotWordRequest.Size(m)
}
func (m *HotWordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HotWordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HotWordRequest proto.InternalMessageInfo

func (m *HotWordRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HotWordRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *HotWordRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *HotWordRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type WordDeleteRequest struct {
	// 列表返回的id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 不用传，后端会处理
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WordDeleteRequest) Reset()         { *m = WordDeleteRequest{} }
func (m *WordDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*WordDeleteRequest) ProtoMessage()    {}
func (*WordDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{62}
}

func (m *WordDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WordDeleteRequest.Unmarshal(m, b)
}
func (m *WordDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WordDeleteRequest.Marshal(b, m, deterministic)
}
func (m *WordDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WordDeleteRequest.Merge(m, src)
}
func (m *WordDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_WordDeleteRequest.Size(m)
}
func (m *WordDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WordDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WordDeleteRequest proto.InternalMessageInfo

func (m *WordDeleteRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WordDeleteRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WordDeleteRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type HotWordOperateRequest struct {
	// id，有值表示修改，默认新增
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 是否推荐，1-推荐，2-不推荐
	IsRecom int32 `protobuf:"varint,3,opt,name=is_recom,json=isRecom,proto3" json:"is_recom"`
	// 词名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotWordOperateRequest) Reset()         { *m = HotWordOperateRequest{} }
func (m *HotWordOperateRequest) String() string { return proto.CompactTextString(m) }
func (*HotWordOperateRequest) ProtoMessage()    {}
func (*HotWordOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{63}
}

func (m *HotWordOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotWordOperateRequest.Unmarshal(m, b)
}
func (m *HotWordOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotWordOperateRequest.Marshal(b, m, deterministic)
}
func (m *HotWordOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotWordOperateRequest.Merge(m, src)
}
func (m *HotWordOperateRequest) XXX_Size() int {
	return xxx_messageInfo_HotWordOperateRequest.Size(m)
}
func (m *HotWordOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HotWordOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HotWordOperateRequest proto.InternalMessageInfo

func (m *HotWordOperateRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *HotWordOperateRequest) GetIsRecom() int32 {
	if m != nil {
		return m.IsRecom
	}
	return 0
}

func (m *HotWordOperateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HotWordOperateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type WordOperateRequest struct {
	// id，有值表示修改，默认新增
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 词名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 搜索渠道
	Channel int32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel"`
	// 落地页路径
	PathUrl string `protobuf:"bytes,5,opt,name=path_url,json=pathUrl,proto3" json:"path_url"`
	// 是否显示，1-显示，2-不显示
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	// 是否推荐，1-推荐，2-不推荐
	IsRecom int32 `protobuf:"varint,7,opt,name=is_recom,json=isRecom,proto3" json:"is_recom"`
	// 类型，1-热词，2-默认词
	Type int32 `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WordOperateRequest) Reset()         { *m = WordOperateRequest{} }
func (m *WordOperateRequest) String() string { return proto.CompactTextString(m) }
func (*WordOperateRequest) ProtoMessage()    {}
func (*WordOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{64}
}

func (m *WordOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WordOperateRequest.Unmarshal(m, b)
}
func (m *WordOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WordOperateRequest.Marshal(b, m, deterministic)
}
func (m *WordOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WordOperateRequest.Merge(m, src)
}
func (m *WordOperateRequest) XXX_Size() int {
	return xxx_messageInfo_WordOperateRequest.Size(m)
}
func (m *WordOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WordOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WordOperateRequest proto.InternalMessageInfo

func (m *WordOperateRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WordOperateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *WordOperateRequest) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *WordOperateRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *WordOperateRequest) GetPathUrl() string {
	if m != nil {
		return m.PathUrl
	}
	return ""
}

func (m *WordOperateRequest) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *WordOperateRequest) GetIsRecom() int32 {
	if m != nil {
		return m.IsRecom
	}
	return 0
}

func (m *WordOperateRequest) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *WordOperateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DefaultWordRequest struct {
	// 默认搜索词
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,2,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 搜索渠道
	Channel int32 `protobuf:"varint,3,opt,name=channel,proto3" json:"channel"`
	// 页码
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页条数
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DefaultWordRequest) Reset()         { *m = DefaultWordRequest{} }
func (m *DefaultWordRequest) String() string { return proto.CompactTextString(m) }
func (*DefaultWordRequest) ProtoMessage()    {}
func (*DefaultWordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{65}
}

func (m *DefaultWordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DefaultWordRequest.Unmarshal(m, b)
}
func (m *DefaultWordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DefaultWordRequest.Marshal(b, m, deterministic)
}
func (m *DefaultWordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DefaultWordRequest.Merge(m, src)
}
func (m *DefaultWordRequest) XXX_Size() int {
	return xxx_messageInfo_DefaultWordRequest.Size(m)
}
func (m *DefaultWordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DefaultWordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DefaultWordRequest proto.InternalMessageInfo

func (m *DefaultWordRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DefaultWordRequest) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *DefaultWordRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *DefaultWordRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *DefaultWordRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DefaultWordRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type DefaultWordOperateRequest struct {
	// id，有值表示修改，默认新增
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 词名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 实际搜索词
	RealName string `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name"`
	// 搜索渠道
	Channel int32 `protobuf:"varint,4,opt,name=channel,proto3" json:"channel"`
	// 落地页路径
	PathUrl string `protobuf:"bytes,5,opt,name=path_url,json=pathUrl,proto3" json:"path_url"`
	// 是否显示，1-显示，2-不显示
	IsShow int32 `protobuf:"varint,6,opt,name=is_show,json=isShow,proto3" json:"is_show"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DefaultWordOperateRequest) Reset()         { *m = DefaultWordOperateRequest{} }
func (m *DefaultWordOperateRequest) String() string { return proto.CompactTextString(m) }
func (*DefaultWordOperateRequest) ProtoMessage()    {}
func (*DefaultWordOperateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{66}
}

func (m *DefaultWordOperateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DefaultWordOperateRequest.Unmarshal(m, b)
}
func (m *DefaultWordOperateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DefaultWordOperateRequest.Marshal(b, m, deterministic)
}
func (m *DefaultWordOperateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DefaultWordOperateRequest.Merge(m, src)
}
func (m *DefaultWordOperateRequest) XXX_Size() int {
	return xxx_messageInfo_DefaultWordOperateRequest.Size(m)
}
func (m *DefaultWordOperateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DefaultWordOperateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DefaultWordOperateRequest proto.InternalMessageInfo

func (m *DefaultWordOperateRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DefaultWordOperateRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DefaultWordOperateRequest) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *DefaultWordOperateRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *DefaultWordOperateRequest) GetPathUrl() string {
	if m != nil {
		return m.PathUrl
	}
	return ""
}

func (m *DefaultWordOperateRequest) GetIsShow() int32 {
	if m != nil {
		return m.IsShow
	}
	return 0
}

func (m *DefaultWordOperateRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type KeywordsRequest struct {
	//搜索关键词
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//页码
	PageIndex int32 `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index"`
	// 每页数量，不传默认10
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,5,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeywordsRequest) Reset()         { *m = KeywordsRequest{} }
func (m *KeywordsRequest) String() string { return proto.CompactTextString(m) }
func (*KeywordsRequest) ProtoMessage()    {}
func (*KeywordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{67}
}

func (m *KeywordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeywordsRequest.Unmarshal(m, b)
}
func (m *KeywordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeywordsRequest.Marshal(b, m, deterministic)
}
func (m *KeywordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeywordsRequest.Merge(m, src)
}
func (m *KeywordsRequest) XXX_Size() int {
	return xxx_messageInfo_KeywordsRequest.Size(m)
}
func (m *KeywordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_KeywordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_KeywordsRequest proto.InternalMessageInfo

func (m *KeywordsRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KeywordsRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *KeywordsRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *KeywordsRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type KeywordsResponse struct {
	// 状态码，200正常，>=400出错
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*KeywordsResponse_List `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	// 总数
	Total                int32    `protobuf:"varint,4,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeywordsResponse) Reset()         { *m = KeywordsResponse{} }
func (m *KeywordsResponse) String() string { return proto.CompactTextString(m) }
func (*KeywordsResponse) ProtoMessage()    {}
func (*KeywordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{68}
}

func (m *KeywordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeywordsResponse.Unmarshal(m, b)
}
func (m *KeywordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeywordsResponse.Marshal(b, m, deterministic)
}
func (m *KeywordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeywordsResponse.Merge(m, src)
}
func (m *KeywordsResponse) XXX_Size() int {
	return xxx_messageInfo_KeywordsResponse.Size(m)
}
func (m *KeywordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_KeywordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_KeywordsResponse proto.InternalMessageInfo

func (m *KeywordsResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *KeywordsResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *KeywordsResponse) GetData() []*KeywordsResponse_List {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *KeywordsResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type KeywordsResponse_List struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	//状态 0未生效、1已生效 2待删除 4已删除
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeywordsResponse_List) Reset()         { *m = KeywordsResponse_List{} }
func (m *KeywordsResponse_List) String() string { return proto.CompactTextString(m) }
func (*KeywordsResponse_List) ProtoMessage()    {}
func (*KeywordsResponse_List) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{68, 0}
}

func (m *KeywordsResponse_List) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeywordsResponse_List.Unmarshal(m, b)
}
func (m *KeywordsResponse_List) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeywordsResponse_List.Marshal(b, m, deterministic)
}
func (m *KeywordsResponse_List) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeywordsResponse_List.Merge(m, src)
}
func (m *KeywordsResponse_List) XXX_Size() int {
	return xxx_messageInfo_KeywordsResponse_List.Size(m)
}
func (m *KeywordsResponse_List) XXX_DiscardUnknown() {
	xxx_messageInfo_KeywordsResponse_List.DiscardUnknown(m)
}

var xxx_messageInfo_KeywordsResponse_List proto.InternalMessageInfo

func (m *KeywordsResponse_List) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KeywordsResponse_List) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KeywordsResponse_List) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type KeywordStoreRequest struct {
	//热搜词
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeywordStoreRequest) Reset()         { *m = KeywordStoreRequest{} }
func (m *KeywordStoreRequest) String() string { return proto.CompactTextString(m) }
func (*KeywordStoreRequest) ProtoMessage()    {}
func (*KeywordStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{69}
}

func (m *KeywordStoreRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeywordStoreRequest.Unmarshal(m, b)
}
func (m *KeywordStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeywordStoreRequest.Marshal(b, m, deterministic)
}
func (m *KeywordStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeywordStoreRequest.Merge(m, src)
}
func (m *KeywordStoreRequest) XXX_Size() int {
	return xxx_messageInfo_KeywordStoreRequest.Size(m)
}
func (m *KeywordStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_KeywordStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_KeywordStoreRequest proto.InternalMessageInfo

func (m *KeywordStoreRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KeywordStoreRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

type KeywordDeleteRequest struct {
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	//主体：1-阿闻，2-极宠家 3.宠商云
	OrgId                int32    `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeywordDeleteRequest) Reset()         { *m = KeywordDeleteRequest{} }
func (m *KeywordDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*KeywordDeleteRequest) ProtoMessage()    {}
func (*KeywordDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2cb7b218608fdef, []int{70}
}

func (m *KeywordDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeywordDeleteRequest.Unmarshal(m, b)
}
func (m *KeywordDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeywordDeleteRequest.Marshal(b, m, deterministic)
}
func (m *KeywordDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeywordDeleteRequest.Merge(m, src)
}
func (m *KeywordDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_KeywordDeleteRequest.Size(m)
}
func (m *KeywordDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_KeywordDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_KeywordDeleteRequest proto.InternalMessageInfo

func (m *KeywordDeleteRequest) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KeywordDeleteRequest) GetOrgId() int32 {
	if m != nil {
		return m.OrgId
	}
	return 0
}

func init() {
	proto.RegisterType((*ContentResponse)(nil), "ctc.ContentResponse")
	proto.RegisterType((*ArticleSaveRequest)(nil), "ctc.ArticleSaveRequest")
	proto.RegisterType((*ArticleTagData)(nil), "ctc.ArticleTagData")
	proto.RegisterType((*ArticleListRequest)(nil), "ctc.ArticleListRequest")
	proto.RegisterType((*ArticleListResponse)(nil), "ctc.ArticleListResponse")
	proto.RegisterType((*ArticleListData)(nil), "ctc.ArticleListData")
	proto.RegisterType((*ArticleSearchConditionRequest)(nil), "ctc.ArticleSearchConditionRequest")
	proto.RegisterType((*ArticleSearchConditionResponse)(nil), "ctc.ArticleSearchConditionResponse")
	proto.RegisterType((*ArticleArr)(nil), "ctc.ArticleArr")
	proto.RegisterType((*ArticleListMiniResponse)(nil), "ctc.ArticleListMiniResponse")
	proto.RegisterType((*ArticleListMiniData)(nil), "ctc.ArticleListMiniData")
	proto.RegisterType((*ArticleDetailRequest)(nil), "ctc.ArticleDetailRequest")
	proto.RegisterType((*ArticleDetailResponse)(nil), "ctc.ArticleDetailResponse")
	proto.RegisterType((*ArticleDetail)(nil), "ctc.ArticleDetail")
	proto.RegisterType((*ArticleStatusRequest)(nil), "ctc.ArticleStatusRequest")
	proto.RegisterType((*ArticleEditHistoryListRequest)(nil), "ctc.ArticleEditHistoryListRequest")
	proto.RegisterType((*ArticleEditHistoryListResponse)(nil), "ctc.ArticleEditHistoryListResponse")
	proto.RegisterType((*ArticleEditRecord)(nil), "ctc.ArticleEditRecord")
	proto.RegisterType((*ArticleRecommendRequest)(nil), "ctc.ArticleRecommendRequest")
	proto.RegisterType((*ArticleRecommendResponse)(nil), "ctc.ArticleRecommendResponse")
	proto.RegisterType((*ArticleRecommend)(nil), "ctc.ArticleRecommend")
	proto.RegisterType((*ArticleCategorySaveRequest)(nil), "ctc.ArticleCategorySaveRequest")
	proto.RegisterType((*ArticleCategorySaveResponse)(nil), "ctc.ArticleCategorySaveResponse")
	proto.RegisterType((*ArticleCategoryDeleteRequest)(nil), "ctc.ArticleCategoryDeleteRequest")
	proto.RegisterType((*ArticleCategoryDeleteResponse)(nil), "ctc.ArticleCategoryDeleteResponse")
	proto.RegisterType((*ArticleCategoryListRequest)(nil), "ctc.ArticleCategoryListRequest")
	proto.RegisterType((*ArticleCategoryListResponse)(nil), "ctc.ArticleCategoryListResponse")
	proto.RegisterType((*FirstArticleCategory)(nil), "ctc.FirstArticleCategory")
	proto.RegisterType((*SecondArticleCategory)(nil), "ctc.SecondArticleCategory")
	proto.RegisterType((*ThirdArticleCategory)(nil), "ctc.ThirdArticleCategory")
	proto.RegisterType((*QueryDoctorListRequest)(nil), "ctc.QueryDoctorListRequest")
	proto.RegisterType((*QueryDoctorListResponse)(nil), "ctc.QueryDoctorListResponse")
	proto.RegisterType((*ScrmDoctor)(nil), "ctc.ScrmDoctor")
	proto.RegisterType((*ArticleCensusRequest)(nil), "ctc.ArticleCensusRequest")
	proto.RegisterType((*ArticleCensusResponse)(nil), "ctc.ArticleCensusResponse")
	proto.RegisterType((*ArticleCensus)(nil), "ctc.ArticleCensus")
	proto.RegisterType((*BoehringereCensusRequest)(nil), "ctc.BoehringereCensusRequest")
	proto.RegisterType((*BoehringereCensusResponse)(nil), "ctc.BoehringereCensusResponse")
	proto.RegisterType((*BoehringereCensus)(nil), "ctc.BoehringereCensus")
	proto.RegisterType((*AddVisitRecordRequest)(nil), "ctc.AddVisitRecordRequest")
	proto.RegisterType((*BoehringereChickRecord)(nil), "ctc.BoehringereChickRecord")
	proto.RegisterType((*CategoryBarRequest)(nil), "ctc.CategoryBarRequest")
	proto.RegisterType((*CategoryBarResponse)(nil), "ctc.CategoryBarResponse")
	proto.RegisterType((*Category)(nil), "ctc.Category")
	proto.RegisterType((*EmptyRequest)(nil), "ctc.EmptyRequest")
	proto.RegisterType((*OrgIdRequest)(nil), "ctc.OrgIdRequest")
	proto.RegisterType((*ArticleTagGroup)(nil), "ctc.ArticleTagGroup")
	proto.RegisterType((*ArticleMiniFilterResponse)(nil), "ctc.ArticleMiniFilterResponse")
	proto.RegisterType((*GetStatisticsResponse)(nil), "ctc.GetStatisticsResponse")
	proto.RegisterType((*AddClickRecordRequest)(nil), "ctc.AddClickRecordRequest")
	proto.RegisterType((*FindClickRecordsRequest)(nil), "ctc.FindClickRecordsRequest")
	proto.RegisterType((*BiliAdClickRecord)(nil), "ctc.BiliAdClickRecord")
	proto.RegisterType((*FindClickRecordsResponse)(nil), "ctc.FindClickRecordsResponse")
	proto.RegisterType((*SaveConversionRequest)(nil), "ctc.SaveConversionRequest")
	proto.RegisterType((*JsonStrRequest)(nil), "ctc.JsonStrRequest")
	proto.RegisterType((*IndexWordRequest)(nil), "ctc.IndexWordRequest")
	proto.RegisterType((*IndexHotWordResponse)(nil), "ctc.IndexHotWordResponse")
	proto.RegisterType((*IndexHotWordResponse_IndexHotWordData)(nil), "ctc.IndexHotWordResponse.IndexHotWordData")
	proto.RegisterType((*IndexDefaultWordResponse)(nil), "ctc.IndexDefaultWordResponse")
	proto.RegisterType((*IndexDefaultWordResponse_IndexDefaultWordData)(nil), "ctc.IndexDefaultWordResponse.IndexDefaultWordData")
	proto.RegisterType((*WordData)(nil), "ctc.WordData")
	proto.RegisterType((*WordResponse)(nil), "ctc.WordResponse")
	proto.RegisterType((*WordRequest)(nil), "ctc.WordRequest")
	proto.RegisterType((*HotWordRequest)(nil), "ctc.HotWordRequest")
	proto.RegisterType((*WordDeleteRequest)(nil), "ctc.WordDeleteRequest")
	proto.RegisterType((*HotWordOperateRequest)(nil), "ctc.HotWordOperateRequest")
	proto.RegisterType((*WordOperateRequest)(nil), "ctc.WordOperateRequest")
	proto.RegisterType((*DefaultWordRequest)(nil), "ctc.DefaultWordRequest")
	proto.RegisterType((*DefaultWordOperateRequest)(nil), "ctc.DefaultWordOperateRequest")
	proto.RegisterType((*KeywordsRequest)(nil), "ctc.KeywordsRequest")
	proto.RegisterType((*KeywordsResponse)(nil), "ctc.KeywordsResponse")
	proto.RegisterType((*KeywordsResponse_List)(nil), "ctc.KeywordsResponse.List")
	proto.RegisterType((*KeywordStoreRequest)(nil), "ctc.KeywordStoreRequest")
	proto.RegisterType((*KeywordDeleteRequest)(nil), "ctc.KeywordDeleteRequest")
}

func init() { proto.RegisterFile("ctc/contentCenter.proto", fileDescriptor_d2cb7b218608fdef) }

var fileDescriptor_d2cb7b218608fdef = []byte{
	// 3596 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5b, 0xcf, 0x8f, 0x1c, 0x47,
	0xf5, 0x57, 0xcf, 0xef, 0x79, 0xb3, 0xb3, 0x3f, 0xda, 0xbb, 0xde, 0xde, 0x75, 0x36, 0x76, 0xda,
	0x71, 0xe2, 0x6f, 0xf4, 0xc5, 0x31, 0x9b, 0x03, 0x01, 0x41, 0xc8, 0x7a, 0x1d, 0xc7, 0x1b, 0x62,
	0x3b, 0xcc, 0xae, 0x43, 0x4e, 0x8c, 0x7a, 0xbb, 0xcb, 0xb3, 0x15, 0xf7, 0x74, 0x4f, 0xba, 0x6b,
	0xd6, 0xd9, 0x1c, 0x10, 0x48, 0x5c, 0xe1, 0x84, 0x90, 0x50, 0x24, 0x2e, 0x70, 0x83, 0x0b, 0x1c,
	0x38, 0x21, 0x45, 0x5c, 0x40, 0xe2, 0x82, 0xc4, 0x81, 0x03, 0x7f, 0x01, 0xbf, 0xc4, 0x89, 0x0b,
	0x37, 0x54, 0xaf, 0xaa, 0xbb, 0xab, 0xfa, 0xc7, 0xec, 0x66, 0x9c, 0x40, 0x82, 0x38, 0xed, 0xf4,
	0xab, 0xaa, 0x57, 0x55, 0xef, 0x7d, 0xde, 0x8f, 0x7a, 0x55, 0x0b, 0xeb, 0x2e, 0x73, 0x9f, 0x77,
	0xc3, 0x80, 0x91, 0x80, 0xed, 0x92, 0x80, 0x91, 0xe8, 0xda, 0x24, 0x0a, 0x59, 0x68, 0xd6, 0x5d,
	0xe6, 0xda, 0x5f, 0x86, 0xa5, 0x5d, 0xd1, 0x36, 0x20, 0xf1, 0x24, 0x0c, 0x62, 0x62, 0x9a, 0xd0,
	0x70, 0x43, 0x8f, 0x58, 0xc6, 0x25, 0xe3, 0x6a, 0x73, 0x80, 0xbf, 0x4d, 0x0b, 0xda, 0x63, 0x12,
	0xc7, 0xce, 0x88, 0x58, 0xb5, 0x4b, 0xc6, 0xd5, 0xee, 0x20, 0xf9, 0xb4, 0x7f, 0xd7, 0x00, 0x73,
	0x27, 0x62, 0xd4, 0xf5, 0xc9, 0xbe, 0x73, 0x4c, 0x06, 0xe4, 0x9d, 0x29, 0x89, 0x99, 0x79, 0x11,
	0x7a, 0x8c, 0x8c, 0x27, 0xbe, 0xc3, 0xc8, 0x90, 0x7a, 0x92, 0x17, 0x24, 0xa4, 0x3d, 0xcf, 0xbc,
	0x02, 0x8b, 0xae, 0xc3, 0xc8, 0x28, 0x8c, 0x4e, 0x86, 0x0f, 0x68, 0x14, 0x33, 0x64, 0xdc, 0x1c,
	0xf4, 0x13, 0xea, 0x2d, 0x4e, 0x34, 0x9f, 0x85, 0xa5, 0xb4, 0x5b, 0x4c, 0xdc, 0x30, 0xf0, 0xac,
	0x3a, 0xf6, 0x4b, 0x47, 0xef, 0x23, 0x55, 0xe3, 0xc7, 0x8e, 0x68, 0xe4, 0x59, 0x0d, 0x9d, 0xdf,
	0x01, 0x27, 0x9a, 0x17, 0xa0, 0x7b, 0x4c, 0x3d, 0x12, 0x0e, 0xa7, 0x91, 0x6f, 0x35, 0x71, 0x2b,
	0x1d, 0x24, 0xdc, 0x8f, 0x7c, 0xde, 0xe8, 0x86, 0xc7, 0x24, 0xc2, 0xc6, 0x96, 0x68, 0x44, 0x02,
	0x6f, 0x5c, 0x85, 0x26, 0xa3, 0xcc, 0x27, 0x56, 0x1b, 0x1b, 0xc4, 0x07, 0x17, 0x8c, 0x94, 0xad,
	0xd5, 0x11, 0x82, 0x91, 0x9f, 0x5c, 0x02, 0x5e, 0xe8, 0xb2, 0x30, 0x1a, 0xa2, 0x34, 0xbb, 0xd8,
	0x0a, 0x82, 0xb4, 0xcb, 0x65, 0xfa, 0x34, 0x2c, 0x26, 0x1d, 0x48, 0xc4, 0x86, 0x41, 0x68, 0x01,
	0xf6, 0x59, 0x90, 0x7d, 0x48, 0xc4, 0xee, 0x86, 0xe6, 0x06, 0x74, 0x98, 0x33, 0x1a, 0xbe, 0x1d,
	0x87, 0x81, 0xd5, 0x13, 0x33, 0x30, 0x67, 0xf4, 0x5a, 0x1c, 0x06, 0x38, 0x03, 0x8d, 0x87, 0xee,
	0x91, 0x13, 0x04, 0xc4, 0xb7, 0x16, 0x2e, 0xd5, 0xb9, 0x8c, 0x3d, 0x1a, 0xef, 0x0a, 0x8a, 0xf9,
	0x24, 0xf4, 0x68, 0x3c, 0x8c, 0x8f, 0xc2, 0x47, 0x43, 0xc7, 0x8b, 0xad, 0x3e, 0x0a, 0xa4, 0x4b,
	0xe3, 0xfd, 0xa3, 0xf0, 0xd1, 0x8e, 0x17, 0xf3, 0x15, 0x84, 0x81, 0x4f, 0x03, 0x32, 0x74, 0xe2,
	0x87, 0xb8, 0xe9, 0x45, 0xb1, 0x02, 0x41, 0xdd, 0x89, 0x1f, 0xf2, 0x8d, 0x3f, 0x05, 0x0b, 0x8e,
	0x50, 0xf0, 0x90, 0x9d, 0x4c, 0x88, 0xb5, 0x8c, 0x6c, 0x7a, 0x92, 0x76, 0x70, 0x32, 0x21, 0xe6,
	0x16, 0x40, 0xd2, 0x85, 0x7a, 0xd6, 0x8a, 0x98, 0x47, 0x52, 0xf6, 0x3c, 0xf3, 0x3c, 0xb4, 0x62,
	0xe6, 0xb0, 0x69, 0x6c, 0x99, 0xd8, 0x24, 0xbf, 0xb8, 0x48, 0xc3, 0x68, 0xb4, 0xe7, 0x59, 0xe7,
	0x90, 0x2c, 0x3e, 0xec, 0x65, 0x58, 0x94, 0x80, 0x3a, 0x70, 0x46, 0x37, 0x1d, 0xe6, 0xd8, 0x3f,
	0xac, 0xa7, 0x18, 0x7b, 0x9d, 0xc6, 0x2c, 0xc1, 0x58, 0xaa, 0x11, 0x43, 0xd5, 0x48, 0x26, 0xf7,
	0xc0, 0x19, 0x27, 0x70, 0x95, 0x72, 0xbf, 0xeb, 0x8c, 0x49, 0x61, 0x3f, 0xf5, 0xe2, 0x7e, 0x8a,
	0xe0, 0x6c, 0x9c, 0x11, 0x9c, 0xcd, 0x33, 0x82, 0xb3, 0x55, 0x06, 0xce, 0x9c, 0x42, 0xdb, 0xc2,
	0x68, 0x14, 0x85, 0x6e, 0x01, 0x4c, 0x9c, 0x11, 0x19, 0xd2, 0xc0, 0x23, 0xef, 0x22, 0xe0, 0x9a,
	0x83, 0x2e, 0xa7, 0xec, 0x71, 0x02, 0xc7, 0x2f, 0x36, 0xc7, 0xf4, 0x3d, 0x01, 0xb8, 0xe6, 0xa0,
	0xc3, 0x09, 0xfb, 0xf4, 0x3d, 0xa2, 0x28, 0x01, 0x34, 0x25, 0x98, 0xd0, 0x60, 0xce, 0x28, 0x96,
	0xe0, 0xc2, 0xdf, 0x1c, 0xd5, 0x91, 0x13, 0x78, 0x77, 0xa7, 0x63, 0x6b, 0x01, 0x3b, 0x27, 0x9f,
	0x99, 0xca, 0xfa, 0xaa, 0xca, 0xbe, 0x6b, 0xc0, 0x39, 0x4d, 0x41, 0xf3, 0xb8, 0x12, 0xf3, 0x2a,
	0x34, 0x3c, 0x87, 0x39, 0x56, 0xfd, 0x52, 0xfd, 0x6a, 0x6f, 0x7b, 0xf5, 0x9a, 0xcb, 0xdc, 0x6b,
	0x0a, 0x57, 0x0e, 0x85, 0x01, 0xf6, 0x40, 0xef, 0x12, 0x32, 0xc7, 0x1f, 0xba, 0xe1, 0x34, 0x10,
	0xca, 0xa9, 0x0f, 0x00, 0x49, 0xbb, 0x9c, 0x62, 0xff, 0xac, 0x06, 0x4b, 0xb9, 0xa1, 0x39, 0x90,
	0x1a, 0x79, 0x90, 0xa6, 0x68, 0xaa, 0xa9, 0x68, 0x3a, 0x03, 0x58, 0x72, 0x80, 0x6b, 0x14, 0x00,
	0xb7, 0x05, 0xe0, 0x46, 0xc4, 0x61, 0xc4, 0x1b, 0x3a, 0x4c, 0x3a, 0x9d, 0xae, 0xa4, 0xec, 0x30,
	0xf3, 0x39, 0x58, 0xf1, 0x9d, 0x98, 0x0d, 0x27, 0xd3, 0x43, 0x9f, 0xc6, 0x47, 0x43, 0x46, 0xc7,
	0x44, 0x7a, 0x9f, 0x25, 0xde, 0xf0, 0x86, 0xa0, 0x1f, 0x50, 0xc1, 0x6a, 0x3a, 0xf1, 0x12, 0x56,
	0xc2, 0x13, 0x75, 0x25, 0x65, 0x87, 0x99, 0x97, 0xa1, 0x8f, 0xac, 0xc2, 0x09, 0x89, 0x1c, 0x16,
	0x46, 0xd2, 0x27, 0x2d, 0x70, 0xe2, 0x3d, 0x49, 0x53, 0x80, 0x20, 0x7c, 0x92, 0xfc, 0xb2, 0xf7,
	0x60, 0x2b, 0x71, 0xe4, 0xc4, 0x89, 0xdc, 0xa3, 0xdd, 0x30, 0xf0, 0x28, 0xa3, 0x61, 0x90, 0xd8,
	0x9b, 0x09, 0x8d, 0x07, 0x51, 0x38, 0x96, 0xe6, 0x86, 0xbf, 0x33, 0x3c, 0xd4, 0x54, 0x3c, 0xfc,
	0xa8, 0x06, 0x4f, 0x56, 0xf1, 0x9a, 0x0b, 0x1a, 0xdb, 0x05, 0x35, 0x70, 0x88, 0x2c, 0xa9, 0x10,
	0xd9, 0x89, 0x22, 0x5d, 0x2f, 0xd7, 0x75, 0x6b, 0x6a, 0x94, 0x0f, 0x51, 0xcd, 0xeb, 0xd9, 0x54,
	0x32, 0xcd, 0xf2, 0xce, 0x89, 0xcd, 0xdc, 0x84, 0xe5, 0x64, 0x39, 0x89, 0x05, 0x5b, 0x2d, 0x1c,
	0xb2, 0x81, 0x43, 0xd0, 0x3d, 0xc8, 0x71, 0xbb, 0xb2, 0xc3, 0x60, 0xc9, 0xd1, 0x09, 0xf6, 0x75,
	0x80, 0x8c, 0xb7, 0xb9, 0x08, 0xb5, 0x14, 0x96, 0x35, 0xea, 0x71, 0x01, 0x29, 0x0e, 0x0c, 0x7f,
	0xdb, 0xdf, 0x37, 0x60, 0x5d, 0x81, 0xf5, 0x1d, 0x1a, 0xd0, 0x39, 0x05, 0xfa, 0xff, 0x9a, 0xad,
	0x59, 0x79, 0x5b, 0xe3, 0x9c, 0x3f, 0x8c, 0xbd, 0xfd, 0xa0, 0xae, 0x39, 0x80, 0x64, 0x78, 0x89,
	0xcd, 0xd5, 0xff, 0x3d, 0x36, 0x77, 0x1d, 0x56, 0x65, 0x87, 0x49, 0x18, 0x23, 0xf2, 0x44, 0x4f,
	0x61, 0x7d, 0xa6, 0x68, 0x7b, 0x43, 0x36, 0xe1, 0x88, 0x4d, 0xe8, 0x1c, 0x85, 0xf1, 0x84, 0x32,
	0x27, 0x8d, 0xfd, 0xc9, 0x77, 0x49, 0xa8, 0x6e, 0x97, 0x84, 0x6a, 0x2d, 0xb7, 0xe8, 0xcc, 0xca,
	0x2d, 0xba, 0xb9, 0xdc, 0x62, 0x03, 0x3a, 0x47, 0xc4, 0xf1, 0x86, 0x74, 0x3c, 0x92, 0x49, 0x40,
	0x9b, 0x7f, 0xef, 0x8d, 0x47, 0x5c, 0x18, 0x9a, 0x63, 0x10, 0x6e, 0xba, 0x37, 0xa9, 0x74, 0x0a,
	0x0b, 0x39, 0xa7, 0x60, 0xef, 0xc1, 0xaa, 0x54, 0xcd, 0x4d, 0xc2, 0x1c, 0xea, 0x27, 0xe6, 0x7c,
	0x8a, 0x3f, 0x4c, 0xac, 0xbd, 0x96, 0x59, 0xbb, 0xfd, 0x0e, 0xac, 0xe5, 0x58, 0xcd, 0x05, 0xbe,
	0xe7, 0xa0, 0xe5, 0xe1, 0x78, 0x54, 0x6d, 0x6f, 0xdb, 0x54, 0xe1, 0x27, 0x39, 0xcb, 0x1e, 0xf6,
	0x5f, 0x9b, 0xd0, 0xd7, 0x5a, 0xfe, 0x97, 0x5a, 0xfe, 0x27, 0x52, 0xcb, 0x7c, 0x26, 0xf2, 0x0c,
	0x2c, 0x29, 0x1d, 0x86, 0x4e, 0x14, 0x59, 0x16, 0xe6, 0x9f, 0xfd, 0xac, 0x13, 0xf7, 0x6a, 0x9f,
	0x94, 0x14, 0x54, 0xb7, 0x11, 0x33, 0x1f, 0x38, 0x73, 0xfe, 0x64, 0xb5, 0x2c, 0x69, 0x94, 0x1d,
	0x7c, 0x72, 0x4c, 0x7c, 0x6b, 0x4d, 0x98, 0xa1, 0xa0, 0xbd, 0xce, 0x49, 0x7c, 0x0a, 0xd9, 0x85,
	0x9b, 0xf1, 0x79, 0x31, 0x85, 0xa0, 0x70, 0x43, 0xbe, 0x0c, 0xfd, 0xc4, 0x9f, 0x88, 0x49, 0xd6,
	0xc5, 0x46, 0x13, 0x22, 0x9f, 0xc6, 0xbe, 0x93, 0xda, 0xea, 0x3e, 0x46, 0x9a, 0x33, 0xda, 0x6a,
	0x16, 0xd2, 0x6b, 0x6a, 0x6e, 0x67, 0xff, 0xc4, 0x48, 0x63, 0xfa, 0x2b, 0x1e, 0x65, 0xb7, 0x69,
	0xcc, 0xc2, 0xe8, 0x44, 0xcd, 0xa1, 0x4f, 0x77, 0x02, 0x28, 0x70, 0xc1, 0x16, 0x7f, 0x23, 0x94,
	0x31, 0x79, 0xe1, 0x23, 0xea, 0x12, 0xca, 0x48, 0x10, 0x72, 0x56, 0x32, 0xd4, 0xc6, 0xcc, 0x0c,
	0xb5, 0xa9, 0x67, 0xa8, 0xf6, 0xfb, 0x46, 0x9a, 0x35, 0x14, 0x56, 0x3b, 0xa7, 0x9f, 0x51, 0x83,
	0xdc, 0x79, 0xd5, 0xcb, 0xf0, 0x09, 0x06, 0xc4, 0x0d, 0x23, 0xaf, 0x3a, 0xc4, 0x35, 0xb5, 0x10,
	0xf7, 0x47, 0x03, 0x56, 0x0a, 0x83, 0x0b, 0x51, 0x5b, 0x97, 0x67, 0xad, 0x4a, 0x9e, 0x75, 0x45,
	0x9e, 0x57, 0x60, 0x51, 0xe4, 0x6b, 0x3c, 0x48, 0xe1, 0x7a, 0x45, 0x38, 0xeb, 0xa7, 0x54, 0x0c,
	0xa5, 0x9a, 0xd8, 0x9b, 0x39, 0xb1, 0x5f, 0x84, 0x9e, 0x6c, 0x44, 0x68, 0x09, 0x07, 0x23, 0xb3,
	0xce, 0x92, 0x1c, 0xb4, 0x9d, 0xcb, 0x41, 0xed, 0x17, 0xd3, 0xbc, 0x82, 0xef, 0x6b, 0x3c, 0x26,
	0x81, 0x57, 0x8d, 0x10, 0x35, 0x84, 0xdb, 0xdf, 0x33, 0xc0, 0x2a, 0x0e, 0x9d, 0x4b, 0x5d, 0x39,
	0x15, 0xd4, 0xf3, 0x2a, 0x30, 0xff, 0x4f, 0xea, 0x53, 0x64, 0x67, 0x6b, 0xaa, 0x3e, 0xb3, 0xb9,
	0xb1, 0x8b, 0xfd, 0xcb, 0x1a, 0x2c, 0xe7, 0x9b, 0x0a, 0xca, 0x2a, 0x4f, 0x3f, 0x56, 0xa1, 0x89,
	0x9e, 0x59, 0x62, 0x5b, 0x7c, 0xe4, 0x7d, 0x6e, 0xa3, 0xe0, 0x73, 0x73, 0x2e, 0xa4, 0x59, 0x70,
	0x21, 0x97, 0xa0, 0xc7, 0xbd, 0x31, 0x7d, 0x40, 0x79, 0xdc, 0x90, 0x3a, 0x52, 0x49, 0x45, 0x17,
	0xd1, 0x2e, 0xba, 0x88, 0x82, 0x27, 0xea, 0x9c, 0xe6, 0x89, 0xba, 0x79, 0x4f, 0x94, 0xf7, 0xa6,
	0x50, 0xf0, 0xa6, 0xf6, 0xdf, 0x0d, 0xd8, 0xcc, 0xe5, 0xaf, 0x6a, 0x75, 0x27, 0x2f, 0xc8, 0xcb,
	0x90, 0xc6, 0x42, 0xf5, 0xd4, 0xbd, 0x90, 0x10, 0x71, 0xe1, 0x68, 0xfb, 0x11, 0x09, 0x58, 0xe2,
	0x37, 0xd0, 0xf6, 0x39, 0x41, 0x64, 0x82, 0xc7, 0x8e, 0x4f, 0x93, 0xd0, 0x2a, 0x3e, 0x1e, 0x13,
	0xf3, 0xab, 0xd0, 0x14, 0x22, 0x12, 0x07, 0x69, 0xf1, 0x91, 0x9d, 0x58, 0x3a, 0xea, 0x89, 0x65,
	0x08, 0x17, 0x4a, 0xf7, 0x3b, 0x17, 0x90, 0xcd, 0xd4, 0xef, 0x60, 0x6f, 0x04, 0xe4, 0x7d, 0x78,
	0x22, 0x37, 0xc1, 0x4d, 0xe2, 0x13, 0xa6, 0x16, 0xcc, 0x52, 0x11, 0x66, 0x59, 0x4d, 0x42, 0x12,
	0x12, 0x12, 0xbb, 0xa9, 0x29, 0xbb, 0xb1, 0xef, 0xa4, 0x0e, 0x3e, 0xcf, 0x76, 0xae, 0x6a, 0xde,
	0x76, 0x41, 0xed, 0xb9, 0x82, 0x8b, 0x10, 0x9d, 0xa1, 0x8a, 0xee, 0xbd, 0x82, 0xe8, 0x1e, 0xc3,
	0x65, 0x7f, 0x46, 0x73, 0xd9, 0x33, 0x4e, 0x53, 0x42, 0xaa, 0xdf, 0xae, 0xc1, 0x6a, 0x59, 0xf3,
	0xa7, 0x1a, 0xa1, 0xd7, 0xa1, 0xe9, 0x1e, 0x51, 0x9f, 0x23, 0x94, 0x0b, 0x61, 0x13, 0x85, 0x20,
	0x72, 0xd1, 0xbc, 0x14, 0x44, 0x47, 0x2e, 0x86, 0xb5, 0xd2, 0x0e, 0x9f, 0x6a, 0x39, 0x3c, 0xaf,
	0xcb, 0x41, 0x80, 0x01, 0x73, 0xed, 0x0a, 0x31, 0xfc, 0xde, 0x80, 0xd5, 0xb2, 0xf6, 0x4f, 0xb3,
	0x14, 0xec, 0x6d, 0x38, 0xff, 0xd5, 0x29, 0x89, 0x4e, 0x6e, 0x0a, 0x07, 0xaf, 0x98, 0xa3, 0x05,
	0xed, 0x87, 0xe4, 0xe4, 0x51, 0x18, 0x79, 0xb2, 0x24, 0x93, 0x7c, 0xda, 0x3e, 0xac, 0x17, 0xc6,
	0xcc, 0x65, 0x8e, 0x97, 0x35, 0x73, 0x14, 0xf5, 0x90, 0x7d, 0x37, 0x1a, 0x0b, 0xc6, 0xd2, 0x08,
	0xff, 0x69, 0x00, 0x64, 0xc4, 0x7c, 0xa4, 0x34, 0x4e, 0x8b, 0x94, 0xb5, 0x53, 0x93, 0xed, 0xfa,
	0xac, 0x10, 0x17, 0xcb, 0x3c, 0x33, 0x0d, 0x71, 0xfb, 0xe4, 0xdd, 0x5c, 0x04, 0x6c, 0xce, 0xca,
	0xc5, 0x71, 0x91, 0x2d, 0x3d, 0xd0, 0xe2, 0x32, 0xcf, 0x12, 0x8d, 0xed, 0x3f, 0x18, 0x69, 0xc6,
	0xbe, 0x4b, 0x82, 0x58, 0xcb, 0xd8, 0x0f, 0xc9, 0x88, 0x06, 0xe2, 0xd4, 0x2e, 0x84, 0xd0, 0x45,
	0x0a, 0x9e, 0xd9, 0x37, 0xa0, 0x43, 0x02, 0x4f, 0x34, 0x4a, 0x99, 0x93, 0xc0, 0xc3, 0x26, 0x7e,
	0xee, 0x93, 0xe7, 0x2e, 0x81, 0xba, 0xe4, 0xf3, 0x71, 0x92, 0x6b, 0x15, 0x2c, 0x2d, 0x0d, 0x2c,
	0x99, 0x57, 0x6f, 0xe7, 0x4a, 0xba, 0x6b, 0xb9, 0x8d, 0x7d, 0x3c, 0x49, 0xdd, 0x33, 0x12, 0x62,
	0xa2, 0x3e, 0xa7, 0x95, 0x02, 0xe4, 0xc4, 0x02, 0x65, 0xbf, 0x30, 0xd2, 0x42, 0x80, 0xa0, 0x9f,
	0x56, 0x5c, 0xba, 0x0c, 0xfd, 0x34, 0xcd, 0x51, 0xb2, 0xbc, 0x24, 0xf7, 0x39, 0xc0, 0x64, 0xef,
	0x0a, 0x2c, 0x4e, 0x03, 0xfa, 0xce, 0x94, 0x0c, 0x8f, 0x69, 0x4c, 0x59, 0x18, 0xc9, 0x15, 0xf6,
	0x05, 0xf5, 0x4d, 0x41, 0x4c, 0x45, 0x7b, 0x4c, 0xc9, 0x23, 0x29, 0x78, 0x14, 0xed, 0x9b, 0x94,
	0x3c, 0xe2, 0xeb, 0x10, 0x07, 0x7f, 0x6c, 0x15, 0x82, 0x17, 0xa5, 0x00, 0xde, 0x6c, 0x1f, 0x80,
	0x75, 0x23, 0x24, 0x47, 0x11, 0x0d, 0x46, 0x24, 0xfa, 0xa8, 0x50, 0x62, 0x4f, 0x61, 0xa3, 0x84,
	0xeb, 0x47, 0x76, 0x4c, 0x2a, 0xf2, 0x16, 0x5a, 0xf8, 0x96, 0x01, 0x2b, 0x85, 0xb6, 0xf4, 0x58,
	0x63, 0x28, 0xc7, 0x9a, 0x2d, 0x00, 0xfe, 0x77, 0x28, 0x5c, 0xb8, 0x3c, 0x09, 0x71, 0xca, 0x2e,
	0x27, 0xcc, 0x40, 0x79, 0x0e, 0x31, 0xcd, 0xc2, 0x49, 0xec, 0x3b, 0x1c, 0x9a, 0x9e, 0x87, 0xba,
	0x91, 0x67, 0xb8, 0x4c, 0x9c, 0xb2, 0x46, 0xa2, 0x9c, 0x66, 0x25, 0xa5, 0xe2, 0x34, 0x5b, 0xbd,
	0x8e, 0x75, 0x68, 0x4f, 0x63, 0x12, 0x0d, 0x83, 0x50, 0xfa, 0x97, 0x16, 0xff, 0xbc, 0x1b, 0x62,
	0x34, 0x99, 0x48, 0xa7, 0x52, 0xa3, 0x13, 0x9b, 0xc1, 0x79, 0x55, 0x24, 0x47, 0xd4, 0x7d, 0x28,
	0x4f, 0x87, 0x73, 0xc8, 0x45, 0x99, 0xb5, 0x5e, 0x32, 0x6b, 0x23, 0x9d, 0x75, 0x1f, 0xcc, 0x24,
	0xbe, 0xdd, 0x70, 0x22, 0xa5, 0x46, 0x5f, 0x98, 0x31, 0xb9, 0xe1, 0xa9, 0x29, 0x37, 0x3c, 0x6b,
	0xd0, 0x0a, 0xa3, 0x51, 0x16, 0xd9, 0xa4, 0xd5, 0x3f, 0x80, 0x73, 0x1a, 0xd3, 0xb9, 0xf0, 0xf4,
	0x94, 0x86, 0xa7, 0x3e, 0xe2, 0x29, 0x97, 0xb7, 0xfd, 0xda, 0x80, 0xce, 0x7f, 0x45, 0x74, 0x5e,
	0x84, 0x85, 0x57, 0xc6, 0x13, 0x76, 0x22, 0xe5, 0x6f, 0x5f, 0x81, 0x85, 0x7b, 0x5c, 0x92, 0x89,
	0x3e, 0x32, 0x39, 0x6b, 0x39, 0xf3, 0xe7, 0xd3, 0xeb, 0xa9, 0x03, 0x67, 0xf4, 0x6a, 0x14, 0x4e,
	0x27, 0x69, 0xbd, 0xdf, 0xc8, 0xea, 0xfd, 0x65, 0x9a, 0xb3, 0xbf, 0x01, 0x1b, 0x72, 0xe8, 0x1d,
	0x1a, 0xd0, 0x5b, 0xd4, 0x67, 0x64, 0x5e, 0x45, 0xbd, 0x00, 0xc0, 0x9c, 0xd1, 0x70, 0xc4, 0xe7,
	0x8f, 0xcb, 0xae, 0xdd, 0x92, 0xc5, 0x0d, 0xba, 0x4c, 0xfe, 0x8a, 0x6d, 0x07, 0xd6, 0x5e, 0x25,
	0x6c, 0x9f, 0x39, 0x8c, 0xc6, 0x8c, 0xba, 0x99, 0xd3, 0xb1, 0xd3, 0x63, 0x25, 0x9a, 0xa9, 0x5c,
	0x83, 0x46, 0x33, 0x9f, 0xce, 0x54, 0x2b, 0x3a, 0xe5, 0x2a, 0xb3, 0xc2, 0xc0, 0xff, 0x56, 0x47,
	0x03, 0xdf, 0xf5, 0x53, 0x4b, 0x4a, 0xc4, 0xb9, 0x01, 0x1d, 0x16, 0x39, 0xee, 0xc3, 0x44, 0xa0,
	0xdd, 0x41, 0x1b, 0xbf, 0x45, 0xe5, 0xc9, 0x71, 0xd1, 0x65, 0x24, 0x95, 0x97, 0xee, 0xa0, 0x2b,
	0x29, 0x52, 0xbf, 0xce, 0x78, 0xe2, 0xd0, 0x51, 0x90, 0xd5, 0xad, 0x20, 0x21, 0xed, 0x09, 0xc3,
	0x0b, 0x28, 0x0e, 0x4e, 0xcc, 0x3d, 0xa0, 0x4c, 0x41, 0x06, 0x3d, 0x56, 0x80, 0x03, 0x09, 0x69,
	0x0f, 0xcb, 0x0a, 0x61, 0x8c, 0x88, 0xa9, 0x0f, 0x6a, 0x21, 0x7a, 0x43, 0x3a, 0x26, 0x54, 0xe6,
	0x0b, 0xf8, 0x9b, 0xa7, 0x34, 0xae, 0xe3, 0xfb, 0x87, 0x7c, 0xed, 0xd9, 0xf5, 0x40, 0x2f, 0xa1,
	0xdd, 0x8f, 0x7c, 0x3e, 0x6c, 0xec, 0xb8, 0x9f, 0x95, 0xe7, 0x75, 0xfc, 0x8d, 0xac, 0xbc, 0x07,
	0x8e, 0x2c, 0xdf, 0xe2, 0x6f, 0xd4, 0xb1, 0x43, 0xbd, 0xe4, 0xc2, 0x96, 0xff, 0xe6, 0x34, 0x87,
	0xd3, 0x44, 0xf1, 0x1f, 0x7f, 0xa3, 0x40, 0x02, 0x2f, 0x0a, 0xa9, 0xc7, 0x97, 0xdd, 0x97, 0x02,
	0x11, 0x14, 0xe1, 0x0c, 0x43, 0x3e, 0x44, 0xd4, 0x5b, 0xf1, 0xb7, 0xf4, 0x31, 0x4b, 0x89, 0x8f,
	0xe1, 0xdf, 0x53, 0x07, 0xab, 0xad, 0xdd, 0x41, 0x6d, 0xea, 0x70, 0x1b, 0x18, 0x87, 0x1e, 0xf1,
	0xb1, 0xbe, 0xda, 0x1d, 0x88, 0x0f, 0xde, 0x8b, 0x89, 0xab, 0xfd, 0xfa, 0xa0, 0xc6, 0x62, 0x2e,
	0xc9, 0xf8, 0x28, 0x9c, 0xf0, 0x59, 0xcf, 0x21, 0xb1, 0xc5, 0x3f, 0xf7, 0x3c, 0x6e, 0x0c, 0xd3,
	0xc9, 0x70, 0x4c, 0x3d, 0x2c, 0xb0, 0xd6, 0x07, 0xcd, 0xe9, 0xe4, 0x0e, 0xf5, 0xec, 0x1f, 0x1b,
	0xb0, 0x7e, 0x8b, 0x06, 0xaa, 0xbe, 0x63, 0xc5, 0x9f, 0xa1, 0x2c, 0x0d, 0x45, 0x96, 0x42, 0xde,
	0x35, 0x4d, 0xde, 0x5c, 0x48, 0x75, 0x5d, 0x48, 0xb8, 0xbb, 0x46, 0x61, 0x77, 0xcd, 0xdc, 0xee,
	0x5a, 0xe9, 0xee, 0x4e, 0xa9, 0x91, 0x7d, 0x50, 0x87, 0x95, 0x1b, 0xd4, 0xa7, 0x3b, 0xea, 0x42,
	0x15, 0xe7, 0x55, 0x47, 0xe7, 0xa5, 0x22, 0xb4, 0x36, 0x0b, 0xa1, 0xf5, 0x53, 0x10, 0xda, 0x98,
	0x85, 0xd0, 0xe6, 0x2c, 0x84, 0xb6, 0x2a, 0x10, 0xda, 0x2e, 0x20, 0xb4, 0xa3, 0x48, 0x75, 0x16,
	0xfc, 0x7a, 0x25, 0xf0, 0x5b, 0x28, 0x81, 0x5f, 0xbf, 0x12, 0x7e, 0x8b, 0x55, 0xf0, 0x5b, 0x2a,
	0x28, 0x68, 0x39, 0xa7, 0xa0, 0x95, 0x54, 0x41, 0x8f, 0x0b, 0x34, 0x06, 0x56, 0x11, 0x67, 0xf3,
	0xa6, 0x4c, 0x3e, 0x8d, 0x99, 0x9e, 0x32, 0xe5, 0xb1, 0x31, 0xc0, 0x3e, 0xf6, 0x4f, 0xeb, 0xb0,
	0xb6, 0xef, 0x1c, 0x93, 0xdd, 0x30, 0x38, 0x26, 0x51, 0xac, 0x5c, 0xa8, 0x3f, 0x0b, 0xcb, 0x87,
	0xd4, 0xa7, 0x43, 0xc7, 0x1b, 0xba, 0x7c, 0x58, 0x96, 0xc6, 0xf6, 0x0f, 0x33, 0x66, 0x7b, 0x22,
	0x72, 0x85, 0xc1, 0xf1, 0x30, 0xcd, 0x5e, 0xf0, 0xf6, 0x28, 0x38, 0x3e, 0x48, 0xea, 0xf1, 0xd8,
	0xc8, 0xb3, 0xc4, 0x3a, 0x0e, 0x17, 0x8d, 0xf2, 0x6e, 0x10, 0x1b, 0x8f, 0x1d, 0x7f, 0x4a, 0xe4,
	0xc5, 0x2d, 0x76, 0x7f, 0x93, 0x13, 0xd2, 0xe6, 0x2c, 0xd5, 0x92, 0xcd, 0xc2, 0x5d, 0x27, 0x38,
	0x69, 0xe9, 0x38, 0x41, 0x4c, 0xb4, 0x4b, 0xac, 0xad, 0xa3, 0x28, 0x73, 0x19, 0xea, 0x63, 0xc7,
	0x95, 0x70, 0xe2, 0x3f, 0x71, 0xa1, 0x3e, 0xc5, 0x90, 0x3d, 0x91, 0x1e, 0xad, 0x23, 0x08, 0x7b,
	0x93, 0xcc, 0xb5, 0xf4, 0x54, 0xd7, 0xa2, 0x5a, 0xd3, 0x82, 0x6e, 0x4d, 0x02, 0x1c, 0xfd, 0x14,
	0x1c, 0x17, 0xa1, 0x17, 0x91, 0x78, 0xea, 0x33, 0x71, 0xa2, 0x5b, 0x14, 0x77, 0xd4, 0x82, 0x84,
	0xe7, 0xb9, 0x2b, 0xb0, 0x28, 0x3b, 0x24, 0x4a, 0x15, 0xd8, 0xeb, 0x0b, 0xea, 0x1d, 0x59, 0x02,
	0x7b, 0x0b, 0x16, 0x5f, 0x8b, 0xc3, 0x60, 0x9f, 0xa5, 0x39, 0xd5, 0x05, 0xe8, 0xbe, 0x1d, 0x87,
	0xc1, 0xf0, 0xf0, 0x84, 0x09, 0x7c, 0x2c, 0x0c, 0x3a, 0x9c, 0x70, 0xe3, 0x84, 0x91, 0xd2, 0x9c,
	0xb2, 0x22, 0xb9, 0xba, 0x0d, 0xcb, 0x78, 0x50, 0xfb, 0x9a, 0x9e, 0xb1, 0x62, 0xba, 0xe7, 0x8c,
	0x88, 0x0c, 0x99, 0xdd, 0x41, 0x97, 0x53, 0x76, 0x38, 0x41, 0xe1, 0xa4, 0xbd, 0xaf, 0xf8, 0x8d,
	0x01, 0xab, 0xc8, 0xea, 0x76, 0xc8, 0x04, 0xb7, 0xb9, 0x50, 0xfc, 0x92, 0x96, 0xa8, 0x3d, 0x87,
	0x28, 0x2e, 0x63, 0xab, 0x11, 0xb3, 0x67, 0x01, 0x9b, 0x3b, 0x72, 0x43, 0x4a, 0x4b, 0x69, 0x1a,
	0xb3, 0x01, 0x1d, 0x1a, 0x0f, 0x23, 0xe2, 0xca, 0xeb, 0xe4, 0xe6, 0xa0, 0x4d, 0x63, 0x2c, 0xcb,
	0xdb, 0xff, 0x30, 0xc0, 0x42, 0x1e, 0x37, 0xc9, 0x03, 0x67, 0xea, 0x3f, 0xce, 0x6e, 0x6e, 0x69,
	0xbb, 0xd9, 0xce, 0x76, 0x53, 0xc2, 0xba, 0xd0, 0xa0, 0xec, 0xea, 0x50, 0xca, 0x36, 0xd7, 0x5a,
	0xba, 0xb3, 0x0b, 0xd0, 0x8d, 0x48, 0x52, 0x1f, 0x90, 0x86, 0xc9, 0x09, 0x77, 0xe5, 0xb6, 0x27,
	0x0e, 0x3b, 0xc2, 0x78, 0x2f, 0xbc, 0x7d, 0x9b, 0x7f, 0xdf, 0x8f, 0x7c, 0xfb, 0x2f, 0x06, 0x74,
	0x52, 0xc6, 0xf9, 0x10, 0x52, 0xf2, 0xf2, 0x43, 0x9f, 0xa8, 0x91, 0x9b, 0x48, 0x39, 0xc3, 0x34,
	0xf5, 0x33, 0x8c, 0x2a, 0xf9, 0xba, 0x26, 0x79, 0xee, 0x3c, 0xe5, 0xcd, 0xac, 0x7c, 0x8c, 0xd6,
	0x12, 0xb7, 0xb2, 0xda, 0xb2, 0xdb, 0xda, 0xb2, 0x73, 0xb7, 0xa8, 0x9d, 0xfc, 0x2d, 0x6a, 0x86,
	0xd6, 0xae, 0x8a, 0xd6, 0x6f, 0x1a, 0xb0, 0xf0, 0x18, 0x7a, 0x2d, 0x3b, 0x4e, 0xe8, 0x2a, 0x3b,
	0xfd, 0x7d, 0xca, 0x07, 0x06, 0xf4, 0x54, 0xb3, 0xfb, 0xd0, 0xba, 0xac, 0x3e, 0x26, 0x26, 0x0e,
	0xa0, 0xa1, 0x9f, 0xf1, 0x94, 0x42, 0x4d, 0x73, 0x66, 0xa1, 0xa6, 0x95, 0x2b, 0xd4, 0x64, 0x42,
	0xd4, 0xea, 0x31, 0x8f, 0x60, 0x31, 0xb5, 0xca, 0xea, 0x3d, 0x7c, 0x0c, 0x13, 0xdf, 0x85, 0x15,
	0x94, 0xb6, 0x76, 0x5b, 0x51, 0x02, 0xd9, 0x82, 0x10, 0x32, 0x7e, 0x4d, 0x95, 0xdf, 0x18, 0xd6,
	0xe4, 0x46, 0xc4, 0x8b, 0xb4, 0x4a, 0x9e, 0x33, 0xb0, 0x9b, 0x6c, 0xbd, 0xa1, 0x6c, 0xbd, 0x62,
	0xba, 0x3f, 0x1b, 0x60, 0x9e, 0x61, 0xb2, 0x53, 0x6d, 0xae, 0x5e, 0x0d, 0x88, 0x46, 0xc1, 0xe6,
	0x52, 0xfb, 0x69, 0xea, 0xf6, 0x33, 0xcb, 0xe6, 0xd2, 0xbd, 0xb6, 0x0b, 0x7b, 0x45, 0xd1, 0x76,
	0x4a, 0x45, 0xab, 0x19, 0xda, 0xcf, 0x0d, 0x30, 0x35, 0x67, 0xf7, 0x91, 0x83, 0xfd, 0x63, 0xc0,
	0xd7, 0xaf, 0x0c, 0xd8, 0x50, 0x16, 0xfd, 0xc9, 0xd6, 0x53, 0xc5, 0x1e, 0xde, 0x85, 0xa5, 0xaf,
	0x88, 0x6a, 0x6a, 0x7c, 0x76, 0xeb, 0xac, 0xcf, 0x94, 0x5e, 0xa3, 0x52, 0x7a, 0x1a, 0xbc, 0x7f,
	0x6b, 0xc0, 0x72, 0x36, 0xf5, 0x5c, 0xfe, 0xf5, 0x9a, 0xe6, 0x5f, 0xc5, 0x6d, 0x53, 0x9e, 0xe5,
	0x35, 0xbc, 0x43, 0x10, 0xce, 0x76, 0x15, 0x9a, 0xe8, 0x59, 0x93, 0xe2, 0x0a, 0x7e, 0x6c, 0xde,
	0x80, 0x06, 0xef, 0x73, 0x96, 0x67, 0x8c, 0xca, 0x73, 0x95, 0x86, 0xf6, 0x5c, 0xe5, 0x65, 0x38,
	0x27, 0x27, 0xde, 0x67, 0x61, 0x44, 0x66, 0x89, 0xb2, 0x22, 0x31, 0xfa, 0x12, 0xac, 0x4a, 0x0e,
	0x55, 0xfe, 0x4a, 0xac, 0xaa, 0x7c, 0xf8, 0xf6, 0xfb, 0x7d, 0x58, 0xdd, 0x55, 0xff, 0x55, 0x62,
	0x9f, 0x44, 0xc7, 0xd4, 0x25, 0xe6, 0x17, 0xa1, 0xa7, 0xfc, 0x93, 0x83, 0xb9, 0xae, 0x16, 0x49,
	0x94, 0x8b, 0xf1, 0x4d, 0x51, 0x3d, 0xc9, 0xff, 0x47, 0xc5, 0xcb, 0xe9, 0x68, 0x14, 0xd1, 0x7a,
	0xfe, 0xb5, 0x65, 0x32, 0xda, 0x2a, 0x36, 0x48, 0x0e, 0xb7, 0xd2, 0xb7, 0x27, 0x59, 0xd1, 0xc7,
	0x5c, 0xc1, 0xee, 0x6a, 0xf9, 0x69, 0xf3, 0x49, 0x95, 0x43, 0x49, 0x7d, 0xe8, 0xb6, 0xf6, 0x2c,
	0x9a, 0x77, 0xa8, 0x5e, 0xcd, 0x13, 0x65, 0x8f, 0x42, 0x95, 0x15, 0xe5, 0x9e, 0xe5, 0x6d, 0x94,
	0x3c, 0xe2, 0x93, 0x9c, 0x36, 0xcb, 0x9a, 0x24, 0x1f, 0x07, 0xce, 0x97, 0xbf, 0x14, 0x36, 0x6d,
	0x4d, 0xc8, 0xa5, 0x4f, 0x92, 0x37, 0x2f, 0xcf, 0xec, 0x93, 0x8a, 0xbf, 0xaf, 0x3d, 0xaa, 0xd2,
	0x97, 0xaa, 0x3d, 0xb4, 0xaa, 0x50, 0x60, 0xb6, 0xc8, 0xdc, 0xc3, 0x24, 0x7d, 0x91, 0xe5, 0x6f,
	0xac, 0xf4, 0x45, 0x56, 0xbd, 0x6c, 0xba, 0x57, 0xf2, 0x5e, 0xe5, 0x89, 0xf2, 0x17, 0x2e, 0x92,
	0xed, 0x56, 0x45, 0xab, 0x64, 0xf8, 0x56, 0xfa, 0x22, 0x57, 0x7d, 0xd1, 0x60, 0x5e, 0xd4, 0x2e,
	0x58, 0x8a, 0x6f, 0x3b, 0x36, 0x2f, 0x55, 0x77, 0x90, 0x9c, 0xbf, 0x9e, 0xdd, 0x0c, 0x69, 0x6f,
	0x0e, 0xcc, 0xa7, 0xca, 0x86, 0x6a, 0x86, 0xb8, 0x69, 0xcf, 0xea, 0x52, 0xb9, 0x72, 0x14, 0x75,
	0xe9, 0xca, 0x55, 0x39, 0x5f, 0xaa, 0xee, 0x20, 0x39, 0xbf, 0x0e, 0x4b, 0xb9, 0x7b, 0x51, 0xf3,
	0x02, 0x0e, 0x2a, 0xbf, 0x61, 0x95, 0x26, 0x50, 0x75, 0x95, 0xfa, 0x32, 0xf4, 0x94, 0x62, 0xb9,
	0x34, 0xa4, 0x62, 0x4d, 0x5e, 0x9a, 0x75, 0x59, 0x5d, 0xfd, 0x25, 0xe8, 0x6b, 0xb5, 0x54, 0x69,
	0xd2, 0x6a, 0x05, 0x59, 0x1a, 0x4f, 0x79, 0xc9, 0xf5, 0x79, 0x71, 0x8a, 0xc0, 0x8d, 0x2c, 0xa7,
	0x89, 0x71, 0x32, 0x72, 0x45, 0xa1, 0xc8, 0x01, 0x2f, 0x8a, 0x34, 0x58, 0x06, 0x59, 0xf3, 0x1c,
	0xf6, 0xd0, 0x8f, 0xbb, 0x15, 0x26, 0xf0, 0x05, 0x80, 0x2c, 0x0d, 0x34, 0xcf, 0x67, 0x59, 0xb8,
	0xa6, 0xde, 0x2a, 0xff, 0xb7, 0xa0, 0x9e, 0x13, 0xcd, 0xb5, 0xec, 0x6c, 0xa6, 0xae, 0x77, 0xa3,
	0xf2, 0x00, 0x6a, 0x26, 0x47, 0x67, 0x25, 0x51, 0xa8, 0xe2, 0xb2, 0x35, 0xf3, 0xe0, 0x67, 0x7e,
	0x0e, 0x3a, 0x49, 0x70, 0x33, 0x57, 0x73, 0xb1, 0x4e, 0x30, 0x58, 0x2b, 0x8d, 0x80, 0xe6, 0x4b,
	0xb0, 0xa0, 0x06, 0x27, 0xd3, 0x52, 0xbb, 0xa9, 0xf1, 0xaa, 0x52, 0x08, 0x7d, 0x2d, 0x34, 0x49,
	0x2f, 0x54, 0x16, 0xae, 0xca, 0x39, 0x6c, 0x7f, 0x50, 0x83, 0xbe, 0xb8, 0x70, 0x4b, 0xc2, 0xd2,
	0x0d, 0x58, 0xd4, 0x2f, 0xc2, 0x4c, 0xe9, 0x6a, 0xcb, 0x6e, 0xc7, 0x2a, 0xd6, 0x75, 0x17, 0x36,
	0x76, 0x3c, 0xaf, 0xe2, 0x02, 0xeb, 0x42, 0xe1, 0x32, 0x30, 0x6b, 0xac, 0xe0, 0x77, 0x2b, 0x7f,
	0x4d, 0xbb, 0x51, 0x72, 0xa5, 0x5b, 0x16, 0x18, 0x72, 0x77, 0x98, 0x83, 0xb2, 0x8b, 0xc6, 0xad,
	0x8a, 0xcb, 0x49, 0x2d, 0xfc, 0x55, 0xde, 0x8b, 0x6e, 0xff, 0xc9, 0x80, 0xbe, 0x28, 0xd3, 0xe9,
	0x12, 0x54, 0x0b, 0xba, 0xa9, 0x04, 0x8b, 0xd7, 0x0f, 0x15, 0x3b, 0xbe, 0x07, 0xcb, 0xf9, 0xb2,
	0xa2, 0x74, 0xdd, 0x15, 0x55, 0x6d, 0x89, 0xd1, 0xca, 0x5a, 0xe4, 0x0d, 0x58, 0xd4, 0x0b, 0x86,
	0x72, 0x51, 0xa5, 0x55, 0xc4, 0xf2, 0x45, 0x1d, 0xb6, 0xf0, 0x9f, 0x3c, 0x5f, 0xf8, 0x57, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x35, 0x1b, 0x1a, 0x65, 0xff, 0x39, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ContentCenterServiceClient is the client API for ContentCenterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ContentCenterServiceClient interface {
	// 文章新增或修改
	ArticleSave(ctx context.Context, in *ArticleSaveRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 文章列表
	ArticleList(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListResponse, error)
	// 文章小程序过滤数据
	ArticleMiniFilter(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ArticleMiniFilterResponse, error)
	// 文章列表-小程序
	ArticleListMini(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListMiniResponse, error)
	// 文章详情
	ArticleDetail(ctx context.Context, in *ArticleDetailRequest, opts ...grpc.CallOption) (*ArticleDetailResponse, error)
	// 文章搜索条件
	ArticleSearchCondition(ctx context.Context, in *ArticleSearchConditionRequest, opts ...grpc.CallOption) (*ArticleSearchConditionResponse, error)
	// 文章发布、下架
	ArticleStatus(ctx context.Context, in *ArticleStatusRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	//查询文章操作记录
	ArticleEditHistoryList(ctx context.Context, in *ArticleEditHistoryListRequest, opts ...grpc.CallOption) (*ArticleEditHistoryListResponse, error)
	//推送文章概要
	ArticleRecommend(ctx context.Context, in *ArticleRecommendRequest, opts ...grpc.CallOption) (*ArticleRecommendResponse, error)
	// 类目新增或修改
	ArticleCategorySave(ctx context.Context, in *ArticleCategorySaveRequest, opts ...grpc.CallOption) (*ArticleCategorySaveResponse, error)
	// 类目删除
	ArticleCategoryDelete(ctx context.Context, in *ArticleCategoryDeleteRequest, opts ...grpc.CallOption) (*ArticleCategoryDeleteResponse, error)
	// 类目查询
	ArticleCategoryList(ctx context.Context, in *ArticleCategoryListRequest, opts ...grpc.CallOption) (*ArticleCategoryListResponse, error)
	// 医生列表查询
	QueryDoctorList(ctx context.Context, in *QueryDoctorListRequest, opts ...grpc.CallOption) (*QueryDoctorListResponse, error)
	// 获取类目
	CategoryBar(ctx context.Context, in *CategoryBarRequest, opts ...grpc.CallOption) (*CategoryBarResponse, error)
	// 获取二级类目以及文章统计数据
	GetStatistics(ctx context.Context, in *OrgIdRequest, opts ...grpc.CallOption) (*GetStatisticsResponse, error)
	// 搜索词列表
	WordList(ctx context.Context, in *WordRequest, opts ...grpc.CallOption) (*WordResponse, error)
	// 搜索词新增或修改
	WordOperate(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 搜索词隐藏
	WordDelete(ctx context.Context, in *WordDeleteRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 首页热门搜索词
	IndexHotWord(ctx context.Context, in *IndexWordRequest, opts ...grpc.CallOption) (*IndexHotWordResponse, error)
	// 首页默认搜索词
	IndexDefaultWord(ctx context.Context, in *IndexWordRequest, opts ...grpc.CallOption) (*IndexDefaultWordResponse, error)
	//搜索词库列表
	Keywords(ctx context.Context, in *KeywordsRequest, opts ...grpc.CallOption) (*KeywordsResponse, error)
	//热搜词添加
	KeywordStore(ctx context.Context, in *KeywordStoreRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	//单个热搜词删除
	KeywordDelete(ctx context.Context, in *KeywordDeleteRequest, opts ...grpc.CallOption) (*ContentResponse, error)
}

type contentCenterServiceClient struct {
	cc *grpc.ClientConn
}

func NewContentCenterServiceClient(cc *grpc.ClientConn) ContentCenterServiceClient {
	return &contentCenterServiceClient{cc}
}

func (c *contentCenterServiceClient) ArticleSave(ctx context.Context, in *ArticleSaveRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleList(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListResponse, error) {
	out := new(ArticleListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleMiniFilter(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ArticleMiniFilterResponse, error) {
	out := new(ArticleMiniFilterResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleMiniFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleListMini(ctx context.Context, in *ArticleListRequest, opts ...grpc.CallOption) (*ArticleListMiniResponse, error) {
	out := new(ArticleListMiniResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleListMini", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleDetail(ctx context.Context, in *ArticleDetailRequest, opts ...grpc.CallOption) (*ArticleDetailResponse, error) {
	out := new(ArticleDetailResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleSearchCondition(ctx context.Context, in *ArticleSearchConditionRequest, opts ...grpc.CallOption) (*ArticleSearchConditionResponse, error) {
	out := new(ArticleSearchConditionResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleSearchCondition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleStatus(ctx context.Context, in *ArticleStatusRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleEditHistoryList(ctx context.Context, in *ArticleEditHistoryListRequest, opts ...grpc.CallOption) (*ArticleEditHistoryListResponse, error) {
	out := new(ArticleEditHistoryListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleEditHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleRecommend(ctx context.Context, in *ArticleRecommendRequest, opts ...grpc.CallOption) (*ArticleRecommendResponse, error) {
	out := new(ArticleRecommendResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategorySave(ctx context.Context, in *ArticleCategorySaveRequest, opts ...grpc.CallOption) (*ArticleCategorySaveResponse, error) {
	out := new(ArticleCategorySaveResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategorySave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategoryDelete(ctx context.Context, in *ArticleCategoryDeleteRequest, opts ...grpc.CallOption) (*ArticleCategoryDeleteResponse, error) {
	out := new(ArticleCategoryDeleteResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategoryDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) ArticleCategoryList(ctx context.Context, in *ArticleCategoryListRequest, opts ...grpc.CallOption) (*ArticleCategoryListResponse, error) {
	out := new(ArticleCategoryListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/ArticleCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) QueryDoctorList(ctx context.Context, in *QueryDoctorListRequest, opts ...grpc.CallOption) (*QueryDoctorListResponse, error) {
	out := new(QueryDoctorListResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/QueryDoctorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) CategoryBar(ctx context.Context, in *CategoryBarRequest, opts ...grpc.CallOption) (*CategoryBarResponse, error) {
	out := new(CategoryBarResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/CategoryBar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) GetStatistics(ctx context.Context, in *OrgIdRequest, opts ...grpc.CallOption) (*GetStatisticsResponse, error) {
	out := new(GetStatisticsResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/GetStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) WordList(ctx context.Context, in *WordRequest, opts ...grpc.CallOption) (*WordResponse, error) {
	out := new(WordResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/WordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) WordOperate(ctx context.Context, in *JsonStrRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/WordOperate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) WordDelete(ctx context.Context, in *WordDeleteRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/WordDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) IndexHotWord(ctx context.Context, in *IndexWordRequest, opts ...grpc.CallOption) (*IndexHotWordResponse, error) {
	out := new(IndexHotWordResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/IndexHotWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) IndexDefaultWord(ctx context.Context, in *IndexWordRequest, opts ...grpc.CallOption) (*IndexDefaultWordResponse, error) {
	out := new(IndexDefaultWordResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/IndexDefaultWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) Keywords(ctx context.Context, in *KeywordsRequest, opts ...grpc.CallOption) (*KeywordsResponse, error) {
	out := new(KeywordsResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/Keywords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) KeywordStore(ctx context.Context, in *KeywordStoreRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/KeywordStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentCenterServiceClient) KeywordDelete(ctx context.Context, in *KeywordDeleteRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.ContentCenterService/KeywordDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContentCenterServiceServer is the server API for ContentCenterService service.
type ContentCenterServiceServer interface {
	// 文章新增或修改
	ArticleSave(context.Context, *ArticleSaveRequest) (*ContentResponse, error)
	// 文章列表
	ArticleList(context.Context, *ArticleListRequest) (*ArticleListResponse, error)
	// 文章小程序过滤数据
	ArticleMiniFilter(context.Context, *EmptyRequest) (*ArticleMiniFilterResponse, error)
	// 文章列表-小程序
	ArticleListMini(context.Context, *ArticleListRequest) (*ArticleListMiniResponse, error)
	// 文章详情
	ArticleDetail(context.Context, *ArticleDetailRequest) (*ArticleDetailResponse, error)
	// 文章搜索条件
	ArticleSearchCondition(context.Context, *ArticleSearchConditionRequest) (*ArticleSearchConditionResponse, error)
	// 文章发布、下架
	ArticleStatus(context.Context, *ArticleStatusRequest) (*ContentResponse, error)
	//查询文章操作记录
	ArticleEditHistoryList(context.Context, *ArticleEditHistoryListRequest) (*ArticleEditHistoryListResponse, error)
	//推送文章概要
	ArticleRecommend(context.Context, *ArticleRecommendRequest) (*ArticleRecommendResponse, error)
	// 类目新增或修改
	ArticleCategorySave(context.Context, *ArticleCategorySaveRequest) (*ArticleCategorySaveResponse, error)
	// 类目删除
	ArticleCategoryDelete(context.Context, *ArticleCategoryDeleteRequest) (*ArticleCategoryDeleteResponse, error)
	// 类目查询
	ArticleCategoryList(context.Context, *ArticleCategoryListRequest) (*ArticleCategoryListResponse, error)
	// 医生列表查询
	QueryDoctorList(context.Context, *QueryDoctorListRequest) (*QueryDoctorListResponse, error)
	// 获取类目
	CategoryBar(context.Context, *CategoryBarRequest) (*CategoryBarResponse, error)
	// 获取二级类目以及文章统计数据
	GetStatistics(context.Context, *OrgIdRequest) (*GetStatisticsResponse, error)
	// 搜索词列表
	WordList(context.Context, *WordRequest) (*WordResponse, error)
	// 搜索词新增或修改
	WordOperate(context.Context, *JsonStrRequest) (*ContentResponse, error)
	// 搜索词隐藏
	WordDelete(context.Context, *WordDeleteRequest) (*ContentResponse, error)
	// 首页热门搜索词
	IndexHotWord(context.Context, *IndexWordRequest) (*IndexHotWordResponse, error)
	// 首页默认搜索词
	IndexDefaultWord(context.Context, *IndexWordRequest) (*IndexDefaultWordResponse, error)
	//搜索词库列表
	Keywords(context.Context, *KeywordsRequest) (*KeywordsResponse, error)
	//热搜词添加
	KeywordStore(context.Context, *KeywordStoreRequest) (*ContentResponse, error)
	//单个热搜词删除
	KeywordDelete(context.Context, *KeywordDeleteRequest) (*ContentResponse, error)
}

// UnimplementedContentCenterServiceServer can be embedded to have forward compatible implementations.
type UnimplementedContentCenterServiceServer struct {
}

func (*UnimplementedContentCenterServiceServer) ArticleSave(ctx context.Context, req *ArticleSaveRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleSave not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleList(ctx context.Context, req *ArticleListRequest) (*ArticleListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleList not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleMiniFilter(ctx context.Context, req *EmptyRequest) (*ArticleMiniFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleMiniFilter not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleListMini(ctx context.Context, req *ArticleListRequest) (*ArticleListMiniResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleListMini not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleDetail(ctx context.Context, req *ArticleDetailRequest) (*ArticleDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleDetail not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleSearchCondition(ctx context.Context, req *ArticleSearchConditionRequest) (*ArticleSearchConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleSearchCondition not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleStatus(ctx context.Context, req *ArticleStatusRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleStatus not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleEditHistoryList(ctx context.Context, req *ArticleEditHistoryListRequest) (*ArticleEditHistoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleEditHistoryList not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleRecommend(ctx context.Context, req *ArticleRecommendRequest) (*ArticleRecommendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleRecommend not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategorySave(ctx context.Context, req *ArticleCategorySaveRequest) (*ArticleCategorySaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategorySave not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategoryDelete(ctx context.Context, req *ArticleCategoryDeleteRequest) (*ArticleCategoryDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategoryDelete not implemented")
}
func (*UnimplementedContentCenterServiceServer) ArticleCategoryList(ctx context.Context, req *ArticleCategoryListRequest) (*ArticleCategoryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCategoryList not implemented")
}
func (*UnimplementedContentCenterServiceServer) QueryDoctorList(ctx context.Context, req *QueryDoctorListRequest) (*QueryDoctorListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryDoctorList not implemented")
}
func (*UnimplementedContentCenterServiceServer) CategoryBar(ctx context.Context, req *CategoryBarRequest) (*CategoryBarResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategoryBar not implemented")
}
func (*UnimplementedContentCenterServiceServer) GetStatistics(ctx context.Context, req *OrgIdRequest) (*GetStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatistics not implemented")
}
func (*UnimplementedContentCenterServiceServer) WordList(ctx context.Context, req *WordRequest) (*WordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WordList not implemented")
}
func (*UnimplementedContentCenterServiceServer) WordOperate(ctx context.Context, req *JsonStrRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WordOperate not implemented")
}
func (*UnimplementedContentCenterServiceServer) WordDelete(ctx context.Context, req *WordDeleteRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WordDelete not implemented")
}
func (*UnimplementedContentCenterServiceServer) IndexHotWord(ctx context.Context, req *IndexWordRequest) (*IndexHotWordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IndexHotWord not implemented")
}
func (*UnimplementedContentCenterServiceServer) IndexDefaultWord(ctx context.Context, req *IndexWordRequest) (*IndexDefaultWordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IndexDefaultWord not implemented")
}
func (*UnimplementedContentCenterServiceServer) Keywords(ctx context.Context, req *KeywordsRequest) (*KeywordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Keywords not implemented")
}
func (*UnimplementedContentCenterServiceServer) KeywordStore(ctx context.Context, req *KeywordStoreRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KeywordStore not implemented")
}
func (*UnimplementedContentCenterServiceServer) KeywordDelete(ctx context.Context, req *KeywordDeleteRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KeywordDelete not implemented")
}

func RegisterContentCenterServiceServer(s *grpc.Server, srv ContentCenterServiceServer) {
	s.RegisterService(&_ContentCenterService_serviceDesc, srv)
}

func _ContentCenterService_ArticleSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleSaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleSave(ctx, req.(*ArticleSaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleList(ctx, req.(*ArticleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleMiniFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleMiniFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleMiniFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleMiniFilter(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleListMini_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleListMini(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleListMini",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleListMini(ctx, req.(*ArticleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleDetail(ctx, req.(*ArticleDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleSearchCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleSearchConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleSearchCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleSearchCondition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleSearchCondition(ctx, req.(*ArticleSearchConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleStatus(ctx, req.(*ArticleStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleEditHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleEditHistoryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleEditHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleEditHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleEditHistoryList(ctx, req.(*ArticleEditHistoryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleRecommend(ctx, req.(*ArticleRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategorySave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategorySaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategorySave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategorySave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategorySave(ctx, req.(*ArticleCategorySaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategoryDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategoryDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategoryDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategoryDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategoryDelete(ctx, req.(*ArticleCategoryDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_ArticleCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCategoryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).ArticleCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/ArticleCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).ArticleCategoryList(ctx, req.(*ArticleCategoryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_QueryDoctorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDoctorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).QueryDoctorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/QueryDoctorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).QueryDoctorList(ctx, req.(*QueryDoctorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_CategoryBar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CategoryBarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).CategoryBar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/CategoryBar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).CategoryBar(ctx, req.(*CategoryBarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_GetStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrgIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).GetStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/GetStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).GetStatistics(ctx, req.(*OrgIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_WordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).WordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/WordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).WordList(ctx, req.(*WordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_WordOperate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonStrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).WordOperate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/WordOperate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).WordOperate(ctx, req.(*JsonStrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_WordDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WordDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).WordDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/WordDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).WordDelete(ctx, req.(*WordDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_IndexHotWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexWordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).IndexHotWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/IndexHotWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).IndexHotWord(ctx, req.(*IndexWordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_IndexDefaultWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexWordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).IndexDefaultWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/IndexDefaultWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).IndexDefaultWord(ctx, req.(*IndexWordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_Keywords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeywordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).Keywords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/Keywords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).Keywords(ctx, req.(*KeywordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_KeywordStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeywordStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).KeywordStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/KeywordStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).KeywordStore(ctx, req.(*KeywordStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentCenterService_KeywordDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeywordDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentCenterServiceServer).KeywordDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.ContentCenterService/KeywordDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentCenterServiceServer).KeywordDelete(ctx, req.(*KeywordDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ContentCenterService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.ContentCenterService",
	HandlerType: (*ContentCenterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ArticleSave",
			Handler:    _ContentCenterService_ArticleSave_Handler,
		},
		{
			MethodName: "ArticleList",
			Handler:    _ContentCenterService_ArticleList_Handler,
		},
		{
			MethodName: "ArticleMiniFilter",
			Handler:    _ContentCenterService_ArticleMiniFilter_Handler,
		},
		{
			MethodName: "ArticleListMini",
			Handler:    _ContentCenterService_ArticleListMini_Handler,
		},
		{
			MethodName: "ArticleDetail",
			Handler:    _ContentCenterService_ArticleDetail_Handler,
		},
		{
			MethodName: "ArticleSearchCondition",
			Handler:    _ContentCenterService_ArticleSearchCondition_Handler,
		},
		{
			MethodName: "ArticleStatus",
			Handler:    _ContentCenterService_ArticleStatus_Handler,
		},
		{
			MethodName: "ArticleEditHistoryList",
			Handler:    _ContentCenterService_ArticleEditHistoryList_Handler,
		},
		{
			MethodName: "ArticleRecommend",
			Handler:    _ContentCenterService_ArticleRecommend_Handler,
		},
		{
			MethodName: "ArticleCategorySave",
			Handler:    _ContentCenterService_ArticleCategorySave_Handler,
		},
		{
			MethodName: "ArticleCategoryDelete",
			Handler:    _ContentCenterService_ArticleCategoryDelete_Handler,
		},
		{
			MethodName: "ArticleCategoryList",
			Handler:    _ContentCenterService_ArticleCategoryList_Handler,
		},
		{
			MethodName: "QueryDoctorList",
			Handler:    _ContentCenterService_QueryDoctorList_Handler,
		},
		{
			MethodName: "CategoryBar",
			Handler:    _ContentCenterService_CategoryBar_Handler,
		},
		{
			MethodName: "GetStatistics",
			Handler:    _ContentCenterService_GetStatistics_Handler,
		},
		{
			MethodName: "WordList",
			Handler:    _ContentCenterService_WordList_Handler,
		},
		{
			MethodName: "WordOperate",
			Handler:    _ContentCenterService_WordOperate_Handler,
		},
		{
			MethodName: "WordDelete",
			Handler:    _ContentCenterService_WordDelete_Handler,
		},
		{
			MethodName: "IndexHotWord",
			Handler:    _ContentCenterService_IndexHotWord_Handler,
		},
		{
			MethodName: "IndexDefaultWord",
			Handler:    _ContentCenterService_IndexDefaultWord_Handler,
		},
		{
			MethodName: "Keywords",
			Handler:    _ContentCenterService_Keywords_Handler,
		},
		{
			MethodName: "KeywordStore",
			Handler:    _ContentCenterService_KeywordStore_Handler,
		},
		{
			MethodName: "KeywordDelete",
			Handler:    _ContentCenterService_KeywordDelete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}

// CensusServiceClient is the client API for CensusService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CensusServiceClient interface {
	//添加访问记录
	AddVisitRecord(ctx context.Context, in *AddVisitRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	//添加勃林格访问记录
	AddBoehringereChickRecord(ctx context.Context, in *BoehringereChickRecord, opts ...grpc.CallOption) (*ContentResponse, error)
	// 文章统计
	ArticleCensus(ctx context.Context, in *ArticleCensusRequest, opts ...grpc.CallOption) (*ArticleCensusResponse, error)
	// 勃林格统计
	BoehringereCensus(ctx context.Context, in *BoehringereCensusRequest, opts ...grpc.CallOption) (*BoehringereCensusResponse, error)
}

type censusServiceClient struct {
	cc *grpc.ClientConn
}

func NewCensusServiceClient(cc *grpc.ClientConn) CensusServiceClient {
	return &censusServiceClient{cc}
}

func (c *censusServiceClient) AddVisitRecord(ctx context.Context, in *AddVisitRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/AddVisitRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) AddBoehringereChickRecord(ctx context.Context, in *BoehringereChickRecord, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/AddBoehringereChickRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) ArticleCensus(ctx context.Context, in *ArticleCensusRequest, opts ...grpc.CallOption) (*ArticleCensusResponse, error) {
	out := new(ArticleCensusResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/ArticleCensus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *censusServiceClient) BoehringereCensus(ctx context.Context, in *BoehringereCensusRequest, opts ...grpc.CallOption) (*BoehringereCensusResponse, error) {
	out := new(BoehringereCensusResponse)
	err := c.cc.Invoke(ctx, "/ctc.CensusService/BoehringereCensus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CensusServiceServer is the server API for CensusService service.
type CensusServiceServer interface {
	//添加访问记录
	AddVisitRecord(context.Context, *AddVisitRecordRequest) (*ContentResponse, error)
	//添加勃林格访问记录
	AddBoehringereChickRecord(context.Context, *BoehringereChickRecord) (*ContentResponse, error)
	// 文章统计
	ArticleCensus(context.Context, *ArticleCensusRequest) (*ArticleCensusResponse, error)
	// 勃林格统计
	BoehringereCensus(context.Context, *BoehringereCensusRequest) (*BoehringereCensusResponse, error)
}

// UnimplementedCensusServiceServer can be embedded to have forward compatible implementations.
type UnimplementedCensusServiceServer struct {
}

func (*UnimplementedCensusServiceServer) AddVisitRecord(ctx context.Context, req *AddVisitRecordRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddVisitRecord not implemented")
}
func (*UnimplementedCensusServiceServer) AddBoehringereChickRecord(ctx context.Context, req *BoehringereChickRecord) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBoehringereChickRecord not implemented")
}
func (*UnimplementedCensusServiceServer) ArticleCensus(ctx context.Context, req *ArticleCensusRequest) (*ArticleCensusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArticleCensus not implemented")
}
func (*UnimplementedCensusServiceServer) BoehringereCensus(ctx context.Context, req *BoehringereCensusRequest) (*BoehringereCensusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BoehringereCensus not implemented")
}

func RegisterCensusServiceServer(s *grpc.Server, srv CensusServiceServer) {
	s.RegisterService(&_CensusService_serviceDesc, srv)
}

func _CensusService_AddVisitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVisitRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).AddVisitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/AddVisitRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).AddVisitRecord(ctx, req.(*AddVisitRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_AddBoehringereChickRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoehringereChickRecord)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).AddBoehringereChickRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/AddBoehringereChickRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).AddBoehringereChickRecord(ctx, req.(*BoehringereChickRecord))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_ArticleCensus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleCensusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).ArticleCensus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/ArticleCensus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).ArticleCensus(ctx, req.(*ArticleCensusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CensusService_BoehringereCensus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoehringereCensusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CensusServiceServer).BoehringereCensus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.CensusService/BoehringereCensus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CensusServiceServer).BoehringereCensus(ctx, req.(*BoehringereCensusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CensusService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.CensusService",
	HandlerType: (*CensusServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddVisitRecord",
			Handler:    _CensusService_AddVisitRecord_Handler,
		},
		{
			MethodName: "AddBoehringereChickRecord",
			Handler:    _CensusService_AddBoehringereChickRecord_Handler,
		},
		{
			MethodName: "ArticleCensus",
			Handler:    _CensusService_ArticleCensus_Handler,
		},
		{
			MethodName: "BoehringereCensus",
			Handler:    _CensusService_BoehringereCensus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}

// BiliAdServiceClient is the client API for BiliAdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BiliAdServiceClient interface {
	// 添加B站广告点击记录
	AddClickRecord(ctx context.Context, in *AddClickRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error)
	// 查找B站广告点击记录
	FindClickRecords(ctx context.Context, in *FindClickRecordsRequest, opts ...grpc.CallOption) (*FindClickRecordsResponse, error)
	// 保存推送B站广告归因记录
	SaveConversion(ctx context.Context, in *SaveConversionRequest, opts ...grpc.CallOption) (*ContentResponse, error)
}

type biliAdServiceClient struct {
	cc *grpc.ClientConn
}

func NewBiliAdServiceClient(cc *grpc.ClientConn) BiliAdServiceClient {
	return &biliAdServiceClient{cc}
}

func (c *biliAdServiceClient) AddClickRecord(ctx context.Context, in *AddClickRecordRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/AddClickRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biliAdServiceClient) FindClickRecords(ctx context.Context, in *FindClickRecordsRequest, opts ...grpc.CallOption) (*FindClickRecordsResponse, error) {
	out := new(FindClickRecordsResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/FindClickRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *biliAdServiceClient) SaveConversion(ctx context.Context, in *SaveConversionRequest, opts ...grpc.CallOption) (*ContentResponse, error) {
	out := new(ContentResponse)
	err := c.cc.Invoke(ctx, "/ctc.BiliAdService/SaveConversion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BiliAdServiceServer is the server API for BiliAdService service.
type BiliAdServiceServer interface {
	// 添加B站广告点击记录
	AddClickRecord(context.Context, *AddClickRecordRequest) (*ContentResponse, error)
	// 查找B站广告点击记录
	FindClickRecords(context.Context, *FindClickRecordsRequest) (*FindClickRecordsResponse, error)
	// 保存推送B站广告归因记录
	SaveConversion(context.Context, *SaveConversionRequest) (*ContentResponse, error)
}

// UnimplementedBiliAdServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBiliAdServiceServer struct {
}

func (*UnimplementedBiliAdServiceServer) AddClickRecord(ctx context.Context, req *AddClickRecordRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddClickRecord not implemented")
}
func (*UnimplementedBiliAdServiceServer) FindClickRecords(ctx context.Context, req *FindClickRecordsRequest) (*FindClickRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindClickRecords not implemented")
}
func (*UnimplementedBiliAdServiceServer) SaveConversion(ctx context.Context, req *SaveConversionRequest) (*ContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveConversion not implemented")
}

func RegisterBiliAdServiceServer(s *grpc.Server, srv BiliAdServiceServer) {
	s.RegisterService(&_BiliAdService_serviceDesc, srv)
}

func _BiliAdService_AddClickRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddClickRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).AddClickRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/AddClickRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).AddClickRecord(ctx, req.(*AddClickRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiliAdService_FindClickRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindClickRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).FindClickRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/FindClickRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).FindClickRecords(ctx, req.(*FindClickRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BiliAdService_SaveConversion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveConversionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BiliAdServiceServer).SaveConversion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ctc.BiliAdService/SaveConversion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BiliAdServiceServer).SaveConversion(ctx, req.(*SaveConversionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BiliAdService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ctc.BiliAdService",
	HandlerType: (*BiliAdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddClickRecord",
			Handler:    _BiliAdService_AddClickRecord_Handler,
		},
		{
			MethodName: "FindClickRecords",
			Handler:    _BiliAdService_FindClickRecords_Handler,
		},
		{
			MethodName: "SaveConversion",
			Handler:    _BiliAdService_SaveConversion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ctc/contentCenter.proto",
}
