package models

import (
	"time"
)

type Warehouse struct {
	Id         int       `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	Thirdid    string    `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易ID') VARCHAR(50)"`
	Code       string    `xorm:"not null default '''' comment('仓库编号') index index(idx_code_category) VARCHAR(32)"`
	Name       string    `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
	Comefrom   int       `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易 3-门店（美团）)') index INT(11)"`
	Level      int       `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
	Category   int       `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓，5-前置仓虚拟仓)') index index(idx_code_category) INT(11)"`
	Address    string    `xorm:"not null default '''' comment('仓库地址') VARCHAR(255)"`
	Contacts   string    `xorm:"not null default '''' comment('仓库联系人') VARCHAR(50)"`
	Tel        string    `xorm:"not null default '''' comment('仓库联系方式') VARCHAR(20)"`
	Status     int       `xorm:"not null default 0 comment('仓库状态（0-禁用，1-启用）') index INT(11)"`
	Createdate time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	Lastdate   time.Time `xorm:"not null default 'current_timestamp()' comment('最后更新时间') DATETIME"`
	Subsystem  int       `xorm:"default 0 comment('所属系统 0:默认,1:ERP,2:子龙') INT(11)"`
	Ratio      int       `xorm:"default 100 comment('仓库比例') INT(11)"`
	Lng        int       `xorm:"default 0 comment('仓库经度') INT(11)"`
	Lat        int       `xorm:"default 0 comment('仓库纬度') INT(11)"`
	Region     string    `xorm:"default '''' comment('所属区域') VARCHAR(32)"`
	City       string    `xorm:"default '''' comment('所属城市') VARCHAR(32)"`
	// SellDrugs  int       `xorm:"sell_drugs default 0 comment('是否有资质售药') TINYINT(4)"` v6.27.1 删除
}

type NewWarehouse struct {
	Warehouse `xorm:"extends"`
	Number    int
}

// 仓库和配送对应关系
type WarehouseDeliveryRelation struct {
	Id          int    `json:"id" xorm:"pk autoincr not null comment('主键') INT(11) 'id'"`
	WarehouseId int    `json:"warehouse_id" xorm:"not null comment('仓库ID') INT(11) 'warehouse_id'"`
	DeliveryId  int    `json:"delivery_id" xorm:"not null comment('配送配置ID') INT(11) 'delivery_id'"`
	ShopNo      string `json:"shop_no" xorm:"default 'null' comment('配送门店编码') VARCHAR(100) 'shop_no'"`
}

type WarehouseDeliveryConfig struct {
	Id           int    `json:"id" xorm:"pk autoincr not null comment('主键') INT(11) 'id'"`
	DeliveryName string `json:"delivery_name" xorm:"not null comment('配送类型名称') VARCHAR(100) 'delivery_name'"`
	AppKey       string `json:"app_key" xorm:"default 'null' comment('app_key') VARCHAR(100) 'app_key'"`
	AppSecret    string `json:"app_secret" xorm:"default 'null' comment('app_secret') VARCHAR(100) 'app_secret'"`
	SourceId     string `json:"source_id" xorm:"default 'null' comment('source_id') VARCHAR(100) 'source_id'"`
	DeliveryType int    `json:"delivery_type" xorm:"default 'null' comment('//配送类型默认美配 0 美配 1 闪送 2自配 3达达 4蜂鸟 5顺风') INT(11) 'delivery_type'"`
	ShopNo       string `json:"shop_no" xorm:"default 'null' comment('配送门店编码') VARCHAR(100) 'shop_no'"`
}
