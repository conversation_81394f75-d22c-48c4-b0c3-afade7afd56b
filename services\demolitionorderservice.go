package services

import (
	"context"
	"dispatch-center/dtos"
	"dispatch-center/models"
	proto "dispatch-center/proto/dc"
	"dispatch-center/proto/ic"
	"dispatch-center/utils"
	"encoding/json"
	"fmt"
	"runtime"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type DemolitionOrderService struct {
	DBService
}

func init() {
	//go subscribeSaveWarehouseOrder()
	//go subscribeSaveOrderParams()
}

//递归处理拆库存
func ConvertGoodsStock(mapWarehouse map[int]dtos.WarehouseModel, mapWarehouseGoodsStock, lastMapWarehouseGoodsStock map[int]map[string]int32,
	mapGoodsStock map[string]int32, warehouseStock, orderStock int32, mapWarehouseAreaLevel, mapWarehouseLevel map[int]int) (bool, error) {
	//初始化参数
	_mapWarehouseAreaLevel := make(map[int]int, 0)
	_mapWarehouseLevel := make(map[int]int, 0)
	_mapWarehouse := make(map[int]dtos.WarehouseModel, 0)
	for key, value := range mapWarehouseAreaLevel {
		_mapWarehouseAreaLevel[key] = value
	}
	for key, value := range mapWarehouseLevel {
		_mapWarehouseLevel[key] = value
	}
	for key, value := range mapWarehouse {
		_mapWarehouse[key] = value
	}

	mapWarehouseLastGoodsType := make(map[int]int32, 0)
	mapWarehouseLastGoodsStock := make(map[int]int32, 0)
	warehouseOrderStock := make(map[int]int32, 0)
	warehouseWarehouseStock := make(map[int]int32, 0)
	_newMapWarehouseGoodsStock := make(map[int]map[string]int32, 0)
	_lastMapWarehouseGoodsStock := make(map[int]map[string]int32, 0)
	//循环仓库
	for _, w := range _mapWarehouse {
		//判断仓库中是否该仓库
		if _, ok := mapWarehouseGoodsStock[w.Id]; ok {
			_newMapGoodsStock := make(map[string]int32, 0)
			//获取该仓库下的所有商品库存信息
			warehouseGoodsStock := mapWarehouseGoodsStock[w.Id]
			//初始化参数
			_mapGoodsStock := make(map[string]int32, 0)
			for key, value := range mapGoodsStock {
				_mapGoodsStock[key] = value
			}
			_orderStock := orderStock
			_warehouseStock := warehouseStock

			for i, v := range warehouseGoodsStock {
				goodsid := i
				nowWarehouseGoodsStock := v
				if _, ok := _mapGoodsStock[goodsid]; ok {
					//当前商品的库存
					newStock := _mapGoodsStock[goodsid]
					//比较仓库中的库存数据跟订单中的库存大小
					if newStock-nowWarehouseGoodsStock > 0 {
						mapWarehouseLastGoodsStock[w.Id] += nowWarehouseGoodsStock
						_mapGoodsStock[goodsid] = newStock - nowWarehouseGoodsStock
						_newMapGoodsStock[goodsid] = nowWarehouseGoodsStock
						_orderStock = _orderStock - nowWarehouseGoodsStock
					} else {
						mapWarehouseLastGoodsType[w.Id] += 1 //判断整单满足
						mapWarehouseLastGoodsStock[w.Id] += newStock
						_mapGoodsStock[goodsid] = 0
						_newMapGoodsStock[goodsid] = newStock
						_orderStock = _orderStock - newStock
					}
					_warehouseStock = _warehouseStock - nowWarehouseGoodsStock // 减去仓库总库存
				}
			}
			warehouseOrderStock[w.Id] = _orderStock
			warehouseWarehouseStock[w.Id] = _warehouseStock
			_newMapWarehouseGoodsStock[w.Id] = _mapGoodsStock
			_lastMapWarehouseGoodsStock[w.Id] = _newMapGoodsStock
		}
	}
	if len(mapWarehouseLastGoodsType) > 0 {
		get_Map_MaxMap_int32(mapWarehouseLastGoodsType) // 获取最大值。map[仓库id]商品类别
	}
	if len(mapWarehouseLastGoodsType) == 1 {
		for k, _ := range mapWarehouseLastGoodsType {
			lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
			delete(mapWarehouseGoodsStock, k)
			delete(_mapWarehouse, k)
			delete(mapWarehouseAreaLevel, k)
			delete(mapWarehouseLevel, k)
			mapGoodsStock = _newMapWarehouseGoodsStock[k]
			orderStock = warehouseOrderStock[k]
			warehouseStock = warehouseWarehouseStock[k]
		}
	} else {
		//根据仓库类别目录最多的
		for k, _ := range mapWarehouseLastGoodsStock {
			if len(mapWarehouseLastGoodsType) > 0 {
				if _, ok := mapWarehouseLastGoodsType[k]; !ok {
					delete(mapWarehouseLastGoodsStock, k)
				}
			}
		}
		if len(mapWarehouseLastGoodsStock) > 0 {
			get_Map_MaxMap_int32(mapWarehouseLastGoodsStock) // 获取最大值。map[仓库id]仓库库存
		}
		if len(mapWarehouseLastGoodsStock) == 1 {
			for k, _ := range mapWarehouseLastGoodsStock {
				lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
				delete(mapWarehouseGoodsStock, k)
				delete(_mapWarehouse, k)
				delete(mapWarehouseAreaLevel, k)
				delete(mapWarehouseLevel, k)
				mapGoodsStock = _newMapWarehouseGoodsStock[k]
				orderStock = warehouseOrderStock[k]
				warehouseStock = warehouseWarehouseStock[k]
			}
		} else {
			//1、判断配送区域等级
			for k, _ := range _mapWarehouseAreaLevel {
				if len(mapWarehouseLastGoodsStock) > 0 {
					if _, ok := mapWarehouseLastGoodsStock[k]; !ok {
						delete(_mapWarehouseAreaLevel, k)
					}
				}
			}
			if len(_mapWarehouseAreaLevel) > 0 {
				get_Map_MinMap_int(_mapWarehouseAreaLevel)
			}
			if len(_mapWarehouseAreaLevel) == 1 {
				for k, _ := range _mapWarehouseAreaLevel {
					lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
					delete(mapWarehouseGoodsStock, k)
					delete(_mapWarehouse, k)
					delete(mapWarehouseAreaLevel, k)
					delete(mapWarehouseLevel, k)
					mapGoodsStock = _newMapWarehouseGoodsStock[k]
					orderStock = warehouseOrderStock[k]
					warehouseStock = warehouseWarehouseStock[k]
				}
			} else {
				//2、判断仓库等级，基于上个信息
				for k, _ := range _mapWarehouseLevel {
					if len(_mapWarehouseAreaLevel) > 0 {
						if _, ok := _mapWarehouseAreaLevel[k]; !ok {
							delete(_mapWarehouseLevel, k)
						}
					}
				}
				if len(_mapWarehouseLevel) > 0 {
					get_Map_MinMap_int(_mapWarehouseLevel)
				}
				if len(_mapWarehouseLevel) == 1 {
					for k, _ := range _mapWarehouseLevel {
						lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
						delete(mapWarehouseGoodsStock, k)
						delete(_mapWarehouse, k)
						delete(mapWarehouseAreaLevel, k)
						delete(mapWarehouseLevel, k)
						mapGoodsStock = _newMapWarehouseGoodsStock[k]
						orderStock = warehouseOrderStock[k]
						warehouseStock = warehouseWarehouseStock[k]
					}
				} else {
					for k, _ := range _mapWarehouseLevel {
						lastMapWarehouseGoodsStock[k] = _lastMapWarehouseGoodsStock[k]
						delete(mapWarehouseGoodsStock, k)
						delete(_mapWarehouse, k)
						delete(mapWarehouseAreaLevel, k)
						delete(mapWarehouseLevel, k)
						mapGoodsStock = _newMapWarehouseGoodsStock[k]
						orderStock = warehouseOrderStock[k]
						warehouseStock = warehouseWarehouseStock[k]
						break
					}
				}
			}
		}
	}
	//当订单的库存扣完了，则返回结果
	if orderStock == 0 {
		return true, nil
	}
	return ConvertGoodsStock(_mapWarehouse, mapWarehouseGoodsStock, lastMapWarehouseGoodsStock, mapGoodsStock, warehouseStock, orderStock, mapWarehouseAreaLevel, mapWarehouseLevel)
}

/**
获取map中最大的map数据，根据值返回
*/
func get_Map_MaxMap_int32(mp map[int]int32) {
	var newMp = make([]int, 0)
	for _, v := range mp {
		newMp = append(newMp, int(v))
	}
	sort.Ints(newMp)
	var maxValue = newMp[len(newMp)-1]
	for k, v := range mp {
		if v != int32(maxValue) {
			delete(mp, k)
		}
	}
}

/**
获取map中最小的map数据，根据值返回
*/
func get_Map_MinMap_int(mp map[int]int) {
	var newMp = make([]int, 0)
	for _, v := range mp {
		newMp = append(newMp, int(v))
	}
	sort.Ints(newMp)
	var minValue = newMp[0]
	for k, v := range mp {
		if v != minValue {
			delete(mp, k)
		}
	}
}

//根据商品sku获取当前冻结的库存总数量
func getFreezeGoodsStock(keyStr string, warehouseId int, redisConn *redis.Client) int32 {
	redisPipeline := redisConn.Pipeline()
	defer func() {
		if err := recover(); err != nil {
			logger.Error("stockservice.GetGoodsNumber报错：", err)
		}
		redisPipeline.Close()
	}()
	var filedlist []string
	for {
		filed, cursor := redisConn.HScan(keyStr, 0, fmt.Sprintf("*:%d", warehouseId), 1000).Val()
		filedlist = append(filedlist, filed...)
		if cursor == 0 {
			break
		}
	}
	out := 0
	if len(filedlist) > 0 {
		redisPipeline.HMGet(keyStr, filedlist...) //获取当前key下所有的filed数据
		stockResult, _ := redisPipeline.Exec()
		for _, v := range stockResult {
			cmd := v.(*redis.StringCmd)
			stockStr, err := cmd.Result()
			if err != nil {
				stockStr = "0"
			}
			valueInt, _ := strconv.Atoi(stockStr) //转换
			out = out + valueInt
		}
	}
	return int32(out)
}

//写入测试数据
func (d *DemolitionOrderService) SetDemolitionOrderTestData(ctx context.Context, params *proto.DataRequest) (*proto.BaseResponse, error) {
	var out = new(proto.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	client := utils.ConnectClusterRedis()
	defer client.Close()

	for _, item := range params.DataList {
		client.Set("stock:"+item.Goodsid+":"+strconv.Itoa(int(item.Warehouseid)), item.Store, 0)
	}
	return out, nil
}

//锁定库存
func (d *DemolitionOrderService) FreezeStock(warehouseOrders []*models.WarehouseOrder, memberId string) (*proto.BaseResponse, error) {
	var out = new(proto.BaseResponse)
	out.Code = 200

	conn, ctx, client, cf := getClient()
	defer conn.Close()
	defer cf()
	var goodsList []*ic.OrderGoodsInfo

	for _, item := range warehouseOrders {
		var goods = new(ic.OrderGoodsInfo)
		goods.GoodsId = item.GoodsId
		goods.WarehouseId = int32(item.WarehouseId)
		goods.Number = int32(item.Quantity)
		goodsList = append(goodsList, goods)
	}

	var params ic.FreezeRequest
	params.OrderId = warehouseOrders[0].OrderId
	params.MemberId = memberId
	params.GoodsList = goodsList
	logger.Infof("FreezeStock的参数：%v", params)
	r, err := client.FreezeStock(ctx, &params)
	logger.Infof("FreezeStock的结果：%s,%s", r, err)
	if err != nil {
		out.Code = 400
		out.Message = err.Error()
		out.Error = err.Error()
		return out, err
	}
	if r.Code != 200 {
		out.Code = r.Code
		out.Message = r.Message
		out.Error = r.Error
		return out, err
	}
	out.Message = "Success"
	return out, nil

}

// 判定整单是否可以分配在同一仓库
func (d *DemolitionOrderService) FirstToWarehouse(warehouses []*dtos.WarehouseModel) (bool, int) {
	for _, item := range warehouses {
		if item.Enough == true {
			return true, item.Id
		}
	}
	return false, 0
}

//整单一个仓库不满足需求递归指定仓库
func (d *DemolitionOrderService) GetWarehouseOrder(store []*dtos.GoodsStoreDto,
	orderDetails []*proto.OrderGoodsDetail, warehouses []*dtos.WarehouseModel, orderId string,
	warehouseOrders []*models.WarehouseOrder) []*models.WarehouseOrder {

	//先初始化仓库数据
	var warehouses1 []*dtos.WarehouseModel
	for _, item := range warehouses {
		warehouses1 = append(warehouses1, item)
	}
	var model = new(dtos.WarehouseModel)
	// 按照 先商品种类，商品数量，配送等级，配送仓库 递归第一个最优仓库
	if len(warehouses1) > 0 {

		//排序商品类型
		//sort.Sort(dtos.Wrapper{warehouse: warehouses1, by: func(p, q *dtos.WarehouseModel) bool {
		//	return q.GoodsType < p.GoodsType // GoodsType 递减排序
		//}})
		dtos.SortLog(warehouses1, func(p, q *dtos.WarehouseModel) bool {
			return q.GoodsType < p.GoodsType
		})

		goodsType := warehouses1[0].GoodsType
		warehouses2 := d.WarehouseSortGoodsType(warehouses1, goodsType)
		if len(warehouses2) > 1 {
			//排序商品数量
			//sort.Sort(dtos.Wrapper{warehouses2, func(p, q *dtos.WarehouseModel) bool {
			//	return q.Goodsnumber < p.Goodsnumber // Goodsnumber 递减排序
			//}})
			dtos.SortLog(warehouses2, func(p, q *dtos.WarehouseModel) bool {
				return q.Goodsnumber < p.Goodsnumber
			})
			goodsNumber := warehouses2[0].Goodsnumber
			warehouses3 := d.WarehouseSortGoodsNumber(warehouses2, goodsNumber)
			if len(warehouses3) > 1 {

				//排序仓库配送等级

				//sort.Sort(dtos.Wrapper{warehouses3, func(p, q *dtos.WarehouseModel) bool {
				//	return q.Arealevel < p.Arealevel // Arealevel 递减排序
				//}})

				dtos.SortLog(warehouses3, func(p, q *dtos.WarehouseModel) bool {
					return p.Arealevel < q.Arealevel
				})
				areaLevel := warehouses3[0].Arealevel
				warehouses4 := d.WarehouseSortAreaLevel(warehouses3, areaLevel)
				if len(warehouses4) > 1 {

					//排序仓库等级

					//sort.Sort(dtos.Wrapper{warehouses4, func(p, q *dtos.WarehouseModel) bool {
					//	return q.Level < p.Level // Level 递减排序
					//}})
					dtos.SortLog(warehouses4, func(p, q *dtos.WarehouseModel) bool {
						return p.Level < q.Level
					})
					level := warehouses4[0].Level
					warehouses5 := d.WarehouseSortLevel(warehouses4, level)
					model = warehouses5[0]
				} else {
					model = warehouses4[0]
				}
			} else {
				model = warehouses3[0]
			}
		} else {
			model = warehouses2[0]
		}
	}

	//设定递归遍历参数 []*dtos.GoodsStoreDto
	var parStores []*dtos.GoodsStoreDto
	var lockStores []*dtos.GoodsStoreDto

	for _, item := range store {
		var goods = new(dtos.GoodsStoreDto)
		goods = item
		if item.WarehouseId == model.Id && item.Quantity > 0 {
			var warehouseOrder = new(models.WarehouseOrder)
			warehouseOrder.WarehouseId = item.WarehouseId
			warehouseOrder.GoodsId = item.GoodsId
			warehouseOrder.Quantity = item.Quantity
			warehouseOrder.Thirdid = item.Thirdid
			warehouseOrder.WarehouseCode = item.Code
			if item.Quantity >= item.Stock {
				warehouseOrder.Quantity = item.Stock
			}
			warehouseOrder.OrderId = orderId
			warehouseOrders = append(warehouseOrders, warehouseOrder)
			var lookStore = new(dtos.GoodsStoreDto)
			lookStore = item
			lookStore.Quantity = warehouseOrder.Quantity
			lockStores = append(lockStores, lookStore)

		} else {
			parStores = append(parStores, goods)
		}

	}

	for _, item := range lockStores {
		for i := 0; i < len(parStores); i++ {
			if item.GoodsId == parStores[i].GoodsId {
				parStores[i].Quantity = parStores[i].Quantity - item.Quantity
				if parStores[i].Quantity < 0 {
					parStores[i].Quantity = 0
				} else {
					parStores[i].Enough = true
				}
			}
		}

	}

	//设定递归遍历参数 *proto.OrderGoodsDetail
	var parOrderDetails []*proto.OrderGoodsDetail
	for _, item := range orderDetails {
		var parOrderDetail = new(proto.OrderGoodsDetail)
		parOrderDetail = item
		for _, itemGoods := range store {
			if itemGoods.WarehouseId == model.Id {
				if itemGoods.GoodsId == parOrderDetail.Goodsid {
					if itemGoods.Stock <= itemGoods.Quantity {
						parOrderDetail.Quantity = parOrderDetail.Quantity - int32(itemGoods.Stock)
						parOrderDetails = append(parOrderDetails, parOrderDetail)
					}
				}

			}
		}

	}

	//判定是否需要继续循环往下遍历
	enough := false
	for _, item := range parOrderDetails {
		if item.Quantity > 0 {
			enough = true
			break
		}
	}
	if enough == true {
		//设定递归遍历参数 []*dtos.WarehouseModel
		var parWarehouses []*dtos.WarehouseModel
		for _, item := range warehouses {
			var parWarehouse = new(dtos.WarehouseModel)
			parWarehouse = item
			if item.Id != model.Id {
				goodsType := 0
				goodsNumber := 0
				for _, itemParStore := range parStores {
					if parWarehouse.Id == itemParStore.WarehouseId {
						if itemParStore.Enough == true {
							goodsNumber = goodsNumber + itemParStore.Stock
							goodsType = goodsType + 1
						} else {
							goodsNumber = goodsNumber + itemParStore.Quantity
						}
					}
				}
				parWarehouse.GoodsType = goodsType
				parWarehouses = append(parWarehouses, parWarehouse)
			}
		}

		return d.GetWarehouseOrder(parStores, parOrderDetails, parWarehouses, orderId, warehouseOrders)
	}

	return warehouseOrders

}

//获取商品种类相同的仓库
func (d *DemolitionOrderService) WarehouseSortGoodsType(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].GoodsType {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取商品数量相同的仓库
func (d *DemolitionOrderService) WarehouseSortGoodsNumber(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Goodsnumber {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取配送区域相同的仓库
func (d *DemolitionOrderService) WarehouseSortAreaLevel(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Arealevel {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取仓库等级相同的仓库
func (d *DemolitionOrderService) WarehouseSortLevel(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Level {
			items = append(items, warehouses[i])
		}
	}
	return items
}

// 设置商品对应发货订单实体
func (d *DemolitionOrderService) SetWarehouseOrder(goodsStores []*dtos.GoodsStoreDto, orderId string, warehouseId int) []*models.WarehouseOrder {
	var warehouseOrders []*models.WarehouseOrder
	for _, item := range goodsStores {
		if item.WarehouseId == warehouseId {
			var warehouseOrder = new(models.WarehouseOrder)
			warehouseOrder.OrderId = orderId
			warehouseOrder.Quantity = item.Quantity
			warehouseOrder.WarehouseId = item.WarehouseId
			warehouseOrder.GoodsId = item.GoodsId
			warehouseOrder.Thirdid = item.Thirdid
			warehouseOrder.WarehouseCode = item.Code
			warehouseOrders = append(warehouseOrders, warehouseOrder)
		}
	}

	return warehouseOrders
}

//订阅保存仓库订单信息
func subscribeSaveWarehouseOrder() {
	redisKey := fmt.Sprintf("dispatchcenter:savewarehouseorder")
	redisClient := utils.ConnectClusterRedis()
	defer redisClient.Close() // 关闭redis

	pubsub := redisClient.Subscribe(redisKey)
	_, err := pubsub.Receive()
	if err != nil {
		return
	}
	ch := pubsub.Channel()
	for msg := range ch {
		lockKey := fmt.Sprintf("lock:dispatchcenter:savewarehouseorder")
		defer redisClient.Del(lockKey)
		//注：集群需要修改成0，0就是永久信息
		if redisClient.SetNX(lockKey, time.Now().Unix(), -1).Val() {
			var model []*models.WarehouseOrder
			_ = json.Unmarshal([]byte(msg.Payload), &model)
			d := DemolitionOrderService{}
			d.SaveWarehouseOrder(model)
		}
	}
}

//保存拆单后 商品对应仓库
func (d *DemolitionOrderService) SaveWarehouseOrder(model []*models.WarehouseOrder) (*proto.BaseResponse, error) {
	var out = new(proto.BaseResponse)
	out.Code = 200
	dbConn := d.GetConn()
	tran := dbConn.NewSession()
	tran.Begin()
	var err error
	if len(model) > 0 {
		_, err = tran.Insert(model)
		if err != nil {
			out.Code = 500
			out.Error = err.Error()
			out.Message = err.Error()
			tran.Rollback()
		}
	}
	return out, nil
}

//订阅保存订单信息
func subscribeSaveOrderParams() {
	redisKey := fmt.Sprintf("dispatchcenter:saveorderparams")
	redisClient := utils.ConnectClusterRedis()
	defer redisClient.Close() // 关闭redis

	pubsub := redisClient.Subscribe(redisKey)
	_, err := pubsub.Receive()
	if err != nil {
		return
	}
	ch := pubsub.Channel()
	for msg := range ch {
		lockKey := fmt.Sprintf("lock:dispatchcenter:saveorderparams")
		defer redisClient.Del(lockKey)
		//注：集群需要修改成0，0就是永久信息
		if redisClient.SetNX(lockKey, time.Now().Unix(), -1).Val() {
			var model proto.DemolitionOrderRequest
			_ = json.Unmarshal([]byte(msg.Payload), &model)
			d := DemolitionOrderService{}
			d.saveOrderParams(model)
		}
	}
}

//保存原始参数，等拆单之后推送需要。
func (d *DemolitionOrderService) saveOrderParams(model proto.DemolitionOrderRequest) (*proto.BaseResponse, error) {
	timeTemplate1 := "2006-01-02 15:04:05" //常规类型
	//定义返回信息
	var out = new(proto.BaseResponse)
	out.Code = 200

	dbConn := d.GetConn()
	tran := dbConn.NewSession()
	tran.Begin()
	var err error
	var orderInfo models.OrderInfo
	//orderInfo.Id = model.Id
	orderInfo.Orderid = model.Orderid
	orderInfo.Memberid = model.Memberid
	orderInfo.Ordermoney = int(model.Ordermoney)
	orderInfo.Ordertype = int(model.Ordertype)
	orderInfo.Ordertypedetail = int(model.Ordertypedetail)
	orderInfo.Orderstate = int(model.Orderstate)
	orderInfo.Orderdetail = model.Orderdetail
	orderInfo.Useragent = int(model.Useragent)
	orderInfo.Platformid = int(model.Platformid)
	orderInfo.Belonghospitalid = model.Belonghospitalid
	createTime, _ := time.ParseInLocation(timeTemplate1, model.Createtime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	orderInfo.Createtime = createTime
	lastTime, _ := time.ParseInLocation(timeTemplate1, model.Lasttime, time.Local)
	orderInfo.Lasttime = lastTime
	orderInfo.Orderchildenstate = int(model.Orderchildenstate)
	orderInfo.Isevaluate = int(model.Isevaluate)
	orderInfo.Ispostupet = int(model.Ispostupet)
	orderInfo.Isnotify = int(model.Isnotify)
	orderInfo.Ordersource = int(model.Ordersource)
	orderInfo.Status = int(model.Status)
	orderInfo.Province = model.Province
	orderInfo.City = model.City
	orderInfo.Region = model.Region

	_, err = tran.Insert(&orderInfo)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = err.Error()
		tran.Rollback()
		return out, err
	}
	var goodsList []models.OrderGoodsDetail
	if len(model.OrderGoodsDetails) > 0 {
		for i := 0; i < len(model.OrderGoodsDetails); i++ {
			orderGoods := model.OrderGoodsDetails[i]
			var goods models.OrderGoodsDetail
			goods.Id = orderGoods.Id
			goods.Orderid = orderGoods.Orderid
			goods.Goodsid = orderGoods.Goodsid
			goods.Barcode = orderGoods.Barcode
			goods.Name = orderGoods.Name
			goods.Goodsimage = orderGoods.Goodsimage
			goods.Univalence = int(orderGoods.Univalence)
			goods.Sellprice = int(orderGoods.Sellprice)
			goods.Quantity = int(orderGoods.Quantity)
			goods.Unit = orderGoods.Unit
			goods.Applyhospitalid = orderGoods.Applyhospitalid
			goods.Chargeoff = int(orderGoods.Chargeoff)
			goods.Chargeoffcode = orderGoods.Chargeoffcode
			goods.Chargeoffhospitalid = orderGoods.ChargeoffhospitalId
			chargeOffTime, _ := time.ParseInLocation(timeTemplate1, orderGoods.Chargeofftime, time.Local)
			goods.Chargeofftime = chargeOffTime
			goodsCreateTime, _ := time.ParseInLocation(timeTemplate1, model.Createtime, time.Local)
			goods.Createtime = goodsCreateTime
			goodsLastTime, _ := time.ParseInLocation(timeTemplate1, model.Lasttime, time.Local)
			goods.Lasttime = goodsLastTime
			goods.Source = int(orderGoods.Source)
			goods.Useragent = int(orderGoods.Useragent)
			goods.Chargeoffmemberid = orderGoods.Chargeoffmemberid
			goodsList = append(goodsList, goods)

		}
		_, err = tran.Insert(&goodsList)
		if err != nil {
			out.Code = 400
			out.Error = err.Error()
			out.Message = err.Error()
			tran.Rollback()
			return out, err
		}
	}

	return out, nil
}

//获取整理好的所有仓库数据
func (d *DemolitionOrderService) GetWarehouseModels(comefrom int, province string) []*dtos.WarehouseModel {
	logger.Info("GetWarehouseModels：", comefrom, province, time.Now())
	var warehouses []*dtos.WarehouseModel
	//读取key
	keyStr := fmt.Sprintf("warehouse:%d:%s", comefrom, province)
	redisCluster := utils.ConnectClusterRedis()
	defer redisCluster.Close()
	if redisCluster.Exists(keyStr).Val() > 0 {
		valueStr := redisCluster.Get(keyStr).Val()
		err := json.Unmarshal([]byte(valueStr), &warehouses)
		if err != nil {
			logger.Error("获取redis的值报错：", err, "key的Value值：", valueStr, "key值：", keyStr)
		}
		return warehouses
	} else {
		warehouses = d.GetWarehouse(comefrom, province)
	}
	logger.Info("GetWarehouseModels result：", warehouses)
	return warehouses
}

//根据商品sku,仓库Id获取当前仓库商品的库存数量
func (d *DemolitionOrderService) GetGoodsStore(goodsId string, warehouseId int) int {
	client := utils.ConnectClusterRedis()
	defer client.Close()

	//var key = fmt.Sprint("stock:" + goodsId + ":" + strconv.Itoa(warehouseId))
	//r := client.Get(key).Val()
	r := client.HGet(fmt.Sprintf("stock:%s", goodsId), fmt.Sprintf("%d", warehouseId)).Val()
	out, _ := strconv.Atoi(r)
	//for _, v := range r {
	//	//获取到的数量
	//	value := utils.ConnectRedis().Get(v).Val()
	//	valueInt, _ := strconv.Atoi(value)
	//	out = out + valueInt
	//}

	return out
}

// 获取仓库主表数据
func (d *DemolitionOrderService) GetWarehouse(comefrom int, province string) []*dtos.WarehouseModel {

	logger.Info("GetWarehouse：", comefrom, province, time.Now())
	dbConn := d.GetConn()
	defer dbConn.Close()
	var warehouseModels []*dtos.WarehouseModel

	sql := `SELECT  a.id,a.thirdid,a.code,a.name,a.comefrom,a.level,a.category,b.level AS areaLevel,b.areaid
			FROM warehouse a 
			INNER JOIN warehouse_area b ON  b.warehouseid=a.id 
			INNER JOIN base_area c ON b.areaid=c.area_id
			WHERE a.status=1  AND a.comefrom=?  AND c.area_name=?
			ORDER BY b.level,a.level;`
	err := dbConn.SQL(sql, comefrom, province).Find(&warehouseModels)
	logger.Info("GetWarehouse：", comefrom, province, err, time.Now())
	if err != nil {
		logger.Error("数据库错误信息：", err)
		var _warehouseModels []*dtos.WarehouseModel
		return _warehouseModels
	}
	warehouseModelsStr, _ := json.Marshal(warehouseModels)
	keyStr := fmt.Sprintf("warehouse:%d:%s", comefrom, province)
	redisCluster := utils.ConnectClusterRedis()
	defer redisCluster.Close()
	redisCluster.Set(keyStr, warehouseModelsStr, 0)
	logger.Info("warehouseModels：", warehouseModels, err, time.Now(), warehouseModelsStr)
	return warehouseModels
}

// 获取仓库配送区域数据
func (d *DemolitionOrderService) GetWarehouseArea(comefrom int, province string) []*models.WarehouseArea {
	var warehouseArea []*models.WarehouseArea

	Engine.SQL("SELECT a.`id`,a.`areaid`,a.`warehouseid`,a.`level`,a.`lastdate`  FROM `warehouse_area` a  JOIN  warehouse  b ON  a.`warehouseid`=b.id  WHERE b.`status`=1  AND b.comefrom=?", comefrom).Find(&warehouseArea)

	return warehouseArea

}

//inventory-center服务客户端
func getClient() (*grpc.ClientConn, context.Context, ic.InventoryServiceClient, context.CancelFunc) {
	return getBaseClient("grpc.inventory-center", "localhost:11007")
}

func getBaseClient(grpcserverkey, defalutvalue string) (*grpc.ClientConn, context.Context, ic.InventoryServiceClient, context.CancelFunc) {
	GrpcAddress := config.GetString(grpcserverkey)
	if GrpcAddress == "" || runtime.GOOS == "windows" {
		GrpcAddress = "localhost:11007"
	}

	logger.Info(grpcserverkey, " ", GrpcAddress)
	if conn, err := grpc.Dial(GrpcAddress, grpc.WithInsecure()); err != nil {
		logger.Error(err)
		return nil, nil, nil, nil
	} else {
		client := ic.NewInventoryServiceClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*30)
		return conn, ctx, client, cf
	}
}

func RemoveRepByMap(slc []dtos.ProductOrderDto) []dtos.ProductOrderDto {
	var result []dtos.ProductOrderDto          //存放返回的不重复切片
	tempMap := map[dtos.ProductOrderDto]byte{} // 存放不重复主键
	for _, e := range slc {
		l := len(tempMap)
		tempMap[e] = 0 //当e存在于tempMap中时，再次添加是添加不进去的，，因为key不允许重复
		//如果上一行添加成功，那么长度发生变化且此时元素一定不重复
		if len(tempMap) != l { // 加入map后，map长度变化，则元素不重复
			result = append(result, e) //当元素不重复时，将元素添加到切片result中
		}
	}
	return result
}
