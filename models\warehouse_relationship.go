package models

import (
	"time"
)

type WarehouseRelationship struct {
	Id          int    `xorm:"not null pk autoincr INT(11)"`
	WarehouseId int    `xorm:"default NULL comment('本地仓库id') INT(11)"`
	ShopId      string `xorm:"default 'NULL' comment('关联的门店id') VARCHAR(100)"`
	ShopName    string `xorm:"default 'NULL' comment('关联门店名称') VARCHAR(200)"`
}

type WarehouseRelationShop struct {
	Id                int    `xorm:"not null pk autoincr INT(11)"`
	WarehouseId       int    `xorm:"default 0 comment('本地仓库id') INT(11)"`
	WarehouseName     string `xorm:"default 'NULL' comment('关联门店名称') VARCHAR(200)"`
	ShopId            string    `xorm:"default 'NULL' comment('关联的门店id') VARCHAR(100)"`
	ShopName          string    `xorm:"default 'NULL' comment('关联门店名称') VARCHAR(200)"`
	ChannelId         int       `xorm:"default 0 comment('渠道id') INT(11)"`
	CreateTime        time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
}

type WarehouseRelationShopRedis struct {
	WarehouseId   int32     `json:"warehouse_id"`
	WarehouseName string    `json:"warehouse_name"`
	Code          string    `json:"code"`
	ShopId        string    `json:"shop_id"`
	ShopName      string    `json:"shop_name"`
	ChannelId     int32     `json:"channel_id"`
	Category      int32     `json:"category"`
	CreateTime    time.Time `json:"create_time"`
}
