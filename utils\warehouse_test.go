package utils

import (
	"fmt"
	"testing"

	"github.com/go-redis/redis"
)

func TestLoadChannelWarehouseCache(t *testing.T) {
	type args struct {
		conn        *redis.Client
		financeCode string
		channelId   int
	}
	redis := ConnectClusterRedis()
	defer redis.Close()
	tests := []struct {
		name string
		args args
	}{
		{
			name: "阿闻渠道",
			args: args{
				conn:        redis,
				financeCode: "CX0013",
				channelId:   1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			warehouse := LoadChannelWarehouseCache(tt.args.conn, tt.args.financeCode, tt.args.channelId)
			fmt.Printf("%s %d %d \n", warehouse.ShopId, warehouse.ChannelId, warehouse.WarehouseId)
		})
	}
}
