// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/upet_dj_service.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 阿闻订单状态
type UpetDjOrderState int32

const (
	// 查询用，代表所有订单
	UpetDjOrderState_all UpetDjOrderState = 0
	// 未付款
	UpetDjOrderState_unPay UpetDjOrderState = 10
	// 未付款取消
	UpetDjOrderState_unPayCancel UpetDjOrderState = 11
	//已付款
	UpetDjOrderState_payed UpetDjOrderState = 20
	// 已付款取消
	UpetDjOrderState_payedCancel UpetDjOrderState = 21
	// 店铺待接单
	UpetDjOrderState_payedWaitShopReceive UpetDjOrderState = 22
	// 店铺已接单
	UpetDjOrderState_payedShopReceived UpetDjOrderState = 23
	// 骑手已接单
	UpetDjOrderState_deliveried UpetDjOrderState = 24
	// 骑手配送中
	UpetDjOrderState_delivering UpetDjOrderState = 25
	// 商家配送中
	UpetDjOrderState_shipping UpetDjOrderState = 26
	// 商家手动已接单
	UpetDjOrderState_payedShopReceivedManual UpetDjOrderState = 27
	// 备货完成, 待买家自提
	UpetDjOrderState_buyerSelfCollection UpetDjOrderState = 28
	// 商家已接单, 备货中
	UpetDjOrderState_payedShopReceivedPicking UpetDjOrderState = 29
	// 已完成
	UpetDjOrderState_finished UpetDjOrderState = 30
	// 拆单中
	UpetDjOrderState_splitIng UpetDjOrderState = 31
	// 拆单失败
	UpetDjOrderState_splitFail UpetDjOrderState = 32
	// 未核销
	UpetDjOrderState_unVerify UpetDjOrderState = 33
	// 待骑手接单
	UpetDjOrderState_waitDeliveried UpetDjOrderState = 34
	// 退款中
	UpetDjOrderState_refunding UpetDjOrderState = 41
	// 退款关闭
	UpetDjOrderState_refundClosed UpetDjOrderState = 42
	// 退款成功
	UpetDjOrderState_refundSuccess UpetDjOrderState = 43
	// 退款初审通过
	UpetDjOrderState_refundFirstPass UpetDjOrderState = 46
	// 退款终审通过
	UpetDjOrderState_refundFinalPass UpetDjOrderState = 47
	// 退款失败
	UpetDjOrderState_refundFail UpetDjOrderState = 48
	// 撤销退款
	UpetDjOrderState_refundUndo UpetDjOrderState = 49
	// 待发货
	UpetDjOrderState_expressWaitingDelivery UpetDjOrderState = 50
	// 已发货
	UpetDjOrderState_expressShipped UpetDjOrderState = 51
)

var UpetDjOrderState_name = map[int32]string{
	0:  "all",
	10: "unPay",
	11: "unPayCancel",
	20: "payed",
	21: "payedCancel",
	22: "payedWaitShopReceive",
	23: "payedShopReceived",
	24: "deliveried",
	25: "delivering",
	26: "shipping",
	27: "payedShopReceivedManual",
	28: "buyerSelfCollection",
	29: "payedShopReceivedPicking",
	30: "finished",
	31: "splitIng",
	32: "splitFail",
	33: "unVerify",
	34: "waitDeliveried",
	41: "refunding",
	42: "refundClosed",
	43: "refundSuccess",
	46: "refundFirstPass",
	47: "refundFinalPass",
	48: "refundFail",
	49: "refundUndo",
	50: "expressWaitingDelivery",
	51: "expressShipped",
}

var UpetDjOrderState_value = map[string]int32{
	"all":                      0,
	"unPay":                    10,
	"unPayCancel":              11,
	"payed":                    20,
	"payedCancel":              21,
	"payedWaitShopReceive":     22,
	"payedShopReceived":        23,
	"deliveried":               24,
	"delivering":               25,
	"shipping":                 26,
	"payedShopReceivedManual":  27,
	"buyerSelfCollection":      28,
	"payedShopReceivedPicking": 29,
	"finished":                 30,
	"splitIng":                 31,
	"splitFail":                32,
	"unVerify":                 33,
	"waitDeliveried":           34,
	"refunding":                41,
	"refundClosed":             42,
	"refundSuccess":            43,
	"refundFirstPass":          46,
	"refundFinalPass":          47,
	"refundFail":               48,
	"refundUndo":               49,
	"expressWaitingDelivery":   50,
	"expressShipped":           51,
}

func (x UpetDjOrderState) String() string {
	return proto.EnumName(UpetDjOrderState_name, int32(x))
}

func (UpetDjOrderState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{0}
}

// 加载省市区列表数据请求
type SectionQueryRequest struct {
	// 上级ID
	ParentId int32 `protobuf:"varint,1,opt,name=parentId,proto3" json:"parentId"`
	// 是否加载子区域
	IsChild              bool     `protobuf:"varint,2,opt,name=isChild,proto3" json:"isChild"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionQueryRequest) Reset()         { *m = SectionQueryRequest{} }
func (m *SectionQueryRequest) String() string { return proto.CompactTextString(m) }
func (*SectionQueryRequest) ProtoMessage()    {}
func (*SectionQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{0}
}

func (m *SectionQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionQueryRequest.Unmarshal(m, b)
}
func (m *SectionQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionQueryRequest.Marshal(b, m, deterministic)
}
func (m *SectionQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionQueryRequest.Merge(m, src)
}
func (m *SectionQueryRequest) XXX_Size() int {
	return xxx_messageInfo_SectionQueryRequest.Size(m)
}
func (m *SectionQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SectionQueryRequest proto.InternalMessageInfo

func (m *SectionQueryRequest) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *SectionQueryRequest) GetIsChild() bool {
	if m != nil {
		return m.IsChild
	}
	return false
}

// 省市区 数据模型
type SectionDto struct {
	// 区域Id
	Id int32 `protobuf:"varint,2,opt,name=id,proto3" json:"id"`
	// 上级部门Id
	ParentId int32 `protobuf:"varint,1,opt,name=parentId,proto3" json:"parentId"`
	// 名称
	SectionName string `protobuf:"bytes,3,opt,name=sectionName,proto3" json:"sectionName"`
	// 子区域
	ChildSectionDto      []*SectionDto `protobuf:"bytes,4,rep,name=childSectionDto,proto3" json:"childSectionDto"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SectionDto) Reset()         { *m = SectionDto{} }
func (m *SectionDto) String() string { return proto.CompactTextString(m) }
func (*SectionDto) ProtoMessage()    {}
func (*SectionDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{1}
}

func (m *SectionDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionDto.Unmarshal(m, b)
}
func (m *SectionDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionDto.Marshal(b, m, deterministic)
}
func (m *SectionDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionDto.Merge(m, src)
}
func (m *SectionDto) XXX_Size() int {
	return xxx_messageInfo_SectionDto.Size(m)
}
func (m *SectionDto) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionDto.DiscardUnknown(m)
}

var xxx_messageInfo_SectionDto proto.InternalMessageInfo

func (m *SectionDto) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SectionDto) GetParentId() int32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *SectionDto) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionDto) GetChildSectionDto() []*SectionDto {
	if m != nil {
		return m.ChildSectionDto
	}
	return nil
}

// 加载省市区列表数据响应
type SectionQueryResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,2,opt,name=code,proto3" json:"code"`
	// 省市列表
	Data                 []*SectionDto `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SectionQueryResponse) Reset()         { *m = SectionQueryResponse{} }
func (m *SectionQueryResponse) String() string { return proto.CompactTextString(m) }
func (*SectionQueryResponse) ProtoMessage()    {}
func (*SectionQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{2}
}

func (m *SectionQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionQueryResponse.Unmarshal(m, b)
}
func (m *SectionQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionQueryResponse.Marshal(b, m, deterministic)
}
func (m *SectionQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionQueryResponse.Merge(m, src)
}
func (m *SectionQueryResponse) XXX_Size() int {
	return xxx_messageInfo_SectionQueryResponse.Size(m)
}
func (m *SectionQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SectionQueryResponse proto.InternalMessageInfo

func (m *SectionQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *SectionQueryResponse) GetData() []*SectionDto {
	if m != nil {
		return m.Data
	}
	return nil
}

// 根据配送区域计算运费数据请求
type UpetDjMoneyCalcRequest struct {
	// 店铺Id
	ShopId string `protobuf:"bytes,1,opt,name=shopId,proto3" json:"shopId"`
	//目的地坐标X
	DestinationX float64 `protobuf:"fixed64,3,opt,name=destinationX,proto3" json:"destinationX"`
	//目的地坐标Y
	DestinationY float64 `protobuf:"fixed64,4,opt,name=destinationY,proto3" json:"destinationY"`
	// 总重量，换算成克
	TotalWeight int32 `protobuf:"varint,5,opt,name=totalWeight,proto3" json:"totalWeight"`
	// 商品sku列表
	Products             []*UpetDjMoneyCalcProductDto `protobuf:"bytes,6,rep,name=products,proto3" json:"products"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UpetDjMoneyCalcRequest) Reset()         { *m = UpetDjMoneyCalcRequest{} }
func (m *UpetDjMoneyCalcRequest) String() string { return proto.CompactTextString(m) }
func (*UpetDjMoneyCalcRequest) ProtoMessage()    {}
func (*UpetDjMoneyCalcRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{3}
}

func (m *UpetDjMoneyCalcRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjMoneyCalcRequest.Unmarshal(m, b)
}
func (m *UpetDjMoneyCalcRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjMoneyCalcRequest.Marshal(b, m, deterministic)
}
func (m *UpetDjMoneyCalcRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjMoneyCalcRequest.Merge(m, src)
}
func (m *UpetDjMoneyCalcRequest) XXX_Size() int {
	return xxx_messageInfo_UpetDjMoneyCalcRequest.Size(m)
}
func (m *UpetDjMoneyCalcRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjMoneyCalcRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjMoneyCalcRequest proto.InternalMessageInfo

func (m *UpetDjMoneyCalcRequest) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *UpetDjMoneyCalcRequest) GetDestinationX() float64 {
	if m != nil {
		return m.DestinationX
	}
	return 0
}

func (m *UpetDjMoneyCalcRequest) GetDestinationY() float64 {
	if m != nil {
		return m.DestinationY
	}
	return 0
}

func (m *UpetDjMoneyCalcRequest) GetTotalWeight() int32 {
	if m != nil {
		return m.TotalWeight
	}
	return 0
}

func (m *UpetDjMoneyCalcRequest) GetProducts() []*UpetDjMoneyCalcProductDto {
	if m != nil {
		return m.Products
	}
	return nil
}

// 商品信息
type UpetDjMoneyCalcProductDto struct {
	// 商品数量
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	// 商品Sku
	SkuId                string   `protobuf:"bytes,2,opt,name=skuId,proto3" json:"skuId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjMoneyCalcProductDto) Reset()         { *m = UpetDjMoneyCalcProductDto{} }
func (m *UpetDjMoneyCalcProductDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjMoneyCalcProductDto) ProtoMessage()    {}
func (*UpetDjMoneyCalcProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{4}
}

func (m *UpetDjMoneyCalcProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjMoneyCalcProductDto.Unmarshal(m, b)
}
func (m *UpetDjMoneyCalcProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjMoneyCalcProductDto.Marshal(b, m, deterministic)
}
func (m *UpetDjMoneyCalcProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjMoneyCalcProductDto.Merge(m, src)
}
func (m *UpetDjMoneyCalcProductDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjMoneyCalcProductDto.Size(m)
}
func (m *UpetDjMoneyCalcProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjMoneyCalcProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjMoneyCalcProductDto proto.InternalMessageInfo

func (m *UpetDjMoneyCalcProductDto) GetCount() int32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *UpetDjMoneyCalcProductDto) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

// 根据配送区域计算运费数据Dto
type UpetDjMoneyDto struct {
	// 运费金额
	UpetDjMoney float32 `protobuf:"fixed32,1,opt,name=upetDjMoney,proto3" json:"upetDjMoney"`
	// 时间
	UpetDjSeconds float32 `protobuf:"fixed32,2,opt,name=upetDjSeconds,proto3" json:"upetDjSeconds"`
	// 总重量
	TotalWeight int32 `protobuf:"varint,3,opt,name=totalWeight,proto3" json:"totalWeight"`
	// 运费金额,以分为单位
	UpetDjMoneyByMinUnit int32    `protobuf:"varint,5,opt,name=upetDjMoneyByMinUnit,proto3" json:"upetDjMoneyByMinUnit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjMoneyDto) Reset()         { *m = UpetDjMoneyDto{} }
func (m *UpetDjMoneyDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjMoneyDto) ProtoMessage()    {}
func (*UpetDjMoneyDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{5}
}

func (m *UpetDjMoneyDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjMoneyDto.Unmarshal(m, b)
}
func (m *UpetDjMoneyDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjMoneyDto.Marshal(b, m, deterministic)
}
func (m *UpetDjMoneyDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjMoneyDto.Merge(m, src)
}
func (m *UpetDjMoneyDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjMoneyDto.Size(m)
}
func (m *UpetDjMoneyDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjMoneyDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjMoneyDto proto.InternalMessageInfo

func (m *UpetDjMoneyDto) GetUpetDjMoney() float32 {
	if m != nil {
		return m.UpetDjMoney
	}
	return 0
}

func (m *UpetDjMoneyDto) GetUpetDjSeconds() float32 {
	if m != nil {
		return m.UpetDjSeconds
	}
	return 0
}

func (m *UpetDjMoneyDto) GetTotalWeight() int32 {
	if m != nil {
		return m.TotalWeight
	}
	return 0
}

func (m *UpetDjMoneyDto) GetUpetDjMoneyByMinUnit() int32 {
	if m != nil {
		return m.UpetDjMoneyByMinUnit
	}
	return 0
}

// 根据配送区域计算运费数据响应
type UpetDjMoneyResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,2,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message"`
	// 运费数据 Dto
	Dto                  *UpetDjMoneyDto `protobuf:"bytes,1,opt,name=dto,proto3" json:"dto"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpetDjMoneyResponse) Reset()         { *m = UpetDjMoneyResponse{} }
func (m *UpetDjMoneyResponse) String() string { return proto.CompactTextString(m) }
func (*UpetDjMoneyResponse) ProtoMessage()    {}
func (*UpetDjMoneyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{6}
}

func (m *UpetDjMoneyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjMoneyResponse.Unmarshal(m, b)
}
func (m *UpetDjMoneyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjMoneyResponse.Marshal(b, m, deterministic)
}
func (m *UpetDjMoneyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjMoneyResponse.Merge(m, src)
}
func (m *UpetDjMoneyResponse) XXX_Size() int {
	return xxx_messageInfo_UpetDjMoneyResponse.Size(m)
}
func (m *UpetDjMoneyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjMoneyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjMoneyResponse proto.InternalMessageInfo

func (m *UpetDjMoneyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpetDjMoneyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpetDjMoneyResponse) GetDto() *UpetDjMoneyDto {
	if m != nil {
		return m.Dto
	}
	return nil
}

// 确认收货数据请求
type UpetDjConfirmRequest struct {
	// 订单Id
	OrderId              string   `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjConfirmRequest) Reset()         { *m = UpetDjConfirmRequest{} }
func (m *UpetDjConfirmRequest) String() string { return proto.CompactTextString(m) }
func (*UpetDjConfirmRequest) ProtoMessage()    {}
func (*UpetDjConfirmRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{7}
}

func (m *UpetDjConfirmRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjConfirmRequest.Unmarshal(m, b)
}
func (m *UpetDjConfirmRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjConfirmRequest.Marshal(b, m, deterministic)
}
func (m *UpetDjConfirmRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjConfirmRequest.Merge(m, src)
}
func (m *UpetDjConfirmRequest) XXX_Size() int {
	return xxx_messageInfo_UpetDjConfirmRequest.Size(m)
}
func (m *UpetDjConfirmRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjConfirmRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjConfirmRequest proto.InternalMessageInfo

func (m *UpetDjConfirmRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 确认收货数据相应
type UpetDjConfirmResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjConfirmResponse) Reset()         { *m = UpetDjConfirmResponse{} }
func (m *UpetDjConfirmResponse) String() string { return proto.CompactTextString(m) }
func (*UpetDjConfirmResponse) ProtoMessage()    {}
func (*UpetDjConfirmResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{8}
}

func (m *UpetDjConfirmResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjConfirmResponse.Unmarshal(m, b)
}
func (m *UpetDjConfirmResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjConfirmResponse.Marshal(b, m, deterministic)
}
func (m *UpetDjConfirmResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjConfirmResponse.Merge(m, src)
}
func (m *UpetDjConfirmResponse) XXX_Size() int {
	return xxx_messageInfo_UpetDjConfirmResponse.Size(m)
}
func (m *UpetDjConfirmResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjConfirmResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjConfirmResponse proto.InternalMessageInfo

func (m *UpetDjConfirmResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpetDjConfirmResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 400 响应内容
type BadRequestResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BadRequestResponse) Reset()         { *m = BadRequestResponse{} }
func (m *BadRequestResponse) String() string { return proto.CompactTextString(m) }
func (*BadRequestResponse) ProtoMessage()    {}
func (*BadRequestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{9}
}

func (m *BadRequestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BadRequestResponse.Unmarshal(m, b)
}
func (m *BadRequestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BadRequestResponse.Marshal(b, m, deterministic)
}
func (m *BadRequestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BadRequestResponse.Merge(m, src)
}
func (m *BadRequestResponse) XXX_Size() int {
	return xxx_messageInfo_BadRequestResponse.Size(m)
}
func (m *BadRequestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BadRequestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BadRequestResponse proto.InternalMessageInfo

func (m *BadRequestResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BadRequestResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 阿闻到家订单查询
type UpetDjOrderQueryRequest struct {
	// 页索引
	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,proto3" json:"pageIndex"`
	// 页大小
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// 店铺Id
	ShopId []string `protobuf:"bytes,3,rep,name=shopId,proto3" json:"shopId"`
	// 登录会员Id
	MemberId string `protobuf:"bytes,4,opt,name=memberId,proto3" json:"memberId"`
	//订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 30 已完成
	State UpetDjOrderState `protobuf:"varint,5,opt,name=state,proto3,enum=oc.UpetDjOrderState" json:"state"`
	//订单号
	OrderSn              string   `protobuf:"bytes,6,opt,name=orderSn,proto3" json:"orderSn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderQueryRequest) Reset()         { *m = UpetDjOrderQueryRequest{} }
func (m *UpetDjOrderQueryRequest) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderQueryRequest) ProtoMessage()    {}
func (*UpetDjOrderQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{10}
}

func (m *UpetDjOrderQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderQueryRequest.Unmarshal(m, b)
}
func (m *UpetDjOrderQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderQueryRequest.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderQueryRequest.Merge(m, src)
}
func (m *UpetDjOrderQueryRequest) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderQueryRequest.Size(m)
}
func (m *UpetDjOrderQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderQueryRequest proto.InternalMessageInfo

func (m *UpetDjOrderQueryRequest) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *UpetDjOrderQueryRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *UpetDjOrderQueryRequest) GetShopId() []string {
	if m != nil {
		return m.ShopId
	}
	return nil
}

func (m *UpetDjOrderQueryRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

func (m *UpetDjOrderQueryRequest) GetState() UpetDjOrderState {
	if m != nil {
		return m.State
	}
	return UpetDjOrderState_all
}

func (m *UpetDjOrderQueryRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

// 根据订单Id查询到家订单详情
type UpetDjOrderDetailRequest struct {
	// 订单Id
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 订单号码 orderId 与 orderSn任选其一
	OrderSn              string   `protobuf:"bytes,2,opt,name=orderSn,proto3" json:"orderSn"`
	MemberId             string   `protobuf:"bytes,3,opt,name=member_id,json=memberId,proto3" json:"member_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderDetailRequest) Reset()         { *m = UpetDjOrderDetailRequest{} }
func (m *UpetDjOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderDetailRequest) ProtoMessage()    {}
func (*UpetDjOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{11}
}

func (m *UpetDjOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderDetailRequest.Unmarshal(m, b)
}
func (m *UpetDjOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderDetailRequest.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderDetailRequest.Merge(m, src)
}
func (m *UpetDjOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderDetailRequest.Size(m)
}
func (m *UpetDjOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderDetailRequest proto.InternalMessageInfo

func (m *UpetDjOrderDetailRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UpetDjOrderDetailRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UpetDjOrderDetailRequest) GetMemberId() string {
	if m != nil {
		return m.MemberId
	}
	return ""
}

// 阿闻订单数据模型
type UpetDjOrderDto struct {
	// 订单Id
	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	// 订单号码
	OrderNo string `protobuf:"bytes,2,opt,name=orderNo,proto3" json:"orderNo"`
	// 订单号码
	ParentOrderSn string `protobuf:"bytes,3,opt,name=parent_order_sn,json=parentOrderSn,proto3" json:"parent_order_sn"`
	//店铺Id
	ShopId string `protobuf:"bytes,4,opt,name=shopId,proto3" json:"shopId"`
	//店铺名字
	ShopName string `protobuf:"bytes,5,opt,name=shopName,proto3" json:"shopName"`
	//店铺联系电话
	ShopMobile string `protobuf:"bytes,6,opt,name=shopMobile,proto3" json:"shopMobile"`
	//订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 27 商家手动已接单 28 备货完成, 待买家自提 29 商家已接单, 备货中 30 已完成, 31拆单中, 32拆单失败, 33未核销, 34待骑手接单，50已发货
	State UpetDjOrderState `protobuf:"varint,7,opt,name=state,proto3,enum=oc.UpetDjOrderState" json:"state"`
	// 订单总金额
	TotalMoney float32 `protobuf:"fixed32,8,opt,name=totalMoney,proto3" json:"totalMoney"`
	// 优惠金额
	Privilege float64 `protobuf:"fixed64,9,opt,name=privilege,proto3" json:"privilege"`
	// 下单日期
	CreateDateTime string `protobuf:"bytes,10,opt,name=createDateTime,proto3" json:"createDateTime"`
	// 支付方式 0 待支付 1支付宝  2微信 3美团 4其他
	PayMode int32 `protobuf:"varint,11,opt,name=payMode,proto3" json:"payMode"`
	//有售后记录 0 无 11 整单退款 12 整单退货 21 部分退款 22 部分退货
	ReturnWay int32 `protobuf:"varint,12,opt,name=returnWay,proto3" json:"returnWay"`
	// 最近一次售后记录
	ReturnNo string `protobuf:"bytes,13,opt,name=returnNo,proto3" json:"returnNo"`
	// 订单备注
	Remarks string `protobuf:"bytes,14,opt,name=remarks,proto3" json:"remarks"`
	// 订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
	ReturnState int32 `protobuf:"varint,15,opt,name=returnState,proto3" json:"returnState"`
	// 店铺营业时间段
	BusinessTimes string `protobuf:"bytes,16,opt,name=business_times,json=businessTimes,proto3" json:"business_times"`
	// 取货码
	PickupCode string `protobuf:"bytes,17,opt,name=pickup_code,json=pickupCode,proto3" json:"pickup_code"`
	// 店铺地址
	ShopAddress string `protobuf:"bytes,18,opt,name=shop_address,json=shopAddress,proto3" json:"shop_address"`
	// 店铺备货时长
	StockUpTime int32 `protobuf:"varint,19,opt,name=stock_up_time,json=stockUpTime,proto3" json:"stock_up_time"`
	// 是否显示退款按钮
	IsApplyBtn bool `protobuf:"varint,20,opt,name=is_apply_btn,json=isApplyBtn,proto3" json:"is_apply_btn"`
	// 是否是虚拟订单，0否1是
	IsVirtual int32 `protobuf:"varint,21,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual"`
	// 是否可以取消订单
	EnableCancel bool `protobuf:"varint,24,opt,name=enable_cancel,json=enableCancel,proto3" json:"enable_cancel"`
	// 阿闻订单商品列表
	ProductList []*UpetDjOrderProductDto `protobuf:"bytes,22,rep,name=productList,proto3" json:"productList"`
	// 阿闻订单子订单列表
	ChildOrderList []*UpetDjOrderDto `protobuf:"bytes,23,rep,name=childOrderList,proto3" json:"childOrderList"`
	// 阿闻宠团团这个门店（财务编码：QZC00300）的所有订单，在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款
	// 0默认显示 1不显示
	NoDisplayAfterPayment int32                     `protobuf:"varint,25,opt,name=no_display_after_payment,json=noDisplayAfterPayment,proto3" json:"no_display_after_payment"`
	IsPay                 int32                     `protobuf:"varint,26,opt,name=is_pay,json=isPay,proto3" json:"is_pay"`
	GroupInfo             *UpetDjOrderDto_GroupInfo `protobuf:"bytes,27,opt,name=group_info,json=groupInfo,proto3" json:"group_info"`
	//渠道id（datacenter.platform_channel表）,订单来源 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗
	ChannelId int32 `protobuf:"varint,28,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 处方单号
	ConsultOrderSn       string   `protobuf:"bytes,29,opt,name=consult_order_sn,json=consultOrderSn,proto3" json:"consult_order_sn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderDto) Reset()         { *m = UpetDjOrderDto{} }
func (m *UpetDjOrderDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderDto) ProtoMessage()    {}
func (*UpetDjOrderDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{12}
}

func (m *UpetDjOrderDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderDto.Unmarshal(m, b)
}
func (m *UpetDjOrderDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderDto.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderDto.Merge(m, src)
}
func (m *UpetDjOrderDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderDto.Size(m)
}
func (m *UpetDjOrderDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderDto proto.InternalMessageInfo

func (m *UpetDjOrderDto) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UpetDjOrderDto) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *UpetDjOrderDto) GetParentOrderSn() string {
	if m != nil {
		return m.ParentOrderSn
	}
	return ""
}

func (m *UpetDjOrderDto) GetShopId() string {
	if m != nil {
		return m.ShopId
	}
	return ""
}

func (m *UpetDjOrderDto) GetShopName() string {
	if m != nil {
		return m.ShopName
	}
	return ""
}

func (m *UpetDjOrderDto) GetShopMobile() string {
	if m != nil {
		return m.ShopMobile
	}
	return ""
}

func (m *UpetDjOrderDto) GetState() UpetDjOrderState {
	if m != nil {
		return m.State
	}
	return UpetDjOrderState_all
}

func (m *UpetDjOrderDto) GetTotalMoney() float32 {
	if m != nil {
		return m.TotalMoney
	}
	return 0
}

func (m *UpetDjOrderDto) GetPrivilege() float64 {
	if m != nil {
		return m.Privilege
	}
	return 0
}

func (m *UpetDjOrderDto) GetCreateDateTime() string {
	if m != nil {
		return m.CreateDateTime
	}
	return ""
}

func (m *UpetDjOrderDto) GetPayMode() int32 {
	if m != nil {
		return m.PayMode
	}
	return 0
}

func (m *UpetDjOrderDto) GetReturnWay() int32 {
	if m != nil {
		return m.ReturnWay
	}
	return 0
}

func (m *UpetDjOrderDto) GetReturnNo() string {
	if m != nil {
		return m.ReturnNo
	}
	return ""
}

func (m *UpetDjOrderDto) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *UpetDjOrderDto) GetReturnState() int32 {
	if m != nil {
		return m.ReturnState
	}
	return 0
}

func (m *UpetDjOrderDto) GetBusinessTimes() string {
	if m != nil {
		return m.BusinessTimes
	}
	return ""
}

func (m *UpetDjOrderDto) GetPickupCode() string {
	if m != nil {
		return m.PickupCode
	}
	return ""
}

func (m *UpetDjOrderDto) GetShopAddress() string {
	if m != nil {
		return m.ShopAddress
	}
	return ""
}

func (m *UpetDjOrderDto) GetStockUpTime() int32 {
	if m != nil {
		return m.StockUpTime
	}
	return 0
}

func (m *UpetDjOrderDto) GetIsApplyBtn() bool {
	if m != nil {
		return m.IsApplyBtn
	}
	return false
}

func (m *UpetDjOrderDto) GetIsVirtual() int32 {
	if m != nil {
		return m.IsVirtual
	}
	return 0
}

func (m *UpetDjOrderDto) GetEnableCancel() bool {
	if m != nil {
		return m.EnableCancel
	}
	return false
}

func (m *UpetDjOrderDto) GetProductList() []*UpetDjOrderProductDto {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *UpetDjOrderDto) GetChildOrderList() []*UpetDjOrderDto {
	if m != nil {
		return m.ChildOrderList
	}
	return nil
}

func (m *UpetDjOrderDto) GetNoDisplayAfterPayment() int32 {
	if m != nil {
		return m.NoDisplayAfterPayment
	}
	return 0
}

func (m *UpetDjOrderDto) GetIsPay() int32 {
	if m != nil {
		return m.IsPay
	}
	return 0
}

func (m *UpetDjOrderDto) GetGroupInfo() *UpetDjOrderDto_GroupInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

func (m *UpetDjOrderDto) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpetDjOrderDto) GetConsultOrderSn() string {
	if m != nil {
		return m.ConsultOrderSn
	}
	return ""
}

type UpetDjOrderDto_GroupInfo struct {
	// 社区团购活动状态 0开团中 1拼团成功 2拼团失败
	GroupStatus int32 `protobuf:"varint,1,opt,name=group_status,json=groupStatus,proto3" json:"group_status"`
	// 收件人
	ReceiverName string `protobuf:"bytes,2,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	// 收件省
	ReceiverState string `protobuf:"bytes,3,opt,name=receiver_state,json=receiverState,proto3" json:"receiver_state"`
	// 收件市
	ReceiverCity string `protobuf:"bytes,4,opt,name=receiver_city,json=receiverCity,proto3" json:"receiver_city"`
	// 收件区
	ReceiverDistrict string `protobuf:"bytes,5,opt,name=receiver_district,json=receiverDistrict,proto3" json:"receiver_district"`
	// 收件地址
	ReceiverAddress string `protobuf:"bytes,6,opt,name=receiver_address,json=receiverAddress,proto3" json:"receiver_address"`
	// 收件手机
	ReceiverMobile string `protobuf:"bytes,7,opt,name=receiver_mobile,json=receiverMobile,proto3" json:"receiver_mobile"`
	// 是否团长 1是 0否
	GroupLeader int32 `protobuf:"varint,8,opt,name=group_leader,json=groupLeader,proto3" json:"group_leader"`
	// 团员信息 名字
	GroupName string `protobuf:"bytes,9,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	// 团员信息 手机
	GroupMobile string `protobuf:"bytes,10,opt,name=group_mobile,json=groupMobile,proto3" json:"group_mobile"`
	// 团长代收状态 0不代收 1代收
	FinalTakeType int32 `protobuf:"varint,11,opt,name=final_take_type,json=finalTakeType,proto3" json:"final_take_type"`
	// 团ID
	GroupId int32 `protobuf:"varint,12,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 最晚N日内送达
	DeliverDays string `protobuf:"bytes,13,opt,name=deliver_days,json=deliverDays,proto3" json:"deliver_days"`
	// 团员信息 地址
	GroupAddress         string   `protobuf:"bytes,14,opt,name=group_address,json=groupAddress,proto3" json:"group_address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderDto_GroupInfo) Reset()         { *m = UpetDjOrderDto_GroupInfo{} }
func (m *UpetDjOrderDto_GroupInfo) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderDto_GroupInfo) ProtoMessage()    {}
func (*UpetDjOrderDto_GroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{12, 0}
}

func (m *UpetDjOrderDto_GroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderDto_GroupInfo.Unmarshal(m, b)
}
func (m *UpetDjOrderDto_GroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderDto_GroupInfo.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderDto_GroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderDto_GroupInfo.Merge(m, src)
}
func (m *UpetDjOrderDto_GroupInfo) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderDto_GroupInfo.Size(m)
}
func (m *UpetDjOrderDto_GroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderDto_GroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderDto_GroupInfo proto.InternalMessageInfo

func (m *UpetDjOrderDto_GroupInfo) GetGroupStatus() int32 {
	if m != nil {
		return m.GroupStatus
	}
	return 0
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverState() string {
	if m != nil {
		return m.ReceiverState
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverCity() string {
	if m != nil {
		return m.ReceiverCity
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverDistrict() string {
	if m != nil {
		return m.ReceiverDistrict
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverAddress() string {
	if m != nil {
		return m.ReceiverAddress
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetReceiverMobile() string {
	if m != nil {
		return m.ReceiverMobile
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetGroupLeader() int32 {
	if m != nil {
		return m.GroupLeader
	}
	return 0
}

func (m *UpetDjOrderDto_GroupInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetGroupMobile() string {
	if m != nil {
		return m.GroupMobile
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetFinalTakeType() int32 {
	if m != nil {
		return m.FinalTakeType
	}
	return 0
}

func (m *UpetDjOrderDto_GroupInfo) GetGroupId() int32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpetDjOrderDto_GroupInfo) GetDeliverDays() string {
	if m != nil {
		return m.DeliverDays
	}
	return ""
}

func (m *UpetDjOrderDto_GroupInfo) GetGroupAddress() string {
	if m != nil {
		return m.GroupAddress
	}
	return ""
}

// 配送模型
type UpetDjDeliveryDto struct {
	//收货人名称
	Receivername string `protobuf:"bytes,1,opt,name=receivername,proto3" json:"receivername"`
	//收货省
	Receiverstate string `protobuf:"bytes,2,opt,name=receiverstate,proto3" json:"receiverstate"`
	//收货市
	Receivercity string `protobuf:"bytes,3,opt,name=receivercity,proto3" json:"receivercity"`
	//收货区
	Receiverdistrict string `protobuf:"bytes,4,opt,name=receiverdistrict,proto3" json:"receiverdistrict"`
	//收货地址
	Receiveraddress string `protobuf:"bytes,5,opt,name=receiveraddress,proto3" json:"receiveraddress"`
	//收货电话
	Receiverphone string `protobuf:"bytes,6,opt,name=receiverphone,proto3" json:"receiverphone"`
	//收货人手机号
	Receivermobile string `protobuf:"bytes,7,opt,name=receivermobile,proto3" json:"receivermobile"`
	//配送费
	Freight float64 `protobuf:"fixed64,8,opt,name=freight,proto3" json:"freight"`
	// 送达时间
	DeliveryTime string `protobuf:"bytes,9,opt,name=deliveryTime,proto3" json:"deliveryTime"`
	// 配送方式 1 快递 2 外卖 3 自提 4 同城
	DeliveryType int32 `protobuf:"varint,10,opt,name=deliveryType,proto3" json:"deliveryType"`
	//配送方式名称
	DeliveryTypeName string `protobuf:"bytes,21,opt,name=deliveryTypeName,proto3" json:"deliveryTypeName"`
	// 配送标识
	DeliveryId int64 `protobuf:"varint,15,opt,name=deliveryId,proto3" json:"deliveryId"`
	// 配送单号
	DeliveryOrderId string `protobuf:"bytes,16,opt,name=deliveryOrderId,proto3" json:"deliveryOrderId"`
	// 配送服务代码
	DeliveryServiceCode string `protobuf:"bytes,18,opt,name=deliveryServiceCode,proto3" json:"deliveryServiceCode"`
	// 收货地址维度
	Latitude float64 `protobuf:"fixed64,11,opt,name=latitude,proto3" json:"latitude"`
	// 收货地址经度
	Longitude float64 `protobuf:"fixed64,12,opt,name=longitude,proto3" json:"longitude"`
	// 店铺维度
	ShopLatitude float64 `protobuf:"fixed64,13,opt,name=shopLatitude,proto3" json:"shopLatitude"`
	// 店铺经度
	ShopLongitude float64 `protobuf:"fixed64,14,opt,name=shopLongitude,proto3" json:"shopLongitude"`
	//提货点名称
	PickupStationName string `protobuf:"bytes,19,opt,name=pickupStationName,proto3" json:"pickupStationName"`
	//提货点地址
	PickupStationAddress string `protobuf:"bytes,20,opt,name=pickupStationAddress,proto3" json:"pickupStationAddress"`
	// 配送节点信息
	Nodes []*UpetDjDeliverNodesDto `protobuf:"bytes,17,rep,name=nodes,proto3" json:"nodes"`
	// 订单发货记录
	Express []*UpetDjDeliveryDto_OrderExpress `protobuf:"bytes,22,rep,name=express,proto3" json:"express"`
	// 配送标识字符串类型
	DeliveryIdStr        string   `protobuf:"bytes,23,opt,name=deliveryIdStr,proto3" json:"deliveryIdStr"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjDeliveryDto) Reset()         { *m = UpetDjDeliveryDto{} }
func (m *UpetDjDeliveryDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjDeliveryDto) ProtoMessage()    {}
func (*UpetDjDeliveryDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{13}
}

func (m *UpetDjDeliveryDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjDeliveryDto.Unmarshal(m, b)
}
func (m *UpetDjDeliveryDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjDeliveryDto.Marshal(b, m, deterministic)
}
func (m *UpetDjDeliveryDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjDeliveryDto.Merge(m, src)
}
func (m *UpetDjDeliveryDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjDeliveryDto.Size(m)
}
func (m *UpetDjDeliveryDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjDeliveryDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjDeliveryDto proto.InternalMessageInfo

func (m *UpetDjDeliveryDto) GetReceivername() string {
	if m != nil {
		return m.Receivername
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceiverstate() string {
	if m != nil {
		return m.Receiverstate
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceivercity() string {
	if m != nil {
		return m.Receivercity
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceiverdistrict() string {
	if m != nil {
		return m.Receiverdistrict
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceiveraddress() string {
	if m != nil {
		return m.Receiveraddress
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceiverphone() string {
	if m != nil {
		return m.Receiverphone
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetReceivermobile() string {
	if m != nil {
		return m.Receivermobile
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetFreight() float64 {
	if m != nil {
		return m.Freight
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetDeliveryTime() string {
	if m != nil {
		return m.DeliveryTime
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetDeliveryType() int32 {
	if m != nil {
		return m.DeliveryType
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetDeliveryTypeName() string {
	if m != nil {
		return m.DeliveryTypeName
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetDeliveryId() int64 {
	if m != nil {
		return m.DeliveryId
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetDeliveryOrderId() string {
	if m != nil {
		return m.DeliveryOrderId
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetDeliveryServiceCode() string {
	if m != nil {
		return m.DeliveryServiceCode
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetLatitude() float64 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetLongitude() float64 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetShopLatitude() float64 {
	if m != nil {
		return m.ShopLatitude
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetShopLongitude() float64 {
	if m != nil {
		return m.ShopLongitude
	}
	return 0
}

func (m *UpetDjDeliveryDto) GetPickupStationName() string {
	if m != nil {
		return m.PickupStationName
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetPickupStationAddress() string {
	if m != nil {
		return m.PickupStationAddress
	}
	return ""
}

func (m *UpetDjDeliveryDto) GetNodes() []*UpetDjDeliverNodesDto {
	if m != nil {
		return m.Nodes
	}
	return nil
}

func (m *UpetDjDeliveryDto) GetExpress() []*UpetDjDeliveryDto_OrderExpress {
	if m != nil {
		return m.Express
	}
	return nil
}

func (m *UpetDjDeliveryDto) GetDeliveryIdStr() string {
	if m != nil {
		return m.DeliveryIdStr
	}
	return ""
}

type UpetDjDeliveryDto_OrderExpress struct {
	//快递单号
	ExpressNo string `protobuf:"bytes,1,opt,name=express_no,json=expressNo,proto3" json:"express_no"`
	//商品数量
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num"`
	//快递名称
	ExpressName          string   `protobuf:"bytes,3,opt,name=express_name,json=expressName,proto3" json:"express_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjDeliveryDto_OrderExpress) Reset()         { *m = UpetDjDeliveryDto_OrderExpress{} }
func (m *UpetDjDeliveryDto_OrderExpress) String() string { return proto.CompactTextString(m) }
func (*UpetDjDeliveryDto_OrderExpress) ProtoMessage()    {}
func (*UpetDjDeliveryDto_OrderExpress) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{13, 0}
}

func (m *UpetDjDeliveryDto_OrderExpress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjDeliveryDto_OrderExpress.Unmarshal(m, b)
}
func (m *UpetDjDeliveryDto_OrderExpress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjDeliveryDto_OrderExpress.Marshal(b, m, deterministic)
}
func (m *UpetDjDeliveryDto_OrderExpress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjDeliveryDto_OrderExpress.Merge(m, src)
}
func (m *UpetDjDeliveryDto_OrderExpress) XXX_Size() int {
	return xxx_messageInfo_UpetDjDeliveryDto_OrderExpress.Size(m)
}
func (m *UpetDjDeliveryDto_OrderExpress) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjDeliveryDto_OrderExpress.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjDeliveryDto_OrderExpress proto.InternalMessageInfo

func (m *UpetDjDeliveryDto_OrderExpress) GetExpressNo() string {
	if m != nil {
		return m.ExpressNo
	}
	return ""
}

func (m *UpetDjDeliveryDto_OrderExpress) GetNum() int32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UpetDjDeliveryDto_OrderExpress) GetExpressName() string {
	if m != nil {
		return m.ExpressName
	}
	return ""
}

// 退款单汇总信息
type UpetDjRefundDto struct {
	// 最近退款单号
	RefundNo string `protobuf:"bytes,1,opt,name=refundNo,proto3" json:"refundNo"`
	// 总退款金额
	RefundMoney float64 `protobuf:"fixed64,2,opt,name=refundMoney,proto3" json:"refundMoney"`
	// 退款单状态 1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
	RefundState          int32    `protobuf:"varint,3,opt,name=refundState,proto3" json:"refundState"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjRefundDto) Reset()         { *m = UpetDjRefundDto{} }
func (m *UpetDjRefundDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjRefundDto) ProtoMessage()    {}
func (*UpetDjRefundDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{14}
}

func (m *UpetDjRefundDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjRefundDto.Unmarshal(m, b)
}
func (m *UpetDjRefundDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjRefundDto.Marshal(b, m, deterministic)
}
func (m *UpetDjRefundDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjRefundDto.Merge(m, src)
}
func (m *UpetDjRefundDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjRefundDto.Size(m)
}
func (m *UpetDjRefundDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjRefundDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjRefundDto proto.InternalMessageInfo

func (m *UpetDjRefundDto) GetRefundNo() string {
	if m != nil {
		return m.RefundNo
	}
	return ""
}

func (m *UpetDjRefundDto) GetRefundMoney() float64 {
	if m != nil {
		return m.RefundMoney
	}
	return 0
}

func (m *UpetDjRefundDto) GetRefundState() int32 {
	if m != nil {
		return m.RefundState
	}
	return 0
}

// 退款商品列表
type UpetDjRefundProductDto struct {
	// 退款商品Id
	ProductId string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId"`
	// 最终退款数量
	RefundCount          int32    `protobuf:"varint,2,opt,name=refundCount,proto3" json:"refundCount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjRefundProductDto) Reset()         { *m = UpetDjRefundProductDto{} }
func (m *UpetDjRefundProductDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjRefundProductDto) ProtoMessage()    {}
func (*UpetDjRefundProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{15}
}

func (m *UpetDjRefundProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjRefundProductDto.Unmarshal(m, b)
}
func (m *UpetDjRefundProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjRefundProductDto.Marshal(b, m, deterministic)
}
func (m *UpetDjRefundProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjRefundProductDto.Merge(m, src)
}
func (m *UpetDjRefundProductDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjRefundProductDto.Size(m)
}
func (m *UpetDjRefundProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjRefundProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjRefundProductDto proto.InternalMessageInfo

func (m *UpetDjRefundProductDto) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UpetDjRefundProductDto) GetRefundCount() int32 {
	if m != nil {
		return m.RefundCount
	}
	return 0
}

// 配送节点信息
type UpetDjDeliverNodesDto struct {
	// 节点说明
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message"`
	// 节点日期
	CreateTime           string   `protobuf:"bytes,2,opt,name=createTime,proto3" json:"createTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjDeliverNodesDto) Reset()         { *m = UpetDjDeliverNodesDto{} }
func (m *UpetDjDeliverNodesDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjDeliverNodesDto) ProtoMessage()    {}
func (*UpetDjDeliverNodesDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{16}
}

func (m *UpetDjDeliverNodesDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjDeliverNodesDto.Unmarshal(m, b)
}
func (m *UpetDjDeliverNodesDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjDeliverNodesDto.Marshal(b, m, deterministic)
}
func (m *UpetDjDeliverNodesDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjDeliverNodesDto.Merge(m, src)
}
func (m *UpetDjDeliverNodesDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjDeliverNodesDto.Size(m)
}
func (m *UpetDjDeliverNodesDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjDeliverNodesDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjDeliverNodesDto proto.InternalMessageInfo

func (m *UpetDjDeliverNodesDto) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpetDjDeliverNodesDto) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

// 阿闻订单商品数据模型
type UpetDjOrderProductDto struct {
	//商品Id
	ProductId string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId"`
	// 商品名称
	ProductName string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	// 商品店铺单价
	ProductPrice float64 `protobuf:"fixed64,3,opt,name=productPrice,proto3" json:"productPrice"`
	// 商品实际单价
	ProductActualPrice float64 `protobuf:"fixed64,4,opt,name=productActualPrice,proto3" json:"productActualPrice"`
	// 数量
	ProductCount int32 `protobuf:"varint,5,opt,name=productCount,proto3" json:"productCount"`
	// 商品Logo
	ProductPic string `protobuf:"bytes,6,opt,name=productPic,proto3" json:"productPic"`
	// 规格
	ProductSpecifica string `protobuf:"bytes,7,opt,name=productSpecifica,proto3" json:"productSpecifica"`
	// 总实收
	ProductActaulMoney float64 `protobuf:"fixed64,8,opt,name=productActaulMoney,proto3" json:"productActaulMoney"`
	// 分类
	ProductCategoryId string `protobuf:"bytes,9,opt,name=productCategoryId,proto3" json:"productCategoryId"`
	// 参与限时折扣的活动id
	PromotionId int32 `protobuf:"varint,10,opt,name=promotionId,proto3" json:"promotionId"`
	// 商品类别1-实物商品2-虚拟商品3-组合商品
	ProductType int32 `protobuf:"varint,11,opt,name=productType,proto3" json:"productType"`
	//核销码列表
	VerifyCodeList []*UpetDjProductVerifyCodeDto `protobuf:"bytes,12,rep,name=verifyCodeList,proto3" json:"verifyCodeList"`
	//skuid
	SkuId string `protobuf:"bytes,13,opt,name=skuId,proto3" json:"skuId"`
	//上级skuid
	ParentSkuId string `protobuf:"bytes,14,opt,name=parentSkuId,proto3" json:"parentSkuId"`
	//上级productid
	ParentProductId string `protobuf:"bytes,15,opt,name=parentProductId,proto3" json:"parentProductId"`
	// 是否处方药
	IsPrescribedDrug int32 `protobuf:"varint,17,opt,name=is_prescribed_drug,json=isPrescribedDrug,proto3" json:"is_prescribed_drug"`
	//子商品列表
	ChildProductList     []*UpetDjOrderProductDto `protobuf:"bytes,16,rep,name=childProductList,proto3" json:"childProductList"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UpetDjOrderProductDto) Reset()         { *m = UpetDjOrderProductDto{} }
func (m *UpetDjOrderProductDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderProductDto) ProtoMessage()    {}
func (*UpetDjOrderProductDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{17}
}

func (m *UpetDjOrderProductDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderProductDto.Unmarshal(m, b)
}
func (m *UpetDjOrderProductDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderProductDto.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderProductDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderProductDto.Merge(m, src)
}
func (m *UpetDjOrderProductDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderProductDto.Size(m)
}
func (m *UpetDjOrderProductDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderProductDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderProductDto proto.InternalMessageInfo

func (m *UpetDjOrderProductDto) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetProductPrice() float64 {
	if m != nil {
		return m.ProductPrice
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetProductActualPrice() float64 {
	if m != nil {
		return m.ProductActualPrice
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetProductCount() int32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetProductPic() string {
	if m != nil {
		return m.ProductPic
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetProductSpecifica() string {
	if m != nil {
		return m.ProductSpecifica
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetProductActaulMoney() float64 {
	if m != nil {
		return m.ProductActaulMoney
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetProductCategoryId() string {
	if m != nil {
		return m.ProductCategoryId
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetProductType() int32 {
	if m != nil {
		return m.ProductType
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetVerifyCodeList() []*UpetDjProductVerifyCodeDto {
	if m != nil {
		return m.VerifyCodeList
	}
	return nil
}

func (m *UpetDjOrderProductDto) GetSkuId() string {
	if m != nil {
		return m.SkuId
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetParentSkuId() string {
	if m != nil {
		return m.ParentSkuId
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetParentProductId() string {
	if m != nil {
		return m.ParentProductId
	}
	return ""
}

func (m *UpetDjOrderProductDto) GetIsPrescribedDrug() int32 {
	if m != nil {
		return m.IsPrescribedDrug
	}
	return 0
}

func (m *UpetDjOrderProductDto) GetChildProductList() []*UpetDjOrderProductDto {
	if m != nil {
		return m.ChildProductList
	}
	return nil
}

// 阿闻订单虚拟商品核销码模型
type UpetDjProductVerifyCodeDto struct {
	// 核销Id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 商品Id
	ProductId string `protobuf:"bytes,2,opt,name=productId,proto3" json:"productId"`
	// 所属子订单号
	OrderSn string `protobuf:"bytes,3,opt,name=orderSn,proto3" json:"orderSn"`
	// 核销码
	VerifyCode string `protobuf:"bytes,4,opt,name=verifyCode,proto3" json:"verifyCode"`
	// 核销码有效期
	VerifyCodeExpiryDate string `protobuf:"bytes,5,opt,name=verifyCodeExpiryDate,proto3" json:"verifyCodeExpiryDate"`
	// 核销码状态 0未核销，1已核销，2已退款
	VerifyStatus int32 `protobuf:"varint,6,opt,name=verifyStatus,proto3" json:"verifyStatus"`
	// 核销时间
	VerifyTime string `protobuf:"bytes,7,opt,name=verifyTime,proto3" json:"verifyTime"`
	// 核销地点财务编码
	VerifyShop string `protobuf:"bytes,8,opt,name=verifyShop,proto3" json:"verifyShop"`
	// 创建时间
	CreateTime string `protobuf:"bytes,9,opt,name=createTime,proto3" json:"createTime"`
	// 最后更新时间
	UpdateTime           string   `protobuf:"bytes,10,opt,name=updateTime,proto3" json:"updateTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjProductVerifyCodeDto) Reset()         { *m = UpetDjProductVerifyCodeDto{} }
func (m *UpetDjProductVerifyCodeDto) String() string { return proto.CompactTextString(m) }
func (*UpetDjProductVerifyCodeDto) ProtoMessage()    {}
func (*UpetDjProductVerifyCodeDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{18}
}

func (m *UpetDjProductVerifyCodeDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjProductVerifyCodeDto.Unmarshal(m, b)
}
func (m *UpetDjProductVerifyCodeDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjProductVerifyCodeDto.Marshal(b, m, deterministic)
}
func (m *UpetDjProductVerifyCodeDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjProductVerifyCodeDto.Merge(m, src)
}
func (m *UpetDjProductVerifyCodeDto) XXX_Size() int {
	return xxx_messageInfo_UpetDjProductVerifyCodeDto.Size(m)
}
func (m *UpetDjProductVerifyCodeDto) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjProductVerifyCodeDto.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjProductVerifyCodeDto proto.InternalMessageInfo

func (m *UpetDjProductVerifyCodeDto) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpetDjProductVerifyCodeDto) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetVerifyCodeExpiryDate() string {
	if m != nil {
		return m.VerifyCodeExpiryDate
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetVerifyStatus() int32 {
	if m != nil {
		return m.VerifyStatus
	}
	return 0
}

func (m *UpetDjProductVerifyCodeDto) GetVerifyTime() string {
	if m != nil {
		return m.VerifyTime
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetVerifyShop() string {
	if m != nil {
		return m.VerifyShop
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *UpetDjProductVerifyCodeDto) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

// 阿闻到家订单查询数据响应
type UpetDjOrderQueryResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,3,opt,name=code,proto3" json:"code"`
	//总订单数据
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	// 当前页数据
	Data []*UpetDjOrderDto `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	// 是否可以加载更多
	HasMore              bool     `protobuf:"varint,4,opt,name=hasMore,proto3" json:"hasMore"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderQueryResponse) Reset()         { *m = UpetDjOrderQueryResponse{} }
func (m *UpetDjOrderQueryResponse) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderQueryResponse) ProtoMessage()    {}
func (*UpetDjOrderQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{19}
}

func (m *UpetDjOrderQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderQueryResponse.Unmarshal(m, b)
}
func (m *UpetDjOrderQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderQueryResponse.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderQueryResponse.Merge(m, src)
}
func (m *UpetDjOrderQueryResponse) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderQueryResponse.Size(m)
}
func (m *UpetDjOrderQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderQueryResponse proto.InternalMessageInfo

func (m *UpetDjOrderQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpetDjOrderQueryResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *UpetDjOrderQueryResponse) GetData() []*UpetDjOrderDto {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *UpetDjOrderQueryResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

// 阿闻到家订单查询明细数据响应
type UpetDjOrderDetailResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 剩余秒数
	RemainSeconds int32 `protobuf:"varint,7,opt,name=remainSeconds,proto3" json:"remainSeconds"`
	// 订单信息
	OrderListInfo *UpetDjOrderDto `protobuf:"bytes,3,opt,name=orderListInfo,proto3" json:"orderListInfo"`
	// 配送信息
	DeliveryInfo *UpetDjDeliveryDto `protobuf:"bytes,4,opt,name=deliveryInfo,proto3" json:"deliveryInfo"`
	// 退款信息
	RefundInfo *UpetDjRefundDto `protobuf:"bytes,5,opt,name=refundInfo,proto3" json:"refundInfo"`
	// 退款商品列表
	RefundProductList    []*UpetDjRefundProductDto `protobuf:"bytes,6,rep,name=refundProductList,proto3" json:"refundProductList"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpetDjOrderDetailResponse) Reset()         { *m = UpetDjOrderDetailResponse{} }
func (m *UpetDjOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderDetailResponse) ProtoMessage()    {}
func (*UpetDjOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{20}
}

func (m *UpetDjOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderDetailResponse.Unmarshal(m, b)
}
func (m *UpetDjOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderDetailResponse.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderDetailResponse.Merge(m, src)
}
func (m *UpetDjOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderDetailResponse.Size(m)
}
func (m *UpetDjOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderDetailResponse proto.InternalMessageInfo

func (m *UpetDjOrderDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpetDjOrderDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpetDjOrderDetailResponse) GetRemainSeconds() int32 {
	if m != nil {
		return m.RemainSeconds
	}
	return 0
}

func (m *UpetDjOrderDetailResponse) GetOrderListInfo() *UpetDjOrderDto {
	if m != nil {
		return m.OrderListInfo
	}
	return nil
}

func (m *UpetDjOrderDetailResponse) GetDeliveryInfo() *UpetDjDeliveryDto {
	if m != nil {
		return m.DeliveryInfo
	}
	return nil
}

func (m *UpetDjOrderDetailResponse) GetRefundInfo() *UpetDjRefundDto {
	if m != nil {
		return m.RefundInfo
	}
	return nil
}

func (m *UpetDjOrderDetailResponse) GetRefundProductList() []*UpetDjRefundProductDto {
	if m != nil {
		return m.RefundProductList
	}
	return nil
}

// 查询订单是否需要自动打印请求
type UpetDjOrderIsAutoPrintQueryRequest struct {
	// 订单号码
	OrderSn              string   `protobuf:"bytes,1,opt,name=orderSn,proto3" json:"orderSn"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderIsAutoPrintQueryRequest) Reset()         { *m = UpetDjOrderIsAutoPrintQueryRequest{} }
func (m *UpetDjOrderIsAutoPrintQueryRequest) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderIsAutoPrintQueryRequest) ProtoMessage()    {}
func (*UpetDjOrderIsAutoPrintQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{21}
}

func (m *UpetDjOrderIsAutoPrintQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest.Unmarshal(m, b)
}
func (m *UpetDjOrderIsAutoPrintQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderIsAutoPrintQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest.Merge(m, src)
}
func (m *UpetDjOrderIsAutoPrintQueryRequest) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest.Size(m)
}
func (m *UpetDjOrderIsAutoPrintQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderIsAutoPrintQueryRequest proto.InternalMessageInfo

func (m *UpetDjOrderIsAutoPrintQueryRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

// 查询订单是否需要自动打印响应
type UpetDjOrderIsAutoPrintQueryResponse struct {
	// 代码 非 200 取 message 错误信息
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 是否自动打印
	IsAutoPrint          bool     `protobuf:"varint,3,opt,name=isAutoPrint,proto3" json:"isAutoPrint"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpetDjOrderIsAutoPrintQueryResponse) Reset()         { *m = UpetDjOrderIsAutoPrintQueryResponse{} }
func (m *UpetDjOrderIsAutoPrintQueryResponse) String() string { return proto.CompactTextString(m) }
func (*UpetDjOrderIsAutoPrintQueryResponse) ProtoMessage()    {}
func (*UpetDjOrderIsAutoPrintQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{22}
}

func (m *UpetDjOrderIsAutoPrintQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse.Unmarshal(m, b)
}
func (m *UpetDjOrderIsAutoPrintQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse.Marshal(b, m, deterministic)
}
func (m *UpetDjOrderIsAutoPrintQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse.Merge(m, src)
}
func (m *UpetDjOrderIsAutoPrintQueryResponse) XXX_Size() int {
	return xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse.Size(m)
}
func (m *UpetDjOrderIsAutoPrintQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpetDjOrderIsAutoPrintQueryResponse proto.InternalMessageInfo

func (m *UpetDjOrderIsAutoPrintQueryResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpetDjOrderIsAutoPrintQueryResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *UpetDjOrderIsAutoPrintQueryResponse) GetIsAutoPrint() bool {
	if m != nil {
		return m.IsAutoPrint
	}
	return false
}

// 促销活动的报表查询
type PromotonOrderReportRequest struct {
	// 促销活动Id
	PromotionId int32 `protobuf:"varint,1,opt,name=promotionId,proto3" json:"promotionId"`
	// 统计开始日期
	StartDate string `protobuf:"bytes,2,opt,name=startDate,proto3" json:"startDate"`
	// 统计截止日期
	EndDate              string   `protobuf:"bytes,3,opt,name=endDate,proto3" json:"endDate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotonOrderReportRequest) Reset()         { *m = PromotonOrderReportRequest{} }
func (m *PromotonOrderReportRequest) String() string { return proto.CompactTextString(m) }
func (*PromotonOrderReportRequest) ProtoMessage()    {}
func (*PromotonOrderReportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{23}
}

func (m *PromotonOrderReportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotonOrderReportRequest.Unmarshal(m, b)
}
func (m *PromotonOrderReportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotonOrderReportRequest.Marshal(b, m, deterministic)
}
func (m *PromotonOrderReportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotonOrderReportRequest.Merge(m, src)
}
func (m *PromotonOrderReportRequest) XXX_Size() int {
	return xxx_messageInfo_PromotonOrderReportRequest.Size(m)
}
func (m *PromotonOrderReportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotonOrderReportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotonOrderReportRequest proto.InternalMessageInfo

func (m *PromotonOrderReportRequest) GetPromotionId() int32 {
	if m != nil {
		return m.PromotionId
	}
	return 0
}

func (m *PromotonOrderReportRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *PromotonOrderReportRequest) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

// 促销活动的报表信息
type PromotionOrderReportReponse struct {
	//昨天订单数量
	OrderCountYesterday int32 `protobuf:"varint,1,opt,name=orderCountYesterday,proto3" json:"orderCountYesterday"`
	//昨天订单流水
	OrderMoneyYesterday float64 `protobuf:"fixed64,2,opt,name=orderMoneyYesterday,proto3" json:"orderMoneyYesterday"`
	// 全部活动订单数量
	OrderCountTotal int64 `protobuf:"varint,3,opt,name=orderCountTotal,proto3" json:"orderCountTotal"`
	// 全部活动订单流水
	OrderMoneyTotal float64 `protobuf:"fixed64,4,opt,name=orderMoneyTotal,proto3" json:"orderMoneyTotal"`
	//订单列表
	OrderList            []*PromotionOrderReportWithDateListDto `protobuf:"bytes,9,rep,name=orderList,proto3" json:"orderList"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *PromotionOrderReportReponse) Reset()         { *m = PromotionOrderReportReponse{} }
func (m *PromotionOrderReportReponse) String() string { return proto.CompactTextString(m) }
func (*PromotionOrderReportReponse) ProtoMessage()    {}
func (*PromotionOrderReportReponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{24}
}

func (m *PromotionOrderReportReponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionOrderReportReponse.Unmarshal(m, b)
}
func (m *PromotionOrderReportReponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionOrderReportReponse.Marshal(b, m, deterministic)
}
func (m *PromotionOrderReportReponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionOrderReportReponse.Merge(m, src)
}
func (m *PromotionOrderReportReponse) XXX_Size() int {
	return xxx_messageInfo_PromotionOrderReportReponse.Size(m)
}
func (m *PromotionOrderReportReponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionOrderReportReponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionOrderReportReponse proto.InternalMessageInfo

func (m *PromotionOrderReportReponse) GetOrderCountYesterday() int32 {
	if m != nil {
		return m.OrderCountYesterday
	}
	return 0
}

func (m *PromotionOrderReportReponse) GetOrderMoneyYesterday() float64 {
	if m != nil {
		return m.OrderMoneyYesterday
	}
	return 0
}

func (m *PromotionOrderReportReponse) GetOrderCountTotal() int64 {
	if m != nil {
		return m.OrderCountTotal
	}
	return 0
}

func (m *PromotionOrderReportReponse) GetOrderMoneyTotal() float64 {
	if m != nil {
		return m.OrderMoneyTotal
	}
	return 0
}

func (m *PromotionOrderReportReponse) GetOrderList() []*PromotionOrderReportWithDateListDto {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 查询当前用户的每天的活动订单信息
type PromotionOrderReportWithDateListDto struct {
	// 统计日期
	CalcDate string `protobuf:"bytes,1,opt,name=calcDate,proto3" json:"calcDate"`
	// 订单数量
	OrderCount int32 `protobuf:"varint,2,opt,name=orderCount,proto3" json:"orderCount"`
	// 订单流水
	OrderMoney           float64  `protobuf:"fixed64,3,opt,name=orderMoney,proto3" json:"orderMoney"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionOrderReportWithDateListDto) Reset()         { *m = PromotionOrderReportWithDateListDto{} }
func (m *PromotionOrderReportWithDateListDto) String() string { return proto.CompactTextString(m) }
func (*PromotionOrderReportWithDateListDto) ProtoMessage()    {}
func (*PromotionOrderReportWithDateListDto) Descriptor() ([]byte, []int) {
	return fileDescriptor_33ed645cab6e1da2, []int{25}
}

func (m *PromotionOrderReportWithDateListDto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PromotionOrderReportWithDateListDto.Unmarshal(m, b)
}
func (m *PromotionOrderReportWithDateListDto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PromotionOrderReportWithDateListDto.Marshal(b, m, deterministic)
}
func (m *PromotionOrderReportWithDateListDto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionOrderReportWithDateListDto.Merge(m, src)
}
func (m *PromotionOrderReportWithDateListDto) XXX_Size() int {
	return xxx_messageInfo_PromotionOrderReportWithDateListDto.Size(m)
}
func (m *PromotionOrderReportWithDateListDto) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionOrderReportWithDateListDto.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionOrderReportWithDateListDto proto.InternalMessageInfo

func (m *PromotionOrderReportWithDateListDto) GetCalcDate() string {
	if m != nil {
		return m.CalcDate
	}
	return ""
}

func (m *PromotionOrderReportWithDateListDto) GetOrderCount() int32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *PromotionOrderReportWithDateListDto) GetOrderMoney() float64 {
	if m != nil {
		return m.OrderMoney
	}
	return 0
}

func init() {
	proto.RegisterEnum("oc.UpetDjOrderState", UpetDjOrderState_name, UpetDjOrderState_value)
	proto.RegisterType((*SectionQueryRequest)(nil), "oc.SectionQueryRequest")
	proto.RegisterType((*SectionDto)(nil), "oc.SectionDto")
	proto.RegisterType((*SectionQueryResponse)(nil), "oc.SectionQueryResponse")
	proto.RegisterType((*UpetDjMoneyCalcRequest)(nil), "oc.UpetDjMoneyCalcRequest")
	proto.RegisterType((*UpetDjMoneyCalcProductDto)(nil), "oc.UpetDjMoneyCalcProductDto")
	proto.RegisterType((*UpetDjMoneyDto)(nil), "oc.UpetDjMoneyDto")
	proto.RegisterType((*UpetDjMoneyResponse)(nil), "oc.UpetDjMoneyResponse")
	proto.RegisterType((*UpetDjConfirmRequest)(nil), "oc.UpetDjConfirmRequest")
	proto.RegisterType((*UpetDjConfirmResponse)(nil), "oc.UpetDjConfirmResponse")
	proto.RegisterType((*BadRequestResponse)(nil), "oc.BadRequestResponse")
	proto.RegisterType((*UpetDjOrderQueryRequest)(nil), "oc.UpetDjOrderQueryRequest")
	proto.RegisterType((*UpetDjOrderDetailRequest)(nil), "oc.UpetDjOrderDetailRequest")
	proto.RegisterType((*UpetDjOrderDto)(nil), "oc.UpetDjOrderDto")
	proto.RegisterType((*UpetDjOrderDto_GroupInfo)(nil), "oc.UpetDjOrderDto.GroupInfo")
	proto.RegisterType((*UpetDjDeliveryDto)(nil), "oc.UpetDjDeliveryDto")
	proto.RegisterType((*UpetDjDeliveryDto_OrderExpress)(nil), "oc.UpetDjDeliveryDto.OrderExpress")
	proto.RegisterType((*UpetDjRefundDto)(nil), "oc.UpetDjRefundDto")
	proto.RegisterType((*UpetDjRefundProductDto)(nil), "oc.UpetDjRefundProductDto")
	proto.RegisterType((*UpetDjDeliverNodesDto)(nil), "oc.UpetDjDeliverNodesDto")
	proto.RegisterType((*UpetDjOrderProductDto)(nil), "oc.UpetDjOrderProductDto")
	proto.RegisterType((*UpetDjProductVerifyCodeDto)(nil), "oc.UpetDjProductVerifyCodeDto")
	proto.RegisterType((*UpetDjOrderQueryResponse)(nil), "oc.UpetDjOrderQueryResponse")
	proto.RegisterType((*UpetDjOrderDetailResponse)(nil), "oc.UpetDjOrderDetailResponse")
	proto.RegisterType((*UpetDjOrderIsAutoPrintQueryRequest)(nil), "oc.UpetDjOrderIsAutoPrintQueryRequest")
	proto.RegisterType((*UpetDjOrderIsAutoPrintQueryResponse)(nil), "oc.UpetDjOrderIsAutoPrintQueryResponse")
	proto.RegisterType((*PromotonOrderReportRequest)(nil), "oc.PromotonOrderReportRequest")
	proto.RegisterType((*PromotionOrderReportReponse)(nil), "oc.PromotionOrderReportReponse")
	proto.RegisterType((*PromotionOrderReportWithDateListDto)(nil), "oc.PromotionOrderReportWithDateListDto")
}

func init() { proto.RegisterFile("oc/upet_dj_service.proto", fileDescriptor_33ed645cab6e1da2) }

var fileDescriptor_33ed645cab6e1da2 = []byte{
	// 2766 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x59, 0x4f, 0x73, 0xdc, 0xc6,
	0xb1, 0x7f, 0xbb, 0xe4, 0x8a, 0xdc, 0x26, 0xb9, 0x84, 0x86, 0xa4, 0x08, 0x2d, 0x29, 0x9a, 0x86,
	0x6d, 0x59, 0x96, 0x5d, 0xb4, 0x9e, 0x7c, 0x78, 0xf6, 0xf3, 0xab, 0x57, 0x25, 0x71, 0x65, 0x87,
	0x15, 0x8b, 0xa6, 0xb1, 0x92, 0x65, 0x57, 0x0e, 0x5b, 0x43, 0x60, 0xb8, 0x1c, 0x13, 0x0b, 0xc0,
	0xf8, 0xa3, 0x78, 0x73, 0xf3, 0x25, 0xc7, 0x54, 0xaa, 0x92, 0x63, 0x3e, 0x43, 0xee, 0xf9, 0x10,
	0x39, 0xe6, 0xe6, 0x5b, 0xbe, 0x48, 0x6a, 0xba, 0x67, 0x80, 0x01, 0xb8, 0xb2, 0x5c, 0xbe, 0xa1,
	0x7f, 0x3d, 0xdd, 0xd3, 0xd3, 0xd3, 0xd3, 0xdd, 0x33, 0x00, 0x37, 0x09, 0x3e, 0x2c, 0x53, 0x51,
	0x4c, 0xc2, 0xef, 0x26, 0xb9, 0xc8, 0x5e, 0xca, 0x40, 0x1c, 0xa5, 0x59, 0x52, 0x24, 0xac, 0x9b,
	0x04, 0xde, 0x6f, 0x61, 0x6b, 0x2c, 0x82, 0x42, 0x26, 0xf1, 0x57, 0xa5, 0xc8, 0xe6, 0xbe, 0xf8,
	0xbe, 0x14, 0x79, 0xc1, 0x86, 0xb0, 0x9a, 0xf2, 0x4c, 0xc4, 0xc5, 0x49, 0xe8, 0x76, 0x0e, 0x3b,
	0xf7, 0x7a, 0x7e, 0x45, 0x33, 0x17, 0x56, 0x64, 0x7e, 0x7c, 0x29, 0xa3, 0xd0, 0xed, 0x1e, 0x76,
	0xee, 0xad, 0xfa, 0x86, 0xf4, 0xfe, 0xda, 0x01, 0xd0, 0xda, 0x46, 0x45, 0xc2, 0x06, 0xd0, 0x95,
	0x34, 0xa6, 0xe7, 0x77, 0x65, 0xf8, 0xb3, 0x4a, 0x0f, 0x61, 0x2d, 0x27, 0xc9, 0x53, 0x3e, 0x13,
	0xee, 0xd2, 0x61, 0xe7, 0x5e, 0xdf, 0xb7, 0x21, 0xf6, 0x31, 0x6c, 0x06, 0x6a, 0x96, 0x7a, 0x02,
	0x77, 0xf9, 0x70, 0xe9, 0xde, 0xda, 0xc3, 0xc1, 0x51, 0x12, 0x1c, 0xd5, 0xa8, 0xdf, 0x1e, 0xe6,
	0x9d, 0xc2, 0x76, 0x73, 0x8d, 0x79, 0x9a, 0xc4, 0xb9, 0x60, 0x0c, 0x96, 0x83, 0x24, 0x14, 0xda,
	0x42, 0xfc, 0x66, 0x1e, 0x2c, 0x87, 0xbc, 0xe0, 0x6e, 0x67, 0xa1, 0x6a, 0xe4, 0x79, 0xff, 0xea,
	0xc0, 0xad, 0xe7, 0xa9, 0x28, 0x46, 0xdf, 0x3d, 0x4d, 0x62, 0x31, 0x3f, 0xe6, 0x51, 0x60, 0xfc,
	0x76, 0x0b, 0x6e, 0xe4, 0x97, 0x49, 0xaa, 0x17, 0xd8, 0xf7, 0x35, 0xc5, 0x3c, 0x58, 0x0f, 0x45,
	0x5e, 0xc8, 0x98, 0x2b, 0x55, 0xdf, 0xe0, 0xfa, 0x3a, 0x7e, 0x03, 0x6b, 0x8d, 0xf9, 0xd6, 0x5d,
	0xbe, 0x36, 0xe6, 0x5b, 0xe5, 0xa6, 0x22, 0x29, 0x78, 0xf4, 0x42, 0xc8, 0xe9, 0x65, 0xe1, 0xf6,
	0xd0, 0x72, 0x1b, 0x62, 0x9f, 0xc0, 0x6a, 0x9a, 0x25, 0x61, 0x19, 0x14, 0xb9, 0x7b, 0x03, 0x17,
	0x71, 0x47, 0x2d, 0xa2, 0x65, 0xef, 0x19, 0x0d, 0x51, 0x6b, 0xaa, 0x86, 0x7b, 0x9f, 0xc3, 0xed,
	0x57, 0x0e, 0x63, 0xdb, 0xd0, 0x0b, 0x92, 0x32, 0x2e, 0xf4, 0xce, 0x11, 0xa1, 0xd0, 0xfc, 0xaa,
	0x3c, 0xa1, 0x5d, 0xee, 0xfb, 0x44, 0x78, 0x7f, 0xef, 0xc0, 0xc0, 0xd2, 0xa4, 0xc4, 0x0f, 0x61,
	0xad, 0xac, 0x11, 0x54, 0xd2, 0xf5, 0x6d, 0x88, 0xbd, 0x0d, 0x1b, 0x44, 0x8e, 0x45, 0x90, 0xc4,
	0x61, 0x8e, 0x2a, 0xbb, 0x7e, 0x13, 0x6c, 0x3b, 0x60, 0xe9, 0xba, 0x03, 0x1e, 0xc2, 0xb6, 0xa5,
	0xf6, 0xf1, 0xfc, 0xa9, 0x8c, 0x9f, 0xc7, 0xd2, 0xf8, 0x6a, 0x21, 0xcf, 0x93, 0xb0, 0x65, 0xd9,
	0xfb, 0xb3, 0x01, 0xe2, 0xc2, 0xca, 0x4c, 0xe4, 0x39, 0x9f, 0x9a, 0x20, 0x35, 0x24, 0x7b, 0x1b,
	0x96, 0xc2, 0x22, 0xc1, 0xa5, 0xad, 0x3d, 0x64, 0x2d, 0xa7, 0x2b, 0x4f, 0x2b, 0xb6, 0xf7, 0x00,
	0xb6, 0x09, 0x3e, 0x4e, 0xe2, 0x0b, 0x99, 0xcd, 0x4c, 0xe4, 0xb8, 0xb0, 0x92, 0x64, 0xa1, 0xc8,
	0xaa, 0xd0, 0x31, 0xa4, 0xf7, 0x04, 0x76, 0x5a, 0x12, 0x2d, 0xf3, 0x3a, 0x8b, 0xcd, 0xeb, 0x36,
	0xcc, 0xf3, 0x1e, 0x03, 0x7b, 0xcc, 0x43, 0x3d, 0xdd, 0xaf, 0xd4, 0xf1, 0xcf, 0x0e, 0xec, 0x92,
	0x2d, 0x5f, 0x2a, 0xe3, 0x1a, 0x29, 0x63, 0x1f, 0xfa, 0x29, 0x9f, 0x8a, 0x93, 0x38, 0x14, 0x3f,
	0x68, 0x75, 0x35, 0x40, 0x67, 0x7f, 0x2a, 0xc6, 0xf2, 0x0f, 0xc6, 0x9d, 0x15, 0x6d, 0x1d, 0x9a,
	0xa5, 0xc3, 0x25, 0xeb, 0xd0, 0x0c, 0x61, 0x75, 0x26, 0x66, 0xe7, 0xe8, 0x93, 0x65, 0x34, 0xa4,
	0xa2, 0xd9, 0x7d, 0xe8, 0xe5, 0x05, 0x2f, 0x04, 0x6e, 0xeb, 0xe0, 0xe1, 0x76, 0xed, 0x6e, 0xb4,
	0x6c, 0xac, 0x78, 0x3e, 0x0d, 0xa9, 0x5c, 0x3b, 0x8e, 0xdd, 0x1b, 0x96, 0x6b, 0xc7, 0xb1, 0x77,
	0x05, 0xae, 0x25, 0x34, 0x12, 0x05, 0x97, 0xd1, 0x6b, 0x37, 0xc4, 0xd6, 0xd7, 0x6d, 0xe8, 0x63,
	0x7b, 0xd0, 0x27, 0x0b, 0x27, 0x32, 0xd4, 0xe1, 0x51, 0x99, 0xec, 0xfd, 0x63, 0xdd, 0x9c, 0x0a,
	0x9a, 0xad, 0x48, 0x7e, 0xc1, 0x1c, 0xa7, 0x49, 0x63, 0x8e, 0xd3, 0x84, 0xdd, 0x85, 0x4d, 0xca,
	0x9a, 0x13, 0x44, 0x26, 0x79, 0xac, 0x67, 0xda, 0x20, 0xf8, 0x4b, 0x6d, 0x4b, 0xed, 0xd5, 0xe5,
	0x46, 0x2a, 0x1a, 0xc2, 0xaa, 0xfa, 0xc2, 0x34, 0xdb, 0x23, 0x13, 0x0d, 0xcd, 0x0e, 0x00, 0xd4,
	0xf7, 0xd3, 0xe4, 0x5c, 0x46, 0x42, 0x3b, 0xcb, 0x42, 0x6a, 0xaf, 0xaf, 0xbc, 0xde, 0xeb, 0x07,
	0x00, 0x78, 0x2c, 0xe9, 0xc0, 0xaf, 0xe2, 0x61, 0xb6, 0x10, 0x8c, 0x97, 0x4c, 0xbe, 0x94, 0x91,
	0x98, 0x0a, 0xb7, 0x8f, 0xb9, 0xae, 0x06, 0xd8, 0x5d, 0x18, 0x04, 0x99, 0xe0, 0x85, 0x18, 0xf1,
	0x42, 0x3c, 0x93, 0x33, 0xe1, 0x02, 0x5a, 0xd3, 0x42, 0x95, 0x9f, 0x52, 0x3e, 0x7f, 0xaa, 0x42,
	0x78, 0x0d, 0xc3, 0xca, 0x90, 0x4a, 0x7f, 0x26, 0x8a, 0x32, 0x8b, 0x5f, 0xf0, 0xb9, 0xbb, 0x4e,
	0xf1, 0x58, 0x01, 0xca, 0x0b, 0x44, 0x9c, 0x26, 0xee, 0x06, 0x79, 0xc1, 0xd0, 0x4a, 0x67, 0x26,
	0x66, 0x3c, 0xbb, 0xca, 0xdd, 0x01, 0xf9, 0x5e, 0x93, 0x2a, 0xfb, 0xd0, 0x28, 0x5c, 0xa9, 0xbb,
	0x49, 0xd9, 0xc7, 0x82, 0xd8, 0x3b, 0x30, 0x38, 0x2f, 0x73, 0x19, 0x8b, 0x3c, 0x9f, 0x14, 0x72,
	0x26, 0x72, 0xd7, 0xa1, 0xcd, 0x31, 0xa8, 0xb2, 0x3a, 0x67, 0x6f, 0xc0, 0x5a, 0x2a, 0x83, 0xab,
	0x32, 0x9d, 0xe0, 0xe9, 0xbb, 0x49, 0x9e, 0x26, 0xe8, 0x58, 0x59, 0xff, 0x26, 0xac, 0x2b, 0xbf,
	0x4f, 0x78, 0x18, 0x66, 0x22, 0xcf, 0x5d, 0xa6, 0x0b, 0xe2, 0x65, 0x92, 0x3e, 0x22, 0x88, 0x79,
	0xb0, 0x91, 0x17, 0x49, 0x70, 0x35, 0x29, 0x53, 0x9c, 0xca, 0xdd, 0x22, 0x73, 0x10, 0x7c, 0x9e,
	0xa2, 0x7b, 0x0e, 0x61, 0x5d, 0xe6, 0x13, 0x9e, 0xa6, 0xd1, 0x7c, 0x72, 0x5e, 0xc4, 0xee, 0x36,
	0x16, 0x6c, 0x90, 0xf9, 0x23, 0x05, 0x3d, 0x2e, 0x62, 0x76, 0x07, 0x40, 0xe6, 0x93, 0x97, 0x32,
	0x2b, 0x4a, 0x1e, 0xb9, 0x3b, 0xe4, 0x27, 0x99, 0x7f, 0x4d, 0x00, 0x7b, 0x0b, 0x36, 0x44, 0xcc,
	0xcf, 0x23, 0x31, 0x09, 0x78, 0x1c, 0x88, 0xc8, 0x75, 0x51, 0xc3, 0x3a, 0x81, 0xc7, 0x88, 0xb1,
	0x4f, 0x61, 0x4d, 0x17, 0x91, 0x2f, 0x64, 0x5e, 0xb8, 0xb7, 0xb0, 0xec, 0xdc, 0x6e, 0x05, 0x87,
	0x55, 0x72, 0xec, 0xd1, 0xec, 0x7f, 0x61, 0x80, 0x05, 0x1b, 0x07, 0xa1, 0xfc, 0x2e, 0xca, 0xb3,
	0x96, 0xbc, 0x12, 0x6c, 0x8d, 0x64, 0xff, 0x03, 0x6e, 0x9c, 0x4c, 0x42, 0x99, 0xa7, 0x11, 0x9f,
	0x4f, 0xf8, 0x45, 0x21, 0xb2, 0x49, 0xca, 0xe7, 0x33, 0x11, 0x17, 0xee, 0x6d, 0x5c, 0xca, 0x4e,
	0x9c, 0x8c, 0x88, 0xfd, 0x48, 0x71, 0xcf, 0x88, 0xc9, 0x76, 0xe0, 0x86, 0xcc, 0xd5, 0x50, 0x77,
	0x48, 0xe5, 0x4c, 0xe6, 0x67, 0x7c, 0xce, 0x3e, 0x05, 0x98, 0x66, 0x49, 0x99, 0x4e, 0x64, 0x7c,
	0x91, 0xb8, 0x7b, 0x98, 0xc9, 0xf7, 0xaf, 0xdb, 0x71, 0xf4, 0xb9, 0x1a, 0x74, 0x12, 0x5f, 0x24,
	0x7e, 0x7f, 0x6a, 0x3e, 0x95, 0x27, 0x83, 0x4b, 0x1e, 0xc7, 0x22, 0x52, 0xa7, 0x7f, 0x9f, 0x3c,
	0xa9, 0x91, 0x93, 0x90, 0xdd, 0x03, 0x27, 0x48, 0xe2, 0xbc, 0x8c, 0xac, 0x83, 0x7b, 0x47, 0xc7,
	0x34, 0xe1, 0xfa, 0xe4, 0x0e, 0xff, 0xbc, 0x0c, 0xfd, 0x6a, 0x06, 0x15, 0x09, 0x64, 0x93, 0x3a,
	0x56, 0x65, 0xae, 0x53, 0xeb, 0x1a, 0x62, 0x63, 0x84, 0xd4, 0x26, 0x65, 0x22, 0x10, 0xf2, 0xa5,
	0xc8, 0x26, 0xb1, 0x3a, 0xd7, 0x94, 0x32, 0xd6, 0x0d, 0x88, 0x67, 0xfb, 0x1d, 0x18, 0x54, 0x83,
	0xe8, 0x10, 0xeb, 0xb4, 0x61, 0x50, 0x0a, 0x60, 0x5b, 0x57, 0x20, 0x8b, 0xb9, 0xce, 0x1e, 0x95,
	0xae, 0x63, 0x59, 0xcc, 0xd9, 0xfb, 0x70, 0xb3, 0x1a, 0x14, 0xca, 0xbc, 0xc8, 0x64, 0x50, 0xe8,
	0x64, 0xe2, 0x18, 0xc6, 0x48, 0xe3, 0xec, 0x3d, 0xa8, 0xb0, 0x2a, 0x9c, 0x29, 0xb5, 0x6c, 0x1a,
	0xdc, 0x84, 0xf4, 0xbb, 0x50, 0x41, 0x93, 0x19, 0x25, 0xa1, 0x15, 0x72, 0x91, 0x81, 0x75, 0x22,
	0xaa, 0x9c, 0x12, 0x09, 0x1e, 0x8a, 0x0c, 0xd3, 0x8b, 0x71, 0xca, 0x17, 0x08, 0xa9, 0xed, 0xa0,
	0x21, 0xe8, 0x91, 0x3e, 0xaa, 0xa1, 0xdd, 0x42, 0x77, 0x54, 0x1a, 0xf4, 0x3c, 0x94, 0x5e, 0x48,
	0x83, 0x9e, 0xe4, 0x2e, 0x6c, 0x5e, 0xc8, 0x98, 0x47, 0x93, 0x82, 0x5f, 0x89, 0x49, 0x31, 0x4f,
	0x4d, 0x8e, 0xd9, 0x40, 0xf8, 0x19, 0xbf, 0x12, 0xcf, 0xe6, 0xa9, 0x60, 0xb7, 0x61, 0x55, 0x47,
	0x4d, 0xa8, 0x13, 0xcd, 0x0a, 0x45, 0x45, 0xa8, 0x66, 0x09, 0x45, 0x44, 0x7e, 0xe2, 0xf3, 0x5c,
	0xa7, 0x9a, 0x35, 0x8d, 0x8d, 0xf8, 0x1c, 0x37, 0x8f, 0xa4, 0x8d, 0x6f, 0x28, 0xe7, 0x90, 0x75,
	0xda, 0x31, 0xde, 0x9f, 0x56, 0xe1, 0x26, 0xc5, 0xe0, 0x88, 0x44, 0xb1, 0xa9, 0xf2, 0xa0, 0xda,
	0x16, 0x5c, 0x64, 0xa7, 0xb9, 0x55, 0x0a, 0x53, 0x6d, 0x95, 0xa1, 0x69, 0xd7, 0xbb, 0xcd, 0x5d,
	0xa7, 0x64, 0x6d, 0x69, 0xc2, 0x4d, 0x5f, 0x6a, 0x6a, 0x52, 0x18, 0xbb, 0x5f, 0xef, 0x63, 0xb5,
	0xe7, 0xcb, 0xcd, 0x3d, 0x37, 0x38, 0xbb, 0x57, 0x6f, 0xa4, 0x59, 0x56, 0xaf, 0xb9, 0xe5, 0x1a,
	0xb6, 0xed, 0x4b, 0x2f, 0x93, 0xd8, 0x54, 0x9d, 0x26, 0xa8, 0xca, 0x81, 0x01, 0x16, 0xc7, 0x05,
	0xa1, 0x2a, 0x75, 0x5f, 0x64, 0xd4, 0x1a, 0xae, 0x62, 0x49, 0x31, 0x24, 0x75, 0xd7, 0xe4, 0x3a,
	0x2c, 0x27, 0x14, 0x10, 0x0d, 0xac, 0x31, 0x46, 0xed, 0x36, 0xe0, 0x66, 0x36, 0x30, 0xe5, 0x05,
	0x9b, 0xc6, 0x32, 0xba, 0x43, 0x5e, 0x68, 0xe3, 0xaa, 0x04, 0x1a, 0xec, 0x24, 0xc4, 0x6a, 0xb1,
	0xe4, 0x5b, 0x88, 0xf2, 0x92, 0xa1, 0xbe, 0xd4, 0x6d, 0x00, 0x55, 0x8b, 0x36, 0xcc, 0x1e, 0xc0,
	0x96, 0x81, 0xc6, 0x74, 0x87, 0x53, 0x55, 0x42, 0x57, 0x85, 0x45, 0x2c, 0x55, 0xe0, 0x22, 0x5e,
	0xc8, 0xa2, 0xd4, 0x95, 0xb1, 0xe3, 0x57, 0xb4, 0x2a, 0x8d, 0x51, 0x12, 0x4f, 0x89, 0xb9, 0x4e,
	0xa5, 0xb7, 0x02, 0x94, 0x17, 0x54, 0x99, 0xf9, 0xc2, 0x48, 0x6f, 0xd0, 0x3d, 0xc4, 0xc6, 0xd4,
	0xae, 0x21, 0x5d, 0x69, 0x19, 0xe0, 0xa0, 0x26, 0xc8, 0x3e, 0x80, 0x9b, 0x54, 0xd2, 0x54, 0x6a,
	0x31, 0x57, 0xbb, 0x2d, 0xb4, 0xf9, 0x3a, 0x43, 0x35, 0xee, 0x0d, 0x50, 0xc7, 0x3e, 0xd6, 0xac,
	0xbe, 0xbf, 0x90, 0xc7, 0x3e, 0x84, 0x5e, 0x9c, 0x84, 0x22, 0x77, 0x6f, 0xb6, 0x6b, 0x8e, 0x3e,
	0x27, 0xa7, 0x8a, 0xab, 0x4a, 0x07, 0x8d, 0x63, 0xff, 0x07, 0x2b, 0xe2, 0x87, 0x14, 0xf5, 0x52,
	0x99, 0xf2, 0xae, 0x89, 0xa8, 0xa3, 0x75, 0x84, 0x8e, 0x7f, 0x42, 0x23, 0x7d, 0x23, 0xa2, 0x96,
	0x5d, 0x6f, 0xdf, 0xb8, 0xc8, 0xdc, 0x5d, 0x0a, 0xd6, 0x06, 0x38, 0x3c, 0x87, 0x75, 0x5b, 0x5c,
	0x65, 0x22, 0xad, 0x60, 0x12, 0x27, 0xfa, 0x90, 0xf6, 0x35, 0x72, 0x9a, 0x30, 0x07, 0x96, 0xe2,
	0x72, 0xa6, 0xbb, 0x62, 0xf5, 0xa9, 0xb2, 0x46, 0x25, 0x60, 0xdd, 0x86, 0x8d, 0x08, 0x9f, 0x09,
	0xef, 0x7b, 0xd8, 0x24, 0xa3, 0x7d, 0x71, 0x51, 0xc6, 0xa1, 0xca, 0x06, 0xd8, 0xd2, 0x28, 0xe2,
	0xd4, 0x4c, 0x52, 0xd1, 0xd4, 0xb8, 0xa8, 0x6f, 0xea, 0xc6, 0xba, 0xb8, 0x5b, 0x36, 0x54, 0x8f,
	0x18, 0x57, 0xb5, 0xa1, 0xe7, 0xdb, 0x90, 0xf7, 0x8d, 0xb9, 0xf5, 0xd2, 0x94, 0xd6, 0xdd, 0x10,
	0x5b, 0x39, 0xa4, 0xaa, 0x46, 0xb6, 0x06, 0x6a, 0xcd, 0xc7, 0x78, 0x7f, 0xec, 0xda, 0x9a, 0x11,
	0xf2, 0xbe, 0x32, 0x37, 0x9c, 0xd6, 0xa6, 0xd9, 0x37, 0x91, 0x4e, 0xf3, 0xb2, 0x75, 0x00, 0x40,
	0x9d, 0x20, 0x1e, 0x66, 0xca, 0x69, 0x16, 0xe2, 0xfd, 0xd4, 0x33, 0x3a, 0x5b, 0xcd, 0xc7, 0xeb,
	0x8d, 0xd5, 0xc4, 0x69, 0x5d, 0x48, 0x6d, 0x48, 0x1d, 0x0f, 0x4d, 0x9e, 0x65, 0x32, 0x10, 0xe6,
	0x2a, 0x6f, 0x63, 0xec, 0x08, 0x98, 0xa6, 0x1f, 0x05, 0xaa, 0x8d, 0xa2, 0x91, 0x74, 0xa1, 0x5f,
	0xc0, 0xb1, 0x74, 0x92, 0x8f, 0xe8, 0xae, 0xda, 0xc0, 0xd4, 0x8a, 0xcd, 0x1c, 0x32, 0x30, 0xbd,
	0x79, 0x8d, 0xa8, 0xc4, 0xa4, 0xa9, 0x71, 0x2a, 0x02, 0x79, 0x21, 0x03, 0xae, 0x93, 0xe4, 0x35,
	0xbc, 0x69, 0x1f, 0x2f, 0xad, 0x1e, 0xbd, 0x61, 0x9f, 0xe1, 0xe0, 0x41, 0xd6, 0xb6, 0xf0, 0x42,
	0x4c, 0x13, 0xcc, 0x67, 0x7d, 0x7d, 0x90, 0xdb, 0x0c, 0xed, 0xc3, 0x59, 0xa2, 0x0e, 0xea, 0x49,
	0xa8, 0xb3, 0xa8, 0x0d, 0x59, 0x5e, 0x7e, 0x56, 0x57, 0x55, 0x1b, 0x62, 0x9f, 0xc1, 0xe0, 0xa5,
	0xc8, 0xe4, 0xc5, 0x5c, 0x25, 0x33, 0xec, 0x0a, 0xd7, 0xf1, 0xb8, 0x1e, 0xd4, 0xc7, 0x55, 0xef,
	0xe9, 0xd7, 0xd5, 0x30, 0xec, 0x10, 0x9b, 0x52, 0xf5, 0x03, 0xc5, 0x86, 0xf5, 0x40, 0x81, 0xf3,
	0xe3, 0x65, 0x69, 0x8c, 0xbc, 0x81, 0xde, 0xe5, 0x1a, 0x52, 0xa9, 0x99, 0xc8, 0xb3, 0x2a, 0x56,
	0x36, 0x29, 0x35, 0xb7, 0x60, 0xf6, 0x01, 0x30, 0xd5, 0x4a, 0x66, 0x22, 0x0f, 0x32, 0x79, 0x2e,
	0xc2, 0x49, 0x98, 0x95, 0x53, 0xec, 0xe8, 0x7b, 0xbe, 0x23, 0xf3, 0xb3, 0x8a, 0x31, 0xca, 0xca,
	0x29, 0x7b, 0x02, 0x0e, 0xf6, 0xb0, 0x67, 0x56, 0xbf, 0xec, 0xbc, 0xae, 0x5f, 0xbe, 0x26, 0xe2,
	0xfd, 0xd4, 0x85, 0xe1, 0xab, 0xbd, 0xa0, 0x5f, 0xde, 0x3a, 0x58, 0x70, 0xba, 0x32, 0x6c, 0xc6,
	0x7c, 0xb7, 0x1d, 0xf3, 0xd6, 0x7d, 0x76, 0xa9, 0x79, 0x9f, 0x3d, 0x00, 0xa8, 0xfd, 0xa9, 0x8b,
	0xbd, 0x85, 0xa8, 0x94, 0x5d, 0x53, 0x4f, 0x7e, 0x48, 0x65, 0x36, 0x1f, 0x99, 0x4b, 0x79, 0xdf,
	0x5f, 0xc8, 0x53, 0xb1, 0x4e, 0x38, 0x35, 0xaf, 0x18, 0xc9, 0x3d, 0xbf, 0x81, 0xd5, 0xf3, 0xe2,
	0xe9, 0x5e, 0xb1, 0xe7, 0xc5, 0x42, 0x5d, 0xf1, 0xc7, 0x97, 0x49, 0x8a, 0x71, 0x5b, 0xf1, 0x15,
	0xd2, 0xca, 0x0e, 0xfd, 0x76, 0x76, 0x50, 0xfc, 0x32, 0x0d, 0x9b, 0x37, 0x4b, 0x0b, 0xf1, 0xfe,
	0xd8, 0x69, 0x3c, 0x0c, 0x2c, 0x7e, 0x36, 0x5c, 0xb2, 0x9e, 0x4c, 0xb6, 0xa1, 0x87, 0x57, 0x5b,
	0xed, 0x73, 0x22, 0xd8, 0x5d, 0xfd, 0x98, 0xd8, 0x7d, 0xe5, 0x85, 0x06, 0xf9, 0x6a, 0x03, 0x2e,
	0x79, 0xfe, 0x34, 0xc9, 0xc8, 0xc7, 0xab, 0xbe, 0x21, 0xbd, 0x7f, 0x77, 0xcd, 0x9b, 0x5c, 0xe3,
	0x85, 0xe2, 0xd7, 0x3c, 0xde, 0x50, 0xa7, 0x35, 0xe3, 0x32, 0x36, 0x0f, 0x6c, 0x2b, 0xd4, 0xcc,
	0x36, 0x40, 0xf6, 0x31, 0x6c, 0x24, 0xe6, 0x7e, 0xa5, 0xee, 0x1f, 0xb8, 0xcc, 0xc5, 0xc6, 0x37,
	0x07, 0xb2, 0x4f, 0xea, 0xee, 0x09, 0x05, 0x97, 0x51, 0x70, 0x67, 0x61, 0x7d, 0xf5, 0x1b, 0x43,
	0xd9, 0x47, 0x00, 0x54, 0x0f, 0x50, 0xb0, 0x87, 0x82, 0x5b, 0xb5, 0x60, 0x55, 0xe3, 0x7c, 0x6b,
	0x18, 0xfb, 0x8d, 0xba, 0x84, 0x58, 0x95, 0x08, 0xcf, 0x12, 0x3d, 0x79, 0x0e, 0xdb, 0xb2, 0xd6,
	0x61, 0xba, 0x2e, 0xe4, 0xfd, 0x3f, 0x78, 0xd6, 0xd2, 0x4e, 0xf2, 0x47, 0x65, 0x91, 0x9c, 0x65,
	0x32, 0x2e, 0x1a, 0x0f, 0x5c, 0xd6, 0x31, 0xe9, 0x34, 0x9f, 0x91, 0x4a, 0x78, 0xeb, 0x67, 0xe5,
	0x7f, 0xd5, 0x76, 0x1d, 0xc2, 0x9a, 0xac, 0x35, 0xe1, 0x36, 0xac, 0xfa, 0x36, 0xe4, 0xbd, 0x84,
	0xe1, 0x19, 0x26, 0xd5, 0x24, 0xc6, 0x89, 0x7d, 0x91, 0x26, 0x59, 0x61, 0xcc, 0x6d, 0x65, 0xe1,
	0xce, 0xf5, 0x2c, 0xbc, 0x0f, 0xfd, 0xbc, 0xe0, 0x59, 0x31, 0xaa, 0xaf, 0x05, 0x35, 0xa0, 0x2c,
	0x13, 0x71, 0x38, 0xaa, 0x2f, 0x8a, 0x86, 0xf4, 0xfe, 0xd6, 0x85, 0xbd, 0x33, 0xa3, 0xa7, 0x31,
	0x33, 0xad, 0xf3, 0x01, 0x6c, 0xa1, 0x67, 0xb0, 0x6e, 0x7d, 0x2b, 0xf2, 0x42, 0x64, 0x21, 0x9f,
	0x6b, 0x0b, 0x16, 0xb1, 0x2a, 0x09, 0xac, 0x36, 0xb5, 0x04, 0xb5, 0x29, 0x8b, 0x58, 0x2a, 0x3f,
	0xd7, 0x8a, 0x9e, 0xe1, 0xd1, 0x5b, 0xc2, 0xa3, 0xd7, 0x86, 0xab, 0x91, 0xa8, 0x80, 0x46, 0x52,
	0x21, 0x6e, 0xc3, 0xec, 0x09, 0xf4, 0xab, 0x88, 0x76, 0xfb, 0x18, 0x48, 0xef, 0xaa, 0x40, 0x5a,
	0xb4, 0xd6, 0x17, 0xb2, 0xb8, 0x54, 0xce, 0x50, 0x63, 0x55, 0x54, 0xd5, 0x92, 0xde, 0x8f, 0x1d,
	0x78, 0xeb, 0x17, 0x88, 0xa8, 0x7e, 0x2d, 0xe0, 0x51, 0x80, 0x1e, 0xd6, 0xfd, 0x9a, 0xa1, 0x55,
	0x82, 0xaa, 0xd7, 0xa1, 0x5b, 0x26, 0x0b, 0xa9, 0xf8, 0x54, 0xb8, 0xa9, 0x05, 0xb1, 0x90, 0xfb,
	0x3f, 0x2e, 0x83, 0xd3, 0x7e, 0x98, 0x63, 0x2b, 0xb0, 0xc4, 0xa3, 0xc8, 0xf9, 0x2f, 0xd6, 0x87,
	0x5e, 0x19, 0x9f, 0xf1, 0xb9, 0x03, 0x6c, 0x13, 0xd6, 0xf0, 0x93, 0x5e, 0x72, 0x9c, 0x35, 0xc5,
	0x4b, 0xf9, 0x5c, 0x84, 0xce, 0xb6, 0xe2, 0xe1, 0xa7, 0xe6, 0xed, 0x30, 0x17, 0xb6, 0x11, 0x78,
	0xc1, 0x65, 0xa1, 0xf2, 0xac, 0x4f, 0x97, 0x2f, 0xe7, 0x16, 0xdb, 0x81, 0x9b, 0xc8, 0xb1, 0xd0,
	0xd0, 0xd9, 0x65, 0x83, 0xea, 0x02, 0x24, 0x45, 0xe8, 0xb8, 0x36, 0x1d, 0x4f, 0x9d, 0xdb, 0x6c,
	0x1d, 0x56, 0xf3, 0x4b, 0x99, 0xa6, 0x8a, 0x1a, 0xb2, 0x3d, 0xd8, 0xbd, 0xa6, 0xe4, 0x29, 0x8f,
	0x4b, 0x1e, 0x39, 0x7b, 0x6c, 0x17, 0xb6, 0xce, 0xcb, 0xb9, 0xc8, 0xc6, 0x22, 0xba, 0x38, 0x4e,
	0xa2, 0x88, 0x7e, 0xca, 0x38, 0xfb, 0x6c, 0x1f, 0xdc, 0x6b, 0x52, 0x67, 0x32, 0xb8, 0x52, 0x3a,
	0xef, 0xa8, 0x19, 0x2e, 0x64, 0x2c, 0xf3, 0x4b, 0x11, 0x3a, 0x07, 0x38, 0x5f, 0x1a, 0xc9, 0xe2,
	0x24, 0x9e, 0x3a, 0x6f, 0xb0, 0x0d, 0xe8, 0x23, 0xf5, 0x19, 0x97, 0x91, 0x73, 0xa8, 0x98, 0x65,
	0x4c, 0x75, 0xd4, 0x79, 0x93, 0x31, 0x18, 0xfc, 0x9e, 0xcb, 0x62, 0x54, 0x9b, 0xef, 0x29, 0x01,
	0x4a, 0x1e, 0x4a, 0xf7, 0x7b, 0xcc, 0x51, 0x97, 0x66, 0xec, 0x62, 0xa3, 0x24, 0x17, 0xa1, 0x73,
	0x9f, 0xdd, 0x54, 0x29, 0x16, 0x3b, 0xe6, 0x32, 0x08, 0x44, 0x9e, 0x3b, 0xef, 0xb3, 0x2d, 0x75,
	0x13, 0x56, 0xd0, 0x67, 0x32, 0xcb, 0x8b, 0x33, 0x9e, 0xe7, 0xce, 0x91, 0x0d, 0xc6, 0x3c, 0x42,
	0xf0, 0x43, 0xe5, 0x1c, 0x0d, 0x2a, 0x7b, 0x1e, 0xd4, 0xf4, 0xf3, 0x38, 0x4c, 0x9c, 0xff, 0x66,
	0x43, 0xb8, 0xa5, 0x6f, 0x00, 0xca, 0xff, 0x32, 0x9e, 0x9a, 0x7c, 0xea, 0x3c, 0x54, 0xd6, 0x6a,
	0xde, 0x58, 0xf9, 0x53, 0x84, 0xce, 0x47, 0x0f, 0xff, 0xb2, 0x0c, 0x1b, 0xcf, 0xf5, 0xcf, 0x13,
	0xbc, 0x17, 0xaa, 0xe6, 0x03, 0x33, 0x92, 0xfe, 0xa3, 0x85, 0x0d, 0xd2, 0xae, 0xf5, 0x8b, 0xcb,
	0x4e, 0x77, 0x43, 0xf7, 0x3a, 0x43, 0xe7, 0xb1, 0x11, 0x6c, 0xe8, 0x5f, 0x11, 0xa4, 0x9e, 0xb9,
	0x75, 0xba, 0x6d, 0xfe, 0xd5, 0x18, 0xde, 0x5e, 0xc0, 0xd1, 0x5a, 0xbe, 0x82, 0x6d, 0x54, 0x6b,
	0x85, 0x29, 0x1a, 0xb4, 0xd7, 0xaa, 0x34, 0x0d, 0xa3, 0xf6, 0x17, 0x33, 0xb5, 0xca, 0xe7, 0x70,
	0xab, 0xad, 0x92, 0x2a, 0x26, 0xbb, 0xf6, 0x88, 0x67, 0x3f, 0xf5, 0x0f, 0xef, 0xbc, 0x82, 0xab,
	0xd5, 0xc6, 0xb0, 0xd7, 0x56, 0x6b, 0xe5, 0x78, 0x76, 0xb7, 0x25, 0xfd, 0x8a, 0xfa, 0x31, 0x7c,
	0xf7, 0xb5, 0xe3, 0xf4, 0x7c, 0xbf, 0x03, 0x17, 0x81, 0x05, 0xc9, 0x9d, 0x1d, 0xd4, 0x09, 0x69,
	0x51, 0xd6, 0x1f, 0xbe, 0xf1, 0xaa, 0x84, 0xa5, 0x93, 0xf3, 0xf9, 0x0d, 0xfc, 0xf7, 0xfb, 0xd1,
	0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x41, 0xa6, 0x9d, 0x28, 0x17, 0x1e, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UpetDjServiceClient is the client API for UpetDjService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UpetDjServiceClient interface {
	// 获取省市区列表
	QuerySectionList(ctx context.Context, in *SectionQueryRequest, opts ...grpc.CallOption) (*SectionQueryResponse, error)
	// 根据配送区域计算运费
	//  rpc CalcUpetDjMoney(UpetDjMoneyCalcRequest) returns(UpetDjMoneyResponse);
	// 修改订单状态，确认收货
	ConfirmUpetDj(ctx context.Context, in *UpetDjConfirmRequest, opts ...grpc.CallOption) (*UpetDjConfirmResponse, error)
	// 查询阿闻到家订单列表
	QueryUpetDjOrderList(ctx context.Context, in *UpetDjOrderQueryRequest, opts ...grpc.CallOption) (*UpetDjOrderQueryResponse, error)
	// 查询阿闻到家订单详情
	QueryUpetDjOrderDetail(ctx context.Context, in *UpetDjOrderDetailRequest, opts ...grpc.CallOption) (*UpetDjOrderDetailResponse, error)
	// 查询阿闻到家订单是否需要自动打印
	QueryUpetDjOrderIsAutoPrint(ctx context.Context, in *UpetDjOrderIsAutoPrintQueryRequest, opts ...grpc.CallOption) (*UpetDjOrderIsAutoPrintQueryResponse, error)
	// 查询促销活动的订单统计信息
	QueryPromotonOrderReport(ctx context.Context, in *PromotonOrderReportRequest, opts ...grpc.CallOption) (*PromotionOrderReportReponse, error)
}

type upetDjServiceClient struct {
	cc *grpc.ClientConn
}

func NewUpetDjServiceClient(cc *grpc.ClientConn) UpetDjServiceClient {
	return &upetDjServiceClient{cc}
}

func (c *upetDjServiceClient) QuerySectionList(ctx context.Context, in *SectionQueryRequest, opts ...grpc.CallOption) (*SectionQueryResponse, error) {
	out := new(SectionQueryResponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/QuerySectionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upetDjServiceClient) ConfirmUpetDj(ctx context.Context, in *UpetDjConfirmRequest, opts ...grpc.CallOption) (*UpetDjConfirmResponse, error) {
	out := new(UpetDjConfirmResponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/ConfirmUpetDj", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upetDjServiceClient) QueryUpetDjOrderList(ctx context.Context, in *UpetDjOrderQueryRequest, opts ...grpc.CallOption) (*UpetDjOrderQueryResponse, error) {
	out := new(UpetDjOrderQueryResponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/QueryUpetDjOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upetDjServiceClient) QueryUpetDjOrderDetail(ctx context.Context, in *UpetDjOrderDetailRequest, opts ...grpc.CallOption) (*UpetDjOrderDetailResponse, error) {
	out := new(UpetDjOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/QueryUpetDjOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upetDjServiceClient) QueryUpetDjOrderIsAutoPrint(ctx context.Context, in *UpetDjOrderIsAutoPrintQueryRequest, opts ...grpc.CallOption) (*UpetDjOrderIsAutoPrintQueryResponse, error) {
	out := new(UpetDjOrderIsAutoPrintQueryResponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/QueryUpetDjOrderIsAutoPrint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upetDjServiceClient) QueryPromotonOrderReport(ctx context.Context, in *PromotonOrderReportRequest, opts ...grpc.CallOption) (*PromotionOrderReportReponse, error) {
	out := new(PromotionOrderReportReponse)
	err := c.cc.Invoke(ctx, "/oc.UpetDjService/QueryPromotonOrderReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UpetDjServiceServer is the server API for UpetDjService service.
type UpetDjServiceServer interface {
	// 获取省市区列表
	QuerySectionList(context.Context, *SectionQueryRequest) (*SectionQueryResponse, error)
	// 根据配送区域计算运费
	//  rpc CalcUpetDjMoney(UpetDjMoneyCalcRequest) returns(UpetDjMoneyResponse);
	// 修改订单状态，确认收货
	ConfirmUpetDj(context.Context, *UpetDjConfirmRequest) (*UpetDjConfirmResponse, error)
	// 查询阿闻到家订单列表
	QueryUpetDjOrderList(context.Context, *UpetDjOrderQueryRequest) (*UpetDjOrderQueryResponse, error)
	// 查询阿闻到家订单详情
	QueryUpetDjOrderDetail(context.Context, *UpetDjOrderDetailRequest) (*UpetDjOrderDetailResponse, error)
	// 查询阿闻到家订单是否需要自动打印
	QueryUpetDjOrderIsAutoPrint(context.Context, *UpetDjOrderIsAutoPrintQueryRequest) (*UpetDjOrderIsAutoPrintQueryResponse, error)
	// 查询促销活动的订单统计信息
	QueryPromotonOrderReport(context.Context, *PromotonOrderReportRequest) (*PromotionOrderReportReponse, error)
}

// UnimplementedUpetDjServiceServer can be embedded to have forward compatible implementations.
type UnimplementedUpetDjServiceServer struct {
}

func (*UnimplementedUpetDjServiceServer) QuerySectionList(ctx context.Context, req *SectionQueryRequest) (*SectionQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySectionList not implemented")
}
func (*UnimplementedUpetDjServiceServer) ConfirmUpetDj(ctx context.Context, req *UpetDjConfirmRequest) (*UpetDjConfirmResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmUpetDj not implemented")
}
func (*UnimplementedUpetDjServiceServer) QueryUpetDjOrderList(ctx context.Context, req *UpetDjOrderQueryRequest) (*UpetDjOrderQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUpetDjOrderList not implemented")
}
func (*UnimplementedUpetDjServiceServer) QueryUpetDjOrderDetail(ctx context.Context, req *UpetDjOrderDetailRequest) (*UpetDjOrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUpetDjOrderDetail not implemented")
}
func (*UnimplementedUpetDjServiceServer) QueryUpetDjOrderIsAutoPrint(ctx context.Context, req *UpetDjOrderIsAutoPrintQueryRequest) (*UpetDjOrderIsAutoPrintQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUpetDjOrderIsAutoPrint not implemented")
}
func (*UnimplementedUpetDjServiceServer) QueryPromotonOrderReport(ctx context.Context, req *PromotonOrderReportRequest) (*PromotionOrderReportReponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotonOrderReport not implemented")
}

func RegisterUpetDjServiceServer(s *grpc.Server, srv UpetDjServiceServer) {
	s.RegisterService(&_UpetDjService_serviceDesc, srv)
}

func _UpetDjService_QuerySectionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SectionQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).QuerySectionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/QuerySectionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).QuerySectionList(ctx, req.(*SectionQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpetDjService_ConfirmUpetDj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpetDjConfirmRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).ConfirmUpetDj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/ConfirmUpetDj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).ConfirmUpetDj(ctx, req.(*UpetDjConfirmRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpetDjService_QueryUpetDjOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpetDjOrderQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/QueryUpetDjOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderList(ctx, req.(*UpetDjOrderQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpetDjService_QueryUpetDjOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpetDjOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/QueryUpetDjOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderDetail(ctx, req.(*UpetDjOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpetDjService_QueryUpetDjOrderIsAutoPrint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpetDjOrderIsAutoPrintQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderIsAutoPrint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/QueryUpetDjOrderIsAutoPrint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).QueryUpetDjOrderIsAutoPrint(ctx, req.(*UpetDjOrderIsAutoPrintQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpetDjService_QueryPromotonOrderReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotonOrderReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpetDjServiceServer).QueryPromotonOrderReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.UpetDjService/QueryPromotonOrderReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpetDjServiceServer).QueryPromotonOrderReport(ctx, req.(*PromotonOrderReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UpetDjService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.UpetDjService",
	HandlerType: (*UpetDjServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QuerySectionList",
			Handler:    _UpetDjService_QuerySectionList_Handler,
		},
		{
			MethodName: "ConfirmUpetDj",
			Handler:    _UpetDjService_ConfirmUpetDj_Handler,
		},
		{
			MethodName: "QueryUpetDjOrderList",
			Handler:    _UpetDjService_QueryUpetDjOrderList_Handler,
		},
		{
			MethodName: "QueryUpetDjOrderDetail",
			Handler:    _UpetDjService_QueryUpetDjOrderDetail_Handler,
		},
		{
			MethodName: "QueryUpetDjOrderIsAutoPrint",
			Handler:    _UpetDjService_QueryUpetDjOrderIsAutoPrint_Handler,
		},
		{
			MethodName: "QueryPromotonOrderReport",
			Handler:    _UpetDjService_QueryPromotonOrderReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/upet_dj_service.proto",
}
