package utils

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"log"
	"runtime"
	"strconv"
	"strings"
	"time"
	"github.com/google/uuid"
	"github.com/maybgit/glog"
)

const (
	ACCESS_FROM_USER = 0
	COLON            = ":"
)

//PrintJSON 将struct序列化json打印日志
func PrintJSON(inter interface{}) {
	bt, _ := json.Marshal(inter)
	log.Println("json", string(bt))
}

//生成32位md5字串
func GetMd5String(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

//生成32位Guid字串
func GetGuid() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

//生成36位Guid字串
func GetGuid36() string {
	return uuid.New().String()
}

//GenCode 生成编号，前缀+随机数（或者指定的某个数字）+数量（数量位数不足5，会前置补0）
func GenCode(prefix string, rand int, count int) string {
	var strs []string
	strs = append(strs, prefix)
	strs = append(strs, time.Now().Local().Format("20060102150405"))
	strs = append(strs, strconv.Itoa(rand))
	strCount := strconv.Itoa(int(count))
	for index := 0; index < 5-len(strCount); index++ {
		strs = append(strs, "0")
	}
	strs = append(strs, strCount)
	return strings.Join(strs, "")
}

//全角转换半角
func DBCtoSBC(s string) string {
	retstr := ""
	for _, i := range s {
		inside_code := i
		if inside_code == 12288 {
			inside_code = 32
		} else {
			inside_code -= 65248
		}
		if inside_code < 32 || inside_code > 126 {
			retstr += string(i)
		} else {
			retstr += string(inside_code)
		}
	}
	return retstr
}

//根据阿里加密规则，获取用户名
func GetUserName(ak string, resourceOwnerId uint64) string {
	var buffer bytes.Buffer
	buffer.WriteString(strconv.Itoa(ACCESS_FROM_USER))
	buffer.WriteString(COLON)
	buffer.WriteString(strconv.FormatUint(resourceOwnerId, 10))
	buffer.WriteString(COLON)
	buffer.WriteString(ak)
	return base64.StdEncoding.EncodeToString(buffer.Bytes())
}

//根据阿里加密规则，获取密码
func GetPassword(sk string) string {
	now := time.Now()
	currentMillis := strconv.FormatInt(now.UnixNano()/1000000, 10)
	var buffer bytes.Buffer
	buffer.WriteString(strings.ToUpper(HmacSha1(currentMillis, sk)))
	buffer.WriteString(COLON)
	buffer.WriteString(currentMillis)
	glog.Info(currentMillis)
	glog.Info(HmacSha1(sk, currentMillis))
	return base64.StdEncoding.EncodeToString(buffer.Bytes())
}

//加密方式
func HmacSha1(keyStr string, message string) string {
	key := []byte(keyStr)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(message))
	return hex.EncodeToString(mac.Sum(nil))
}

// 获取正在运行的函数名
func RunFuncName() string {
	pc := make([]uintptr, 1)
	runtime.Callers(2, pc)
	f := runtime.FuncForPC(pc[0])
	return f.Name()
}