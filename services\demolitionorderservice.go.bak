package services

import (
	"context"
	"dispatch-center/dtos"
	"dispatch-center/models"
	proto "dispatch-center/proto/dc"
	"dispatch-center/proto/ic"
	"dispatch-center/utils"
	"fmt"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	logger "github.com/maybgit/glog"
	"google.golang.org/grpc"
	"runtime"
	"strconv"
	"time"
)

type DemolitionOrderService struct {
	DBService
}

//拆单
//第一步保存拆单传过来的参数
//第二步 获取订单所有对应可以发货的仓库
//第三步 获取订单产品对应仓库中库存数量
//第四步 判定是否可以整单对应一个仓库
//第五步 保存订单商品对应仓库
func (d *DemolitionOrderService) DemolitionOrder(ctx context.Context, params *proto.DemolitionOrderRequest) (*proto.DemolitionOrderResponse, error) {
	//把参数打印到日志
	logger.Info("拆单请求参数：", params)
	var out = new(proto.DemolitionOrderResponse)

	//defer func() {
	// if err:=recover();err!=nil{
	//	 logger.Error("拆单错误原因",err)
	// }
	//}()

	//var error error
	out.Code = 200
	out.Message = "Success"

	if len(params.OrderGoodsDetails) <= 0 {
		out.Code = 400
		out.Message = "请求参数异常"
		return out, nil
	}
	//先去除重复
	var productOrderDtos []dtos.ProductOrderDto
	for _, item := range params.OrderGoodsDetails {
		var productOrderDto dtos.ProductOrderDto
		productOrderDto.Id = ""
		productOrderDto.GoodsId = item.Goodsid
		productOrderDtos = append(productOrderDtos, productOrderDto)
	}

	removeProductOrderDtos := RemoveRepByMap(productOrderDtos)
	//长度不相等
	if len(removeProductOrderDtos) != len(productOrderDtos) {
		var details []*proto.OrderGoodsDetail
		for _, item := range removeProductOrderDtos {
			var v *proto.OrderGoodsDetail
			quantity := int32(0)
			for _, model := range params.OrderGoodsDetails {
				if item.GoodsId == model.Goodsid {
					v = model
					quantity = quantity + model.Quantity
				}
			}
			v.Quantity = quantity
			details = append(details, v)
		}
		params.OrderGoodsDetails = details
	}

	//第二步 获取订单所有对应可以发货的仓库
	warehouses := d.GetWarehouseModels(int(params.Ordersource), params.Province)
	logger.Info("获取到仓库：", warehouses, time.Now())
	if len(warehouses) <= 0 {
		out.Code = 400
		out.Message = "找不到对应发货仓库"
		return out, nil
	}

	//门店逻辑
	if params.Ordersource == 3 {

	}

	//第三步 获取订单产品对应仓库中库存数量
	warehouses, goodsStores, baseResponse := d.GetGoodsStoreModel(params.OrderGoodsDetails, warehouses)

	if baseResponse.Code != 200 {
		out.Code = baseResponse.Code
		out.Message = baseResponse.Message
		return out, nil
	}

	enough := true

	if len(goodsStores) <= 0 {
		enough = false
	}

	//判定库存是否可以满足此订单
	for _, item := range params.OrderGoodsDetails {
		quantity := 0
		for _, itemGoods := range goodsStores {
			if itemGoods.GoodsId == item.Goodsid {
				quantity = quantity + itemGoods.Stock
			}
		}
		if quantity < int(item.Quantity) {
			enough = false
			break
		}
	}

	if enough == false {
		out.Code = 400
		out.Message = "库存不足，无法全部分配商品"
		return out, nil
	}

	warehouseId := 0
	//第四步 判定是否可以整单对应一个仓库
	firstResult, warehouseId := d.FirstToWarehouse(warehouses)

	var warehouseOrders []*models.WarehouseOrder
	if firstResult == true {
		warehouseOrders = d.SetWarehouseOrder(goodsStores, params.Orderid, warehouseId)

	} else {
		warehouseOrders = d.GetWarehouseOrder(goodsStores, params.OrderGoodsDetails, warehouses, params.Orderid, warehouseOrders)
	}

	// 去掉 为0 的实体
	var lists []*models.WarehouseOrder

	for _, item := range warehouseOrders {
		if item.Quantity > 0 {
			lists = append(lists, item)
		}
	}
	dbConn := d.GetConn()

	defer dbConn.Close()
	tran := dbConn.NewSession()

	defer tran.Close()
	tran.Begin()
	//保存拆单传过来的参数
	baseResponse, error := d.saveOrderParams(*params, tran)

	if error != nil || out.Code != 200 {
		out.Code = baseResponse.Code
		out.Message = baseResponse.Message
		out.Error = baseResponse.Error
		return out, nil
	}

	//第五步
	baseResponse, error = d.SaveWarehouseOrder(lists, tran)

	if error != nil || baseResponse.Code != 200 {

		panic(baseResponse.Message)
		out.Code = baseResponse.Code
		out.Message = baseResponse.Message
		out.Error = baseResponse.Error
		tran.Rollback()
		return out, nil
	}
	//第六步 锁库存
	baseResponse, error = d.FreezeStock(lists, params.Memberid)
	if error != nil || baseResponse.Code != 200 {
		out.Code = baseResponse.Code
		out.Message = baseResponse.Message
		out.Error = baseResponse.Error
		//tran.Rollback()
		return out, nil
	}

	//返回商品对应仓库 到前台
	var warehouseToGoodsList []*proto.WarehouseToGoods

	for _, item := range lists {

		var warehouseToGoods = new(proto.WarehouseToGoods)
		warehouseToGoods.Orderid = item.OrderId
		warehouseToGoods.Warehouseid = int32(item.WarehouseId)
		warehouseToGoods.Quantity = int32(item.Quantity)
		warehouseToGoods.Goodsid = item.GoodsId
		warehouseToGoods.Thirdid = item.Thirdid
		warehouseToGoods.Warehousecode = item.WarehouseCode
		warehouseToGoodsList = append(warehouseToGoodsList, warehouseToGoods)
	}

	out.WarehouseToGoodsList = warehouseToGoodsList

	error = tran.Commit()

	if error != nil || baseResponse.Code != 200 {
		out.Code = baseResponse.Code
		out.Message = baseResponse.Message
		out.Error = baseResponse.Error
		//tran.Rollback()
		return out, nil
	}
	return out, nil
}

//写入测试数据
func (d *DemolitionOrderService) SetDemolitionOrderTestData(ctx context.Context, params *proto.DataRequest) (*proto.BaseResponse, error) {
	var out = new(proto.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	client := utils.ConnectRedis()
	defer client.Close()

	for _, item := range params.DataList {
		client.Set("stock:"+item.Goodsid+":"+strconv.Itoa(int(item.Warehouseid)), item.Store, 0)
	}
	return out, nil
}

//锁定库存
func (d *DemolitionOrderService) FreezeStock(warehouseOrders []*models.WarehouseOrder, memberId string) (*proto.BaseResponse, error) {

	var out = new(proto.BaseResponse)
	out.Code = 200

	conn, ctx, client, cf := getClient()
	defer conn.Close()
	defer cf()
	var goodsList []*ic.OrderGoodsInfo

	for _, item := range warehouseOrders {
		var goods = new(ic.OrderGoodsInfo)
		goods.GoodsId = item.GoodsId
		goods.WarehouseId = int32(item.WarehouseId)
		goods.Number = int32(item.Quantity)
		goodsList = append(goodsList, goods)
	}

	var params ic.FreezeRequest
	params.OrderId = warehouseOrders[0].OrderId
	params.MemberId = memberId
	params.GoodsList = goodsList
	r, err := client.FreezeStock(ctx, &params)
	if err != nil || r.Code != 200 {
		out.Code = r.Code
		out.Message = r.Message
		out.Error = r.Error
		return out, err
	}
	out.Message = "Success"
	return out, nil

}

// 判定整单是否可以分配在同一仓库
func (d *DemolitionOrderService) FirstToWarehouse(warehouses []*dtos.WarehouseModel) (bool, int) {
	for _, item := range warehouses {
		if item.Enough == true {
			return true, item.Id
		}
	}
	return false, 0
}

//整单一个仓库不满足需求递归指定仓库
func (d *DemolitionOrderService) GetWarehouseOrder(store []*dtos.GoodsStoreDto,
	orderDetails []*proto.OrderGoodsDetail, warehouses []*dtos.WarehouseModel, orderId string,
	warehouseOrders []*models.WarehouseOrder) []*models.WarehouseOrder {

	//先初始化仓库数据
	var warehouses1 []*dtos.WarehouseModel
	for _, item := range warehouses {
		warehouses1 = append(warehouses1, item)
	}
	var model = new(dtos.WarehouseModel)
	// 按照 先商品种类，商品数量，配送等级，配送仓库 递归第一个最优仓库
	if len(warehouses1) > 0 {

		//排序商品类型
		//sort.Sort(dtos.Wrapper{warehouse: warehouses1, by: func(p, q *dtos.WarehouseModel) bool {
		//	return q.GoodsType < p.GoodsType // GoodsType 递减排序
		//}})
		dtos.SortLog(warehouses1, func(p, q *dtos.WarehouseModel) bool {
			return q.GoodsType < p.GoodsType
		})

		goodsType := warehouses1[0].GoodsType
		warehouses2 := d.WarehouseSortGoodsType(warehouses1, goodsType)
		if len(warehouses2) > 1 {
			//排序商品数量
			//sort.Sort(dtos.Wrapper{warehouses2, func(p, q *dtos.WarehouseModel) bool {
			//	return q.Goodsnumber < p.Goodsnumber // Goodsnumber 递减排序
			//}})
			dtos.SortLog(warehouses2, func(p, q *dtos.WarehouseModel) bool {
				return q.Goodsnumber < p.Goodsnumber
			})
			goodsNumber := warehouses2[0].Goodsnumber
			warehouses3 := d.WarehouseSortGoodsNumber(warehouses2, goodsNumber)
			if len(warehouses3) > 1 {

				//排序仓库配送等级

				//sort.Sort(dtos.Wrapper{warehouses3, func(p, q *dtos.WarehouseModel) bool {
				//	return q.Arealevel < p.Arealevel // Arealevel 递减排序
				//}})

				dtos.SortLog(warehouses3, func(p, q *dtos.WarehouseModel) bool {
					return p.Arealevel < q.Arealevel
				})
				areaLevel := warehouses3[0].Arealevel
				warehouses4 := d.WarehouseSortAreaLevel(warehouses3, areaLevel)
				if len(warehouses4) > 1 {

					//排序仓库等级

					//sort.Sort(dtos.Wrapper{warehouses4, func(p, q *dtos.WarehouseModel) bool {
					//	return q.Level < p.Level // Level 递减排序
					//}})
					dtos.SortLog(warehouses4, func(p, q *dtos.WarehouseModel) bool {
						return p.Level < q.Level
					})
					level := warehouses4[0].Level
					warehouses5 := d.WarehouseSortLevel(warehouses4, level)
					model = warehouses5[0]
				} else {
					model = warehouses4[0]
				}
			} else {
				model = warehouses3[0]
			}
		} else {
			model = warehouses2[0]
		}
	}

	//设定递归遍历参数 []*dtos.GoodsStoreDto
	var parStores []*dtos.GoodsStoreDto
	var lockStores []*dtos.GoodsStoreDto

	for _, item := range store {
		var goods = new(dtos.GoodsStoreDto)
		goods = item
		if item.WarehouseId == model.Id && item.Quantity > 0 {
			var warehouseOrder = new(models.WarehouseOrder)
			warehouseOrder.WarehouseId = item.WarehouseId
			warehouseOrder.GoodsId = item.GoodsId
			warehouseOrder.Quantity = item.Quantity
			warehouseOrder.Thirdid = item.Thirdid
			warehouseOrder.WarehouseCode = item.Code
			if item.Quantity >= item.Stock {
				warehouseOrder.Quantity = item.Stock
			}
			warehouseOrder.OrderId = orderId
			warehouseOrders = append(warehouseOrders, warehouseOrder)
			var lookStore = new(dtos.GoodsStoreDto)
			lookStore = item
			lookStore.Quantity = warehouseOrder.Quantity
			lockStores = append(lockStores, lookStore)

		} else {
			parStores = append(parStores, goods)
		}

	}

	for _, item := range lockStores {
		for i := 0; i < len(parStores); i++ {
			if item.GoodsId == parStores[i].GoodsId {
				parStores[i].Quantity = parStores[i].Quantity - item.Quantity
				if parStores[i].Quantity < 0 {
					parStores[i].Quantity = 0
				} else {
					parStores[i].Enough = true
				}
			}
		}

	}

	//设定递归遍历参数 *proto.OrderGoodsDetail
	var parOrderDetails []*proto.OrderGoodsDetail
	for _, item := range orderDetails {
		var parOrderDetail = new(proto.OrderGoodsDetail)
		parOrderDetail = item
		for _, itemGoods := range store {
			if itemGoods.WarehouseId == model.Id {
				if itemGoods.GoodsId == parOrderDetail.Goodsid {
					if itemGoods.Stock <= itemGoods.Quantity {
						parOrderDetail.Quantity = parOrderDetail.Quantity - int32(itemGoods.Stock)
						parOrderDetails = append(parOrderDetails, parOrderDetail)
					}
				}

			}
		}

	}

	//判定是否需要继续循环往下遍历
	enough := false
	for _, item := range parOrderDetails {
		if item.Quantity > 0 {
			enough = true
			break
		}
	}
	if enough == true {
		//设定递归遍历参数 []*dtos.WarehouseModel
		var parWarehouses []*dtos.WarehouseModel
		for _, item := range warehouses {
			var parWarehouse = new(dtos.WarehouseModel)
			parWarehouse = item
			if item.Id != model.Id {
				goodsType := 0
				goodsNumber := 0
				for _, itemParStore := range parStores {
					if parWarehouse.Id == itemParStore.WarehouseId {
						if itemParStore.Enough == true {
							goodsNumber = goodsNumber + itemParStore.Stock
							goodsType = goodsType + 1
						} else {
							goodsNumber = goodsNumber + itemParStore.Quantity
						}
					}
				}
				parWarehouse.GoodsType = goodsType
				parWarehouses = append(parWarehouses, parWarehouse)
			}
		}

		return d.GetWarehouseOrder(parStores, parOrderDetails, parWarehouses, orderId, warehouseOrders)
	}

	return warehouseOrders

}

//获取商品种类相同的仓库
func (d *DemolitionOrderService) WarehouseSortGoodsType(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].GoodsType {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取商品数量相同的仓库
func (d *DemolitionOrderService) WarehouseSortGoodsNumber(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Goodsnumber {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取配送区域相同的仓库
func (d *DemolitionOrderService) WarehouseSortAreaLevel(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Arealevel {
			items = append(items, warehouses[i])
		}
	}
	return items
}

//获取仓库等级相同的仓库
func (d *DemolitionOrderService) WarehouseSortLevel(warehouses []*dtos.WarehouseModel, number int) []*dtos.WarehouseModel {
	var items []*dtos.WarehouseModel
	for i := 0; i < len(warehouses); i++ {
		if number == warehouses[i].Level {
			items = append(items, warehouses[i])
		}
	}
	return items
}

// 设置商品对应发货订单实体
func (d *DemolitionOrderService) SetWarehouseOrder(goodsStores []*dtos.GoodsStoreDto, orderId string, warehouseId int) []*models.WarehouseOrder {
	var warehouseOrders []*models.WarehouseOrder
	for _, item := range goodsStores {
		if item.WarehouseId == warehouseId {
			var warehouseOrder = new(models.WarehouseOrder)
			warehouseOrder.OrderId = orderId
			warehouseOrder.Quantity = item.Quantity
			warehouseOrder.WarehouseId = item.WarehouseId
			warehouseOrder.GoodsId = item.GoodsId
			warehouseOrder.Thirdid = item.Thirdid
			warehouseOrder.WarehouseCode = item.Code
			warehouseOrders = append(warehouseOrders, warehouseOrder)
		}
	}

	return warehouseOrders
}

//保存拆单后 商品对应仓库
func (d *DemolitionOrderService) SaveWarehouseOrder(model []*models.WarehouseOrder, tran *xorm.Session) (*proto.BaseResponse, error) {
	var out = new(proto.BaseResponse)
	out.Code = 200

	var err error
	if len(model) > 0 {
		_, err = tran.Insert(model)
		if err != nil {
			out.Code = 500
			out.Error = err.Error()
			out.Message = err.Error()
			tran.Rollback()
		}
	}
	return out, nil
}

//保存原始参数，等拆单之后推送需要。
func (d *DemolitionOrderService) saveOrderParams(model proto.DemolitionOrderRequest, tran *xorm.Session) (*proto.BaseResponse, error) {

	timeTemplate1 := "2006-01-02 15:04:05" //常规类型

	//定义返回信息
	var out = new(proto.BaseResponse)
	out.Code = 200

	//dbConn := d.GetConn()
	//tran := dbConn.NewSession()
	//tran.Begin()
	var err error
	var orderInfo models.OrderInfo
	//orderInfo.Id = model.Id
	orderInfo.Orderid = model.Orderid
	orderInfo.Memberid = model.Memberid
	orderInfo.Ordermoney = int(model.Ordermoney)
	orderInfo.Ordertype = int(model.Ordertype)
	orderInfo.Ordertypedetail = int(model.Ordertypedetail)
	orderInfo.Orderstate = int(model.Orderstate)
	orderInfo.Orderdetail = model.Orderdetail
	orderInfo.Useragent = int(model.Useragent)
	orderInfo.Platformid = int(model.Platformid)
	orderInfo.Belonghospitalid = model.Belonghospitalid
	createTime, _ := time.ParseInLocation(timeTemplate1, model.Createtime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	orderInfo.Createtime = createTime
	lastTime, _ := time.ParseInLocation(timeTemplate1, model.Lasttime, time.Local)
	orderInfo.Lasttime = lastTime
	orderInfo.Orderchildenstate = int(model.Orderchildenstate)
	orderInfo.Isevaluate = int(model.Isevaluate)
	orderInfo.Ispostupet = int(model.Ispostupet)
	orderInfo.Isnotify = int(model.Isnotify)
	orderInfo.Ordersource = int(model.Ordersource)
	orderInfo.Status = int(model.Status)
	orderInfo.Province = model.Province
	orderInfo.City = model.City
	orderInfo.Region = model.Region

	_, err = tran.Insert(&orderInfo)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = err.Error()
		tran.Rollback()
		return out, err
	}
	var goodsList []models.OrderGoodsDetail
	if len(model.OrderGoodsDetails) > 0 {
		for i := 0; i < len(model.OrderGoodsDetails); i++ {
			orderGoods := model.OrderGoodsDetails[i]
			var goods models.OrderGoodsDetail
			goods.Id = orderGoods.Id
			goods.Orderid = orderGoods.Orderid
			goods.Goodsid = orderGoods.Goodsid
			goods.Barcode = orderGoods.Barcode
			goods.Name = orderGoods.Name
			goods.Goodsimage = orderGoods.Goodsimage
			goods.Univalence = int(orderGoods.Univalence)
			goods.Sellprice = int(orderGoods.Sellprice)
			goods.Quantity = int(orderGoods.Quantity)
			goods.Unit = orderGoods.Unit
			goods.Applyhospitalid = orderGoods.Applyhospitalid
			goods.Chargeoff = int(orderGoods.Chargeoff)
			goods.Chargeoffcode = orderGoods.Chargeoffcode
			goods.Chargeoffhospitalid = orderGoods.ChargeoffhospitalId
			chargeOffTime, _ := time.ParseInLocation(timeTemplate1, orderGoods.Chargeofftime, time.Local)
			goods.Chargeofftime = chargeOffTime
			goodsCreateTime, _ := time.ParseInLocation(timeTemplate1, model.Createtime, time.Local)
			goods.Createtime = goodsCreateTime
			goodsLastTime, _ := time.ParseInLocation(timeTemplate1, model.Lasttime, time.Local)
			goods.Lasttime = goodsLastTime
			goods.Source = int(orderGoods.Source)
			goods.Useragent = int(orderGoods.Useragent)
			goods.Chargeoffmemberid = orderGoods.Chargeoffmemberid
			goodsList = append(goodsList, goods)

		}
		_, err = tran.Insert(&goodsList)
		if err != nil {
			out.Code = 400
			out.Error = err.Error()
			out.Message = err.Error()
			tran.Rollback()
			return out, err
		}
	}

	return out, nil
}

//获取整理好的所有仓库数据
func (d *DemolitionOrderService) GetWarehouseModels(comefrom int, province string) []*dtos.WarehouseModel {
	logger.Info("GetWarehouseModels：", comefrom, province, time.Now())
	var warehouses []*dtos.WarehouseModel
	warehouses = d.GetWarehouse(comefrom, province)
	return warehouses
}

//获取订单商品所在仓库 库存情况
func (d *DemolitionOrderService) GetGoodsStoreModel(orders []*proto.OrderGoodsDetail, params []*dtos.WarehouseModel) ([]*dtos.WarehouseModel,
	[]*dtos.GoodsStoreDto, *proto.BaseResponse) {

	var out = new(proto.BaseResponse)
	out.Code = 200
	out.Message = "Success"

	var stores []*dtos.GoodsStoreDto
	var warehouses []*dtos.WarehouseModel
	var productAndStocks []*ic.ProductsAndStock
	//遍历所有仓库
	for _, item := range params {
		var warehouse = new(dtos.WarehouseModel)
		warehouse = item
		warehouse.Enough = false
		warehouse.GoodsType = 0
		warehouse.Goodsnumber = 0
		//遍历所有订单对应的商品
		for _, itemOrder := range orders {
			var store = new(dtos.GoodsStoreDto)
			store.WarehouseId = item.Id
			//初始化查询库存的参数
			productAndStock := new(ic.ProductsAndStock)
			productAndStock.ProductId = itemOrder.Goodsid
			productAndStock.WarehouseId = int32(store.WarehouseId)
			productAndStocks = append(productAndStocks, productAndStock)

			store.GoodsId = itemOrder.Goodsid
			//获取当前这个仓库对应这个商品的库存
			store.Stock = 0 //d.GetGoodsStore(store.GoodsId, store.WarehouseId)
			//store.Enough = false
			store.Quantity = int(itemOrder.Quantity)
			store.Thirdid = warehouse.Thirdid
			store.Code = warehouse.Code

			if store.Stock >= int(itemOrder.Quantity) {
				store.Enough = true
				warehouse.GoodsType = item.GoodsType + 1
				warehouse.Goodsnumber = item.Goodsnumber + store.Quantity
			} else {
				warehouse.Goodsnumber = item.Goodsnumber + store.Stock
			}
			stores = append(stores, store)
		}
		//if item.Enough == true {
		//	warehouse.GoodsType = item.GoodsType + 1
		//}
		warehouses = append(warehouses, warehouse)
	}

	//查询订单商品对应的仓库所有库存
	if len(productAndStocks) > 0 {
		out, goodsStocks := d.GetGoodsStoreList(productAndStocks)
		if out.Code != 200 {
			return warehouses, stores, out
		}
		for i := 0; i < len(warehouses); i++ {
			warehouse := warehouses[i]
			warehouse.Enough = false
			enough := true
			for j := 0; j < len(stores); j++ {
				store := stores[j]
				if store.WarehouseId == warehouse.Id {
					for _, item := range goodsStocks {
						if item.ProductId == store.GoodsId && item.Warehouse_Id == int32(store.WarehouseId) {
							stores[j].Stock = int(item.Store)
							//如果这个仓库满足当前这个商品 标识为true 否则false,并且记录当前仓库满足多少个商品种类 以及商品数量
							if store.Stock >= store.Quantity {
								store.Enough = true
								warehouses[i].GoodsType = warehouse.GoodsType + 1
								warehouses[i].Goodsnumber = warehouse.Goodsnumber + store.Quantity
							} else {
								enough = false
								warehouses[i].Goodsnumber = warehouse.Goodsnumber + store.Stock
							}
						}
					}
				}
			}
			warehouses[i].Enough = enough
		}

	}

	// 标示当前这个仓库是否全部满足订单所有商品的库存  满足标示true 否则false
	if len(stores) > 0 {
		for i := 0; i < len(warehouses); i++ {
			enough := true
			for _, item := range stores {
				if item.WarehouseId == warehouses[i].Id && item.Enough == false {
					enough = false
					break
				}
			}
			warehouses[i].Enough = enough
		}
	}

	return warehouses, stores, out
}

//根据商品sku,仓库Id获取当前仓库商品的库存数量
func (d *DemolitionOrderService) GetGoodsStore(goodsId string, warehouseId int) int {

	client := utils.ConnectRedis()
	defer client.Close()

	var key = fmt.Sprint("stock:" + goodsId + ":" + strconv.Itoa(warehouseId))
	r := client.Get(key).Val()
	out, _ := strconv.Atoi(r)
	//for _, v := range r {
	//	//获取到的数量
	//	value := utils.ConnectRedis().Get(v).Val()
	//	valueInt, _ := strconv.Atoi(value)
	//	out = out + valueInt
	//}

	return out
}

//GetGoodsStoreList 调用库存中心获取库存数量
func (d *DemolitionOrderService) GetGoodsStoreList(goods []*ic.ProductsAndStock) (*proto.BaseResponse, []*ic.GoodsStockInfo) {
	var out = new(proto.BaseResponse)
	out.Code = 200

	conn, ctx, client, cf := getClient()
	defer conn.Close()
	defer cf()
	var params ic.GetStockInfoRequest
	for _, item := range goods {
		model := new(ic.ProductsAndStock)
		model.WarehouseId = item.WarehouseId
		model.ProductId = item.ProductId
		params.GoodsList = append(params.GoodsList, model)
	}

	r, err := client.GetStockInfo(ctx, &params)
	if err != nil || r.Code != 200 {
		out.Code = r.Code
		out.Message = r.Message
		out.Error = r.Error
		return out, nil
	}
	out.Message = "Success"
	return out, r.GoodsInfoList
}

// 获取仓库主表数据
func (d *DemolitionOrderService) GetWarehouse(comefrom int, province string) []*dtos.WarehouseModel {

	logger.Info("GetWarehouse：", comefrom, province, time.Now())
	dbConn := d.GetConn()
	defer dbConn.Close()
	var warehouseModels []*dtos.WarehouseModel

	sql := `SELECT  a.id,a.thirdid,a.code,a.name,a.comefrom,a.level,a.category,b.level AS areaLevel,b.areaid
			FROM warehouse a 
			INNER JOIN warehouse_area b ON  b.warehouseid=a.id 
			INNER JOIN base_area c ON b.areaid=c.area_id
			WHERE a.status=1  AND a.comefrom=?  AND c.area_name=?
			ORDER BY b.level,a.level;`
	err := dbConn.SQL(sql, comefrom, province).Find(&warehouseModels)
	logger.Info("GetWarehouse：", comefrom, province, err, time.Now())
	if err != nil {
		logger.Error("数据库错误信息：", err)
		var _warehouseModels []*dtos.WarehouseModel
		return _warehouseModels
	}
	logger.Info("warehouseModels：", warehouseModels, err, time.Now())
	return warehouseModels
}

// 获取仓库配送区域数据
func (d *DemolitionOrderService) GetWarehouseArea(comefrom int, province string) []*models.WarehouseArea {
	var warehouseArea []*models.WarehouseArea

	Engine.SQL("SELECT a.`id`,a.`areaid`,a.`warehouseid`,a.`level`,a.`lastdate`  FROM `warehouse_area` a  JOIN  warehouse  b ON  a.`warehouseid`=b.id  WHERE b.`status`=1  AND b.comefrom=?", comefrom).Find(&warehouseArea)

	return warehouseArea

}

// 建立InventoryCenter 的grpc 连接公共方法
func getClient() (*grpc.ClientConn, context.Context, ic.InventoryServiceClient, context.CancelFunc) {
	/*config := config.SysConfig{}
	config.LoadConfig()
	inventoryAddress := config.InventoryCenterService.Address */
	inventoryAddress := config.GetString("grpc.inventory-center")
	if inventoryAddress == "" || runtime.GOOS == "windows" {
		inventoryAddress = "localhost:11007"
	}
	if conn, err := grpc.Dial(inventoryAddress, grpc.WithInsecure()); err != nil {
		logger.Error("连接GRPC发生错误：", err)
		return nil, nil, nil, nil
	} else {
		//ent := proto.NewDcProductClient(conn)
		client := ic.NewInventoryServiceClient(conn)
		ctx, cf := context.WithTimeout(context.Background(), time.Second*30)
		return conn, ctx, client, cf
	}
}

func RemoveRepByMap(slc []dtos.ProductOrderDto) []dtos.ProductOrderDto {
	result := []dtos.ProductOrderDto{}         //存放返回的不重复切片
	tempMap := map[dtos.ProductOrderDto]byte{} // 存放不重复主键
	for _, e := range slc {
		l := len(tempMap)
		tempMap[e] = 0 //当e存在于tempMap中时，再次添加是添加不进去的，，因为key不允许重复
		//如果上一行添加成功，那么长度发生变化且此时元素一定不重复
		if len(tempMap) != l { // 加入map后，map长度变化，则元素不重复
			result = append(result, e) //当元素不重复时，将元素添加到切片result中
		}
	}
	return result
}
