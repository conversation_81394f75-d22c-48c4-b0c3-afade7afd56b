package main

import (
	pb "dispatch-center/proto/dc"
	"dispatch-center/services"
	"time"

	"github.com/limitedlee/microservice/micro"
	"github.com/maybgit/glog"
	"google.golang.org/grpc/reflection"
)

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh
}

func main() {
	micro := micro.MicService{}
	micro.NewServer()
	//服务注册中心
	//仓库模块
	pb.RegisterWarehouseServiceServer(micro.GrpcServer, &services.WarehouseService{})
	//区域模块
	pb.RegisterBaseAreaServiceServer(micro.GrpcServer, &services.BaseAreaService{})

	pb.RegisterDemolitionOrderServiceServer(micro.GrpcServer, &services.DemolitionOrderService{})

	//服务反射，便于查看grpc的状态
	reflection.Register(micro.GrpcServer)
	glog.Info("dispatch-center服务启动...")
	micro.Start()
}
