package models

type Stock struct {
	Kid           int    `xorm:"not null pk comment('a8 stock kid') INT(11)"`
	TypeId        string `xorm:"default 'NULL' comment('a8 stock.typeid') VARCHAR(50)"`
	Usercode      string `xorm:"default 'NULL' comment('a8 stock.usercode') VARCHAR(50)"`
	FullnameStype string `xorm:"default 'NULL' comment('a8 stype.fullname') VARCHAR(100)"`
	FullnameStock string `xorm:"default 'NULL' comment('a8 stock.fullname') VARCHAR(100)"`
	Address       string `xorm:"default 'NULL' comment('a8 stock.Add') VARCHAR(100)"`
}