package services

import (
	"context"
	"dispatch-center/enum"
	"dispatch-center/models"
	proto "dispatch-center/proto/dc"
	"dispatch-center/proto/pc"
	"dispatch-center/utils"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

type WarehouseService struct {
	DBService
	BaseAreaService
}

var CategoryTypeMap = map[int32]string{1: "电商仓", 2: "区域仓", 3: "门店仓", 4: "前置仓", 5: "前置仓虚拟仓", 6: "门店前置仓"}

// WarehouseList 仓库列表
func (s *WarehouseService) WarehouseList(ctx context.Context, params *proto.WarehouseListRequest) (*proto.WarehouseListResponse, error) {
	res := new(proto.WarehouseListResponse)

	var strParams []interface{}

	sqlStr := "select w.* , count(wr.id) number from warehouse w left join warehouse_relationship wr on w.id = wr.warehouse_id where 1 = 1 "
	countSql := "SELECT COUNT(1) FROM ( %s ) s"

	sqlStr += fmt.Sprintf(" And w.org_id = %d ", params.OrgId)

	if params.Category != "" {
		sqlStr += fmt.Sprintf("  And w.category = %s", params.Category)
	} else if params.PreposeCategory == 1 {
		sqlStr += "  And w.category in (4,5)"
	}

	if params.KeyWord != "" {
		sqlStr += fmt.Sprintf("  And (w.name like '%s'  or w.id like '%s' or w.code = '%s')",
			"%"+params.KeyWord+"%",
			"%"+params.KeyWord+"%",
			params.KeyWord)
	}

	if params.Status == 1 {
		sqlStr += "  And (w.status = 1 )"
	} else if params.Status == 2 {
		sqlStr += "  And (w.status = 0 )"
	}

	if len(params.City) > 0 {
		sqlStr += fmt.Sprintf("  And (w.city like '%s' )", "%"+params.City+"%")
	}
	sqlStr += " GROUP BY w.id ORDER BY w.createdate DESC "
	countSql = fmt.Sprintf(countSql, sqlStr)

	index := params.Pageindex
	size := params.Pagesize
	if index <= 0 {
		index = 1
	}

	if index >= 0 && size > 0 {
		indexCount := (index - 1) * size
		sqlStr += " limit ? , ?"
		strParams = append(strParams, indexCount, size)
	}

	if _, err := Engine.SQL(countSql).Get(&res.TotalCount); err != nil {
		res.Code = 400
		res.Message = err.Error()
		return res, nil
	}

	if res.TotalCount > 0 {
		var list []*models.NewWarehouse
		if err := Engine.SQL(sqlStr, strParams...).Find(&list); err != nil {
			res.Code = 400
			res.Message = err.Error()
			return res, nil
		}

		for _, v := range list {
			var model proto.WarehouseList
			model.Id = int32(v.Id)
			model.Thirdid = v.Thirdid
			model.Code = v.Code
			model.Name = v.Name
			model.Comefrom = int32(v.Comefrom)
			model.Level = int32(v.Level)
			model.Category = int32(v.Category)
			model.Address = v.Address
			model.Contacts = v.Contacts
			model.Tel = v.Tel
			model.Status = int32(v.Status)
			model.Createdate = v.Createdate.Format("2006-01-02 15:04:05")
			model.Subsystem = int32(v.Subsystem)
			model.Ratio = int32(v.Ratio)
			model.Lng = int64(v.Lng)
			model.Lat = int64(v.Lat)
			model.Region = v.Region
			model.City = v.City
			model.Number = int32(v.Number)
			if model.Category == 3 && model.Status == 1 {
				model.Number += 1
			}
			//如果是前置仓，需要把关联的门店信息查询出来
			if model.Number > 0 {
				if err := Engine.Table("warehouse_relationship").Alias("r").Join("inner", "datacenter.store s", "r.shop_id = s.finance_code").
					Where("r.warehouse_id=?", model.Id).Select("s.name").Find(&model.WarehouseInfo); err != nil {
					res.Code = 400
					res.Message = err.Error()
					return res, nil
				}
			}
			res.WarehouseAarray = append(res.WarehouseAarray, &model)
		}
	}
	res.Code = 200
	res.Message = "ok"
	return res, nil
}

// 变更仓库状态
func (s *WarehouseService) UpdateWarehouseStatus(ctx context.Context, params *proto.UpdateWarehouseStatusRequest) (*proto.BaseResponse, error) {
	res := &proto.BaseResponse{}
	status, error := strconv.Atoi(params.Status)
	if error != nil {
		res.Code = 400
		res.Message = "状态数据类型不是数字"
		return res, nil
	}

	// 查找数据库中仓库信息
	model := s.GetWarehouseModel(int32(params.Id))

	if status == 0 {
		//仓库改为禁用，清除该仓库的商品和库存，redis和数据库
		session := Engine.NewSession()
		session.Begin()
		defer session.Close()

		//数据存在
		if model.Id > 0 {
			redisconn := utils.ConnectClusterRedis()
			defer redisconn.Close()
			_, err := session.Exec("update warehouse set status=? where id=?", status, params.Id)
			if err != nil {
				session.Rollback()
				res.Code = 500
				res.Message = "更新数据出错"
				return res, nil
			}
			//删除数据库仓库的商品和库存
			session.Exec("delete from dc_order.warehouse_goods where warehouse_id=" + cast.ToString(model.Id))

			// 清除渠道绑定关系
			var warehouseRelationShopList []models.WarehouseRelationShop
			session.Where(" warehouse_id = ? ", model.Id).Find(&warehouseRelationShopList)
			shopIds := make([]string, 0, len(warehouseRelationShopList))
			for _, v := range warehouseRelationShopList {
				shopIds = append(shopIds, v.ShopId)
			}
			if len(warehouseRelationShopList) > 0 {
				s.delWarehouseChannelRelationShop(session, model.Id, shopIds)
			}

			//查询当前仓库下有库存的数据
			warehouseGoodsList := make([]models.WarehouseGoods, 0)
			session.Where("warehouse_id=?", model.Id).Find(&warehouseGoodsList)
			pipe := redisconn.Pipeline()
			l := 0
			//循环所有的商品去批量删除redis数据
			for _, r := range warehouseGoodsList {
				pipe.HDel("stock:"+r.Goodsid, fmt.Sprintf("%d", model.Id))
				l++
				if l == 30 {
					pipe.Exec()
					l = 0
				}
			}
			if l != 0 {
				pipe.Exec()
			}
			//key := "stock:*:" + strconv.Itoa(model.Id)
			////删除redis仓库的商品和库存
			//utils.RedisDealKeys(key)
		}
		// 禁用后去掉用户仓库权限
		_, err := session.Exec("delete from sc_stock.auth_permission where warehouse_id = ?", params.Id)
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Message = "禁用失败"
			res.Error = "禁用去掉用户仓库权限失败"
			return res, nil
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Message = "更新数据出错"
			return res, nil
		}
		// 仓库操作日志
		grpcContext := s.LoadGrpcContext(ctx) //grpcContext信息
		s.AddWarehouseLog(fmt.Sprintf("禁用%s", model.Name), grpcContext.UserInfo)
	} else {
		if status != 1 {
			res.Code = 400
			res.Message = "启用数字必须是1"
			return res, nil
		}
		//判断同一类型下是否有启用的等级
		if s.IsExistWarehouse(model) && model.Category == 1 {
			res.Code = 400
			res.Message = "同仓库归属，存在同仓库等级启用数据"
			return res, nil
		}

		session := s.GetConn().NewSession()
		session.Begin()
		defer session.Close()

		model.Status = 1
		_, err := session.Id(params.Id).Cols("status").Update(&model)
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Message = "事务修改失败"
			return res, nil
		}
		err = session.Commit()
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Message = "事务提交失败"
			return res, nil
		}

		//Engine.Exec("update warehouse set status=? where id=?", status, params.Id)
		// 仓库操作日志
		grpcContext := s.LoadGrpcContext(ctx) //grpcContext信息
		s.AddWarehouseLog(fmt.Sprintf("启用%s", model.Name), grpcContext.UserInfo)
	}

	res.Code = 200
	res.Message = "ok"
	return res, nil
}

// AddWarehouse 新增仓库
func (w *WarehouseService) AddWarehouse(ctx context.Context, params *proto.AddWarehouseRequest) (*proto.BaseResponse, error) {
	res := new(proto.BaseResponse)
	res.Code = 200
	res.Message = "Success"

	if params.Comefrom == 2 && (params.Ratio <= 0 || params.Ratio > 100) {
		res.Code = 400
		res.Message = "仓库比例不正确"
		return res, nil
	}
	dbConn := w.GetConn()
	defer dbConn.Close()
	//设置时间的时区
	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海
	model := new(models.Warehouse)
	model.Thirdid = params.Thirdid
	model.Code = params.Code
	model.Name = params.Name
	model.Comefrom = int(params.Comefrom)
	model.Level = int(params.Level)
	model.Category = int(params.Category)
	model.Address = params.Address
	model.Contacts = params.Contacts
	model.Tel = params.Tel
	model.Status = 1
	model.Createdate = time.Now().In(cstSh)
	model.Lastdate = time.Now().In(cstSh)
	model.Subsystem = int(params.Subsystem)
	model.Ratio = int(params.Ratio)
	model.Lng = int(params.Lng)
	model.Lat = int(params.Lat)
	model.Region = params.Region
	model.City = params.City
	// model.SellDrugs = int(params.SellDrugs) v6.27.1 删除了
	//验证重复提交
	if strings.TrimSpace(params.Code) == "" {
		res.Code = 400
		res.Message = "仓库不能为空"
		return res, nil
	}

	if len(params.Code) > 0 {
		//判断仓库信息是否存在：
		//注：前置仓和前置仓虚拟仓，两个分类下不能重复。
		// 非前置仓和前置仓虚拟仓 只需要判断当前分类是否存在
		result := w.GetWarehouseByCondition(*model)
		if result.Id > 0 {
			res.Code = 400
			res.Message = "仓库已存在，请重新添加"
			return res, nil
		}

		//验证该仓库是否绑定了前置仓或前置虚拟仓,如果绑定了，则返回400，给出提示，已绑定前置仓或前置虚拟仓
		var wrdata []models.Warehouse
		dbConn.SQL("SELECT w.*  FROM `warehouse` w  INNER JOIN `warehouse_relationship` wr ON w.`id`=wr.`warehouse_id` WHERE w.`category` >= 4 AND wr.`shop_id` = ? ", params.Code).Find(&wrdata)
		for _, wr := range wrdata {
			if wr.Category == int(params.Category) {
				res.Code = 400
				if wr.Category == 5 {
					res.Message = "仓库已绑定前置仓-虚拟仓，请检查后再试"
				} else {
					res.Message = "仓库已绑定前置仓，请检查后再试"
				}
				return res, nil
			}
		}
	}
	if w.IsExistWarehouse(*model) && params.Category == 1 {
		res.Code = 400
		res.Message = "同仓库归属，存在同仓库等级启用数据,或者本仓库已经存在,或仓库编码已存在"
		return res, nil
	}

	//仓库对应配送的配置
	warehouseDeliveryList := make([]models.WarehouseDeliveryRelation, 0)
	for _, k := range params.WarehouseDelivery {
		item := models.WarehouseDeliveryRelation{}
		item.WarehouseId = model.Id
		item.ShopNo = k.ShopNo
		item.DeliveryId = cast.ToInt(k.DeliveryId)
		warehouseDeliveryList = append(warehouseDeliveryList, item)
	}

	session := dbConn.NewSession()
	session.Begin()

	if _, err := session.Insert(model); err != nil {
		res.Code = 400
		session.Rollback()
		res.Error = err.Error()
		return res, nil
	}

	if len(warehouseDeliveryList) > 0 {
		_, err := session.Insert(warehouseDeliveryList)
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Error = err.Error()
			res.Message = err.Error()
			return res, nil
		}
	}
	session.Commit()
	// 如果没有需要忽略日志则可以记录仓库操作日志
	if ctx.Value(SkipWarehouseLog{}) == nil {
		grpcContext := w.LoadGrpcContext(ctx) //grpcContext信息
		w.AddWarehouseLog(fmt.Sprintf("新建%s仓库", model.Name), grpcContext.UserInfo)
	}

	//存Redis
	//var client = utils.ConnectClusterRedis()
	//defer client.Close()
	//if model.Category == 3 {
	//	// 更新店铺仓库缓存
	//	w.buildWarehouseRelationCache(dbConn, client, model.Code)
	//}
	//patternkeys := fmt.Sprintf("warehouse:%d:*", model.Comefrom)
	//utils.RedisDealKeys(patternkeys)
	return res, nil
}

// /根据参数获取Warehouse对象
func (w *WarehouseService) GetWarehouseByCondition(condition models.Warehouse) models.Warehouse {
	dbConn := w.GetConn()
	defer dbConn.Close()
	var result models.Warehouse
	if condition.Category == 4 || condition.Category == 5 {
		dbConn.Where("code=? and category in(4,5)", condition.Code).Get(&result)
	} else {
		dbConn.Where("code=? and category=?", condition.Code, condition.Category).Get(&result)
	}

	return result
}

// 根据仓库ID获取仓库数据
func (w *WarehouseService) GetWarehouseById(ctx context.Context, params *proto.GetWarehouseByIdRequest) (*proto.GetWarehouseByIdResponse, error) {
	res := &proto.GetWarehouseByIdResponse{}
	resItem := make([]models.WarehouseDeliveryConfig, 0)
	Engine.SQL("SELECT b.*,a.shop_no FROM dc_dispatch.warehouse_delivery_relation a INNER JOIN datacenter.delivery_config b ON a.delivery_id=b.id where a.warehouse_id=?", params.Id).Find(&resItem)
	model := models.Warehouse{}
	Engine.Where("id = ?", params.Id).Get(&model)
	res.Warehousecode = model.Code
	res.Id = int32(model.Id)
	res.Name = model.Name
	res.Comefrom = int32(model.Comefrom)
	res.Level = int32(model.Level)
	res.Category = int32(model.Category)
	res.Address = model.Address

	res.Contacts = model.Contacts
	res.Tel = model.Tel
	res.Status = int32(model.Status)
	res.Createdate = model.Createdate.Format("2006-01-02 15:04:05")
	res.Thirdid = model.Thirdid
	res.Subsystem = int32(model.Subsystem)
	res.Ratio = int32(model.Ratio)
	res.Lng = int64(model.Lng)
	res.Lat = int64(model.Lat)
	res.Region = model.Region
	res.City = model.City
	// res.SellDrugs = int32(model.SellDrugs)  v6.27.1 删除了

	for _, k := range resItem {
		item := proto.WarehouseDeliveryInfo{}
		item.DeliveryType = int32(k.DeliveryType)
		item.ShopNo = k.ShopNo
		item.Id = int32(k.Id)
		item.DeliveryName = k.DeliveryName
		item.AppKey = k.AppKey
		item.AppSecret = k.AppSecret
		item.ScoureId = k.SourceId
		res.WarehouseDeliveryList = append(res.WarehouseDeliveryList, &item)
	}
	//查询关联门店信息
	Engine.SQL(`select shop_name,group_concat(case 
		when channel_id = 1 then '阿闻渠道-外卖'
		when channel_id = 2 then '美团'
		when channel_id = 3 then '饿了么'
		when channel_id = 4 then '京东到家'
		when channel_id = 9 then '互联网医院'
		when channel_id = 10 then '阿闻渠道-自提'
		else '' end order by channel_id asc) as channel_names
from warehouse_relation_shop where warehouse_id = ? group by shop_id;`, params.Id).Find(&res.RelationShopList)

	res.Code = 200
	res.Message = "ok"
	return res, nil
}

// 根据仓库ID获取仓库实体数据
func (s *WarehouseService) GetWarehouseModel(id int32) models.Warehouse {
	var model models.Warehouse
	Engine.SQL("SELECT id,CODE,NAME,comefrom,LEVEL,category,address,contacts,tel,STATUS,createdate,thirdid,Lastdate,Subsystem,Lng,Lat FROM warehouse where id=?", id).Get(&model)
	return model
}

func (w *WarehouseService) GetWarehouseByThirdId(thirdId string, comeFrom int) models.Warehouse {
	var model models.Warehouse
	Engine.SQL("SELECT * FROM warehouse  WHERE comefrom=1  and `thirdid`=?", comeFrom, thirdId).Get(&model)
	return model
}

// 根据参数判断是否存在启用数据
func (s *WarehouseService) IsExistWarehouse(model models.Warehouse) bool {
	var sql strings.Builder
	var has bool
	if model.Comefrom == 1 {
		sql.WriteString("SELECT * FROM warehouse  WHERE comefrom=? AND STATUS=1 AND (category=? OR thirdid=?) AND (LEVEL=? or thirdid=?)")
		has, _ = Engine.SQL(sql.String(),
			model.Comefrom, model.Category, model.Thirdid, model.Level, model.Thirdid).Exist()
	} else if model.Comefrom == 2 {
		sql.WriteString("SELECT * FROM warehouse  WHERE (comefrom=? AND STATUS=1 AND category=? AND LEVEL=?) OR ")
		sql.WriteString("(comefrom=? AND thirdid=? AND id != ?)")
		has, _ = Engine.SQL(sql.String(),
			model.Comefrom, model.Category, model.Level, model.Comefrom, model.Thirdid, model.Id).Exist()
		//if !has {
		//	has, _ = Engine.SQL("SELECT * FROM warehouse  WHERE comefrom=? AND thirdid=? AND id != ?",
		//		model.Comefrom, model.Thirdid, model.Id).Exist()
		//}
	}

	return has
}

// 根据参数判断是否存在启用数据，不包含传入ID行
func (s *WarehouseService) IsExistWarehouseExcludeId(model models.Warehouse) bool {
	var sql strings.Builder

	var has bool
	if model.Comefrom == 1 {
		sql.WriteString("SELECT * FROM warehouse  WHERE comefrom=? AND id !=?  AND STATUS=1 AND (category=? OR thirdid=?) AND (LEVEL=?  or thirdid=?) ")
		has, _ = Engine.SQL(sql.String(),
			model.Comefrom, model.Id, model.Category, model.Thirdid, model.Level, model.Thirdid).Exist()
	} else if model.Comefrom == 2 {
		sql.WriteString("SELECT * FROM warehouse  WHERE (comefrom=? AND id !=? AND STATUS=1 AND category=? AND LEVEL=?) ")
		sql.WriteString("OR (comefrom=? AND id !=? AND thirdid=?)")
		has, _ = Engine.SQL(sql.String(),
			model.Comefrom, model.Id, model.Category, model.Level, model.Comefrom, model.Id, model.Thirdid).Exist()
		//if !has {
		//	has, _ = Engine.SQL("SELECT * FROM warehouse  WHERE comefrom=? AND id !=? AND thirdid=?",
		//		model.Comefrom, model.Id, model.Thirdid).Exist()
		//}
	}

	return has
}

// 新增 仓库配送地址
func (w *WarehouseService) AddWarehouseArea(ctx context.Context, params *proto.AddWarehouseAreaRequest) (*proto.BaseResponse, error) {
	//设置时间的时区
	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海

	//定义返回信息
	var out = new(proto.BaseResponse)
	out.Code = 200
	out.Message = "Success"
	dbConn := w.GetConn()
	defer dbConn.Close()
	tran := dbConn.NewSession()
	defer tran.Close()

	_, err := tran.Exec("DELETE FROM warehouse_area WHERE warehouseid=? ", params.Warehouseid)
	if err != nil {
		out.Code = 400
		out.Error = err.Error()
		out.Message = err.Error()
		tran.Rollback()
		return out, nil
	}
	var areas []models.WarehouseArea

	if len(params.WarehouseAreaList) > 0 {
		for i := 0; i < len(params.WarehouseAreaList); i++ {
			model := params.WarehouseAreaList[i]
			var area models.WarehouseArea
			area.Areaid = int(model.Areaid)
			area.Warehouseid = int(model.Warehouseid)
			area.Level = int(model.Level)
			area.Lastdate = time.Now().In(cstSh)
			areas = append(areas, area)

		}
		_, err = tran.Insert(&areas)
		if err != nil {
			out.Code = 500
			out.Error = err.Error()
			out.Message = err.Error()
			tran.Rollback()
			return out, nil
		}
	}

	err = tran.Commit()
	w.SaveWarehouseToRedis(ctx, int(params.Warehouseid))
	patternkeys := fmt.Sprintf("warehouse:*:*")
	utils.RedisDealKeys(patternkeys)
	return out, nil

}

// 编辑仓库信息
func (w *WarehouseService) EditWarehouse(ctx context.Context, params *proto.EditWarehouseRequest) (*proto.BaseResponse, error) {
	res := new(proto.BaseResponse)
	res.Code = 200
	res.Message = "Success"

	if params.Comefrom == 2 && (params.Ratio <= 0 || params.Ratio > 100) {
		res.Code = 400
		res.Message = "仓库比例不正确"
		return res, nil
	}

	var model models.Warehouse
	//设置时间的时区
	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海
	var err error
	warehouse := w.GetWarehouseModel(int32(params.Id))
	model = models.Warehouse{
		Id:         int(params.Id),
		Code:       warehouse.Code,
		Name:       params.Name,
		Comefrom:   warehouse.Comefrom,
		Level:      int(params.Level),
		Address:    params.Address,
		Contacts:   params.Contacts,
		Tel:        params.Tel,
		Status:     warehouse.Status,
		Thirdid:    warehouse.Thirdid,
		Createdate: warehouse.Createdate,
		Subsystem:  int(params.Subsystem),
		Lastdate:   time.Now().In(cstSh),
		Lng:        int(params.Lng),
		Lat:        int(params.Lat),
		Region:     params.Region,
		City:       params.City,
		// SellDrugs:  int(params.SellDrugs),  v6.27.1 删除了
	}
	if params.Comefrom == 1 { //全渠道

	} else if params.Comefrom == 2 { //管易
		model.Ratio = int(params.Ratio)
	} else if params.Comefrom == 3 { //门店
		model.Category = 3 //仓库类型是门店仓
	}

	if w.IsExistWarehouseExcludeId(model) && params.Category == 1 {
		res.Code = 400
		res.Message = "同仓库归属，存在同仓库等级启用数据"
		return res, nil
	}
	//仓库对应配送的配置
	warehouseDeliveryList := make([]models.WarehouseDeliveryRelation, 0)
	for _, k := range params.WarehouseDelivery {
		item := models.WarehouseDeliveryRelation{}
		item.WarehouseId = model.Id
		item.ShopNo = k.ShopNo
		item.DeliveryId = cast.ToInt(k.DeliveryId)
		warehouseDeliveryList = append(warehouseDeliveryList, item)
	}

	session := Engine.NewSession()
	session.Begin()

	_, err = session.Id(model.Id).Update(&model)
	if err != nil {
		session.Rollback()
		res.Code = 400
		res.Error = err.Error()
		res.Message = err.Error()
	}

	_, err = session.Exec("DELETE FROM warehouse_delivery_relation WHERE warehouse_id=?", model.Id)
	if err != nil {
		session.Rollback()
		res.Code = 400
		res.Error = err.Error()
		res.Message = err.Error()
	}

	if len(warehouseDeliveryList) > 0 {
		_, err = session.Insert(warehouseDeliveryList)
		if err != nil {
			session.Rollback()
			res.Code = 400
			res.Error = err.Error()
			res.Message = err.Error()
		}
	}
	session.Commit()
	// 仓库操作日志
	var content string
	if int(params.Level) != warehouse.Level {
		content += fmt.Sprintf("仓库等级%d级改为%d级，", warehouse.Level, params.Level)
	}
	if params.Name != warehouse.Name {
		content += fmt.Sprintf("仓库名称%s改为%s，", warehouse.Name, params.Name)
	}
	if params.Address != warehouse.Address {
		content += fmt.Sprintf("地址%s改为%s，", warehouse.Address, params.Address)
	}
	if params.Contacts != warehouse.Contacts {
		content += fmt.Sprintf("仓库联系人%s改为%s，", warehouse.Contacts, params.Contacts)
	}
	if params.Tel != warehouse.Tel {
		content += fmt.Sprintf("仓库联系方式%s改为%s，", warehouse.Tel, params.Tel)
	}
	if int(params.Subsystem) != warehouse.Subsystem {
		content += fmt.Sprintf("所属系统%d改为%d，", warehouse.Subsystem, params.Subsystem)
	}
	if int(params.Lng) != warehouse.Lng {
		content += fmt.Sprintf("仓库经度%d改为%d，", warehouse.Lng, params.Lng)
	}
	if int(params.Lat) != warehouse.Lat {
		content += fmt.Sprintf("仓库纬度%d改为%d，", warehouse.Lat, params.Lat)
	}
	// if int(params.SellDrugs) != warehouse.SellDrugs {
	// 	content += fmt.Sprintf("仓库售药资质%d改为%d，", warehouse.SellDrugs, params.SellDrugs)
	// }
	// 仓库操作日志
	if content != "" && ctx.Value(SkipWarehouseLog{}) == nil {
		content = fmt.Sprintf("编辑%s仓库，将", model.Name) + content
		grpcContext := w.LoadGrpcContext(ctx) //grpcContext信息
		w.AddWarehouseLog(content, grpcContext.UserInfo)
	}

	//存Redis
	patternkeys := fmt.Sprintf("warehouse:%d:*", model.Comefrom)
	utils.RedisDealKeys(patternkeys)

	// 售药资质变成否，自动下架前置仓关联门店的药品 v6.27.1 前置仓售药资质变更下架g
	// 	if params.SellDrugs == 0 && warehouse.SellDrugs > 0 && warehouse.Category == 4 {
	// 		go func() {
	// 			var shopIds []string
	// 			if err = Engine.Table("warehouse_relation_shop").Where("channel_id = 1 and warehouse_id = ?", warehouse.Id).Select("shop_id").Find(&shopIds); err != nil {
	// 				glog.Warning("EditWarehouse 下架前置仓药品出错：", warehouse.Id, err.Error())
	// 			}
	// 			if len(shopIds) == 0 {
	// 				return
	// 			}
	// 			// 药品关联的商品
	// 			var skuIds []string
	// 			if err := Engine.Table("dc_product.product").Alias("p").
	// 				Join("inner", "dc_product.sku s", "s.product_id = p.id").Where("p.is_drugs = 1").
	// 				Select("s.id").Find(&skuIds); err != nil {
	// 				glog.Warning("EditWarehouse 下架前置仓药品出错：", warehouse.Id, err.Error())
	// 			} else if len(skuIds) == 0 {
	// 				return
	// 			} else {
	// 				var groupSkuIds []string
	// 				if err := Engine.Table("dc_product.sku_group").In("group_sku_id", skuIds).Select("sku_id").Find(&groupSkuIds); err != nil {
	// 					glog.Warning("EditWarehouse 下架前置仓药品出错：", warehouse.Id, err.Error())
	// 				} else if len(groupSkuIds) > 0 {
	// 					skuIds = append(skuIds, groupSkuIds...)
	// 				}
	// 			}

	// 			// 参考 channel_product_up DownPorudct() 逻辑 v6.27.1
	// 			if _, err := Engine.Exec(`update dc_product.channel_store_product sp
	// left join dc_product.channel_store_product_has_stock h on h.channel_store_product_id = sp.id
	// set sp.up_down_state = 0,sp.down_type = 8,h.has_stock_up = 0
	// where sp.channel_id = 1 and sp.up_down_state = 1 and sp.finance_code in ('` + strings.Join(shopIds, "','") + `') and
	// sp.sku_id in (` + strings.Join(skuIds, ",") + `)`); err != nil {
	// 				glog.Warning("EditWarehouse 下架前置仓药品出错：", warehouse.Id, err.Error())
	// 			}
	// 		}()
	// 	}

	return res, nil
}

// 获取仓库类型
func (w *WarehouseService) GetWarehouseType(ctx context.Context, empty *proto.Empty) (*proto.GetWarehouseTypeResponse, error) {

	var result = new(proto.GetWarehouseTypeResponse)
	result.Code = 200
	var warehouseTypes []*proto.WarehouseType
	var model = new(proto.WarehouseType)
	model.Id = 1
	model.Name = "电商仓"

	var mode2 = new(proto.WarehouseType)
	mode2.Id = 2
	mode2.Name = "区域仓"

	var mode3 = new(proto.WarehouseType)
	mode3.Id = 3
	mode3.Name = "门店仓"

	var mode4 = new(proto.WarehouseType)
	mode4.Id = 4
	mode4.Name = "前置仓"

	var mode5 = new(proto.WarehouseType)
	mode5.Id = 5
	mode5.Name = "前置仓虚拟仓"

	warehouseTypes = append(warehouseTypes, model)
	warehouseTypes = append(warehouseTypes, mode2)
	warehouseTypes = append(warehouseTypes, mode3)
	warehouseTypes = append(warehouseTypes, mode4)
	warehouseTypes = append(warehouseTypes, mode5)

	result.WarehouseTypes = warehouseTypes
	return result, nil
}

// 获取仓库级别
func (w *WarehouseService) GetWarehouseLevel(ctx context.Context, empty *proto.Empty) (*proto.GetWarehouseLevelResponse, error) {
	var result = new(proto.GetWarehouseLevelResponse)
	result.Code = 200
	var warehouseLevels []*proto.WarehouseLevel

	var model1 = new(proto.WarehouseLevel)
	model1.Id = 1
	model1.Name = "一级"

	var model2 = new(proto.WarehouseLevel)
	model2.Id = 2
	model2.Name = "二级"

	var model3 = new(proto.WarehouseLevel)
	model3.Id = 3
	model3.Name = "三级"

	var model4 = new(proto.WarehouseLevel)
	model4.Id = 4
	model4.Name = "四级"

	var model5 = new(proto.WarehouseLevel)
	model5.Id = 5
	model5.Name = "五级"

	warehouseLevels = append(warehouseLevels, model1)
	warehouseLevels = append(warehouseLevels, model2)
	warehouseLevels = append(warehouseLevels, model3)
	warehouseLevels = append(warehouseLevels, model4)
	warehouseLevels = append(warehouseLevels, model5)

	result.WarehouseLevels = warehouseLevels
	return result, nil
}

func (w *WarehouseService) SaveWarehouseToRedis(ctx context.Context, warehouseId int) {
	params := new(proto.BaseAreaRequest)
	params.Areadeep = 1
	res, err := w.BaseArea(ctx, params)

	dbConn := w.GetConn()
	defer dbConn.Close()

	var comefrom int64
	_, err = dbConn.SQL("SELECT  comefrom  FROM  warehouse WHERE id = ?", warehouseId).Cols("comefrom").Get(&comefrom)

	if err != nil {
		return
	}

	if err != nil {
		glog.Error(err)
	}
	if res.Code != 200 {
		return

	}
	if len(res.Regionarray) <= 0 {
		glog.Error("区域对应仓库写入失败! 大区不存在！")
		return
	}

	var provinces []*proto.ProvinceList

	for _, item := range res.Regionarray {

		for _, model := range item.Children {
			provinces = append(provinces, model)
		}
	}

	if len(provinces) <= 0 {
		glog.Error("区域对应仓库写入失败! 省份不存在！")
		return
	}
	redisConn := utils.ConnectClusterRedis()
	defer redisConn.Close()
	redisPipeline := redisConn.Pipeline()
	defer redisPipeline.Close()

	for _, item := range provinces {
		keyStr := fmt.Sprintf("s2b2c:warehouse_area:%d", comefrom)
		filed := strconv.Itoa(int(item.Areaid))
		//del key
		redisPipeline.HDel(keyStr, filed)
		ids := ""
		result := w.GetWareHouseByAreaId(int(comefrom), int(item.Areaid))
		if len(result) > 0 {
			for _, id := range result {
				ids = ids + strconv.FormatInt(id, 10) + ","
			}
		}
		ids = strings.Trim(strings.TrimSpace(ids), ",")
		if len(ids) > 0 {
			redisPipeline.HSet(keyStr, filed, ids)
		}

	}
	_, err = redisPipeline.Exec()
	if err != nil {
		panic(err)
	}
}

func (w *WarehouseService) GetWareHouseByAreaId(comefrom int, areaId int) []int64 {
	var ints []int64

	var warehouseModels []models.Warehouse

	dbConn := w.GetConn()
	defer dbConn.Close()

	sql := `SELECT  a.id,a.code,a.name,a.comefrom,a.level,a.category,a.Address,a.Contacts,a.tel,a.status,a.status,a.createdate
			FROM warehouse a 
			JOIN warehouse_area b ON  b.warehouseid=a.id
			WHERE a.status=1  AND a.comefrom=?  AND b.areaid=?
			ORDER BY b.level,a.level `

	err := dbConn.SQL(sql, comefrom, areaId).Cols("id").Find(&warehouseModels)
	if err != nil {
		glog.Error("GetWareHouseByAreaId:" + sql)
	}
	if len(warehouseModels) > 0 {
		for _, item := range warehouseModels {
			ints = append(ints, int64(item.Id))
		}
	}
	return ints

}

func (w *WarehouseService) GetWarehouseByArea(ctx context.Context, params *proto.WarehouseByAreaRequest) (*proto.WarehouseByAreaResponse, error) {
	var out = new(proto.WarehouseByAreaResponse)
	out.Code = 200
	out.Message = "Success"
	sql := `SELECT  a.id,a.code,a.name,a.comefrom,a.level,a.category,a.Address,a.Contacts,a.tel,a.status,a.createdate
			FROM warehouse a 
			INNER JOIN warehouse_area b ON  b.warehouseid=a.id
			WHERE a.status=1  AND a.comefrom=?  AND b.areaid=?
			ORDER BY b.level,a.level `
	var warehouses []*proto.WarehouseList
	engine := w.GetConn()
	defer engine.Close()
	engine.SQL(sql, params.Comefrom, params.Province).Find(&warehouses)
	out.WarehouseAarray = warehouses
	return out, nil
}

type WarehouseIdModel struct {
	Id int `xorm`
}

// 关联店铺
func (w *WarehouseService) WarehouseRelation(ctx context.Context, in *proto.WarehouseRelationRequest) (*proto.BaseResponse, error) {
	glog.Infof("WarehouseService/WarehouseRelation 请求参数 %s", kit.JsonEncode(in))

	var result = &proto.BaseResponse{
		Code: http.StatusBadRequest,
	}
	// 验证入参参数
	if in.WarehouseId == 0 {
		result.Message = "关联的仓库id为空"
		return result, nil
	}

	// 第一步：查询仓库所有绑定关系
	// 第二步，比对出那些门店需要新增和删除
	var client = utils.ConnectClusterRedis()
	defer client.Close()

	conn := w.GetConn()
	defer conn.Close()

	// 查询仓库信息
	var warehouse models.Warehouse
	ok, err := conn.Where("id = ?", in.WarehouseId).Get(&warehouse)
	if err != nil {
		glog.Errorf("WarehouseService/WarehouseRelation 查询仓库信息异常:%+v %d", err, in.WarehouseId)
		result.Message = fmt.Sprintf("查询仓库信息异常:%+v", err)
		return result, nil
	}
	if !ok {
		glog.Errorf("WarehouseService/WarehouseRelation 仓库不存在:%d", in.WarehouseId)
		result.Message = fmt.Sprintf("仓库不存:%d", in.WarehouseId)
		return result, nil
	}

	// 关联仓库前先验证
	err = w.validateWarehouseRelation(conn, warehouse, in.ShopInfoList)
	if err != nil {
		glog.Errorf("WarehouseService/WarehouseRelation %v %d", err, in.WarehouseId)
		result.Message = err.Error()
		return result, nil
	}

	// 数据库中仓库绑定关系
	var relationList []models.WarehouseRelationship
	err = conn.Where("warehouse_id=?", in.WarehouseId).Find(&relationList)
	if err != nil {
		glog.Errorf("WarehouseService/WarehouseRelation 查询仓库与店铺绑定关系异常:%+v %d", err, in.WarehouseId)
		result.Message = fmt.Sprintf("查询仓库与店铺绑定关系异常:%+v", err)
		return result, nil
	}

	// 分别找出新增、删除仓库与店铺绑定关系
	var addShops []*proto.ShopInfo
	oldRelationMap := make(map[string]models.WarehouseRelationship, len(relationList))
	newRelationMap := make(map[string]struct{}, len(in.ShopInfoList))
	for _, v := range relationList {
		oldRelationMap[v.ShopId] = v
	}
	for _, v := range in.ShopInfoList {
		newRelationMap[v.ShopId] = struct{}{}
		if _, ok := oldRelationMap[v.ShopId]; ok {
			delete(oldRelationMap, v.ShopId)
		} else {
			addShops = append(addShops, v)
		}
	}

	// 仓库同类关系，前置仓同类关系是前置虚拟仓，前置虚拟仓同类关系是前置仓
	var anotherWarehouseRelations []models.WarehouseRelationship

	// 开启事务
	_, err = conn.Transaction(func(session *xorm.Session) (interface{}, error) {
		// 仓库操作日志
		var content strings.Builder

		//添加关系
		for _, item := range addShops {
			var add models.WarehouseRelationship
			add.WarehouseId = int(in.WarehouseId)
			add.ShopId = item.ShopId
			_, err := session.Insert(&add)
			if err != nil {
				return nil, err
			}
			content.WriteString(fmt.Sprintf("店铺(%s,%s)绑定仓库(%s,%s)；", item.ShopId, item.ShopName, warehouse.Code, warehouse.Name))
		}

		//删除关系
		if len(oldRelationMap) > 0 {
			// 如果解绑当前仓库是前置仓，解绑前置仓关系时同时需要解绑虚拟仓关系
			if warehouse.Category == 4 {
				delShopIds := make([]string, 0, len(oldRelationMap))
				for shopId := range oldRelationMap {
					delShopIds = append(delShopIds, fmt.Sprintf("'%s'", shopId))
				}
				rawSql := "select wr.* from warehouse_relationship wr left join warehouse w on wr.warehouse_id=w.id where w.category=5 and wr.shop_id in (" + strings.Join(delShopIds, ",") + ")"
				err = session.SQL(rawSql).Find(&anotherWarehouseRelations)
				if err != nil {
					return nil, err
				}
			}

			// 如果解绑当前仓库是前置虚拟仓，解绑前置仓关系时同时需要解绑前置仓关系
			if warehouse.Category == 5 {
				delShopIds := make([]string, 0, len(oldRelationMap))
				for shopId := range oldRelationMap {
					delShopIds = append(delShopIds, fmt.Sprintf("'%s'", shopId))
				}
				rawSql := "select wr.* from warehouse_relationship wr left join warehouse w on wr.warehouse_id=w.id where w.category=4 and wr.shop_id in (" + strings.Join(delShopIds, ",") + ")"
				err = session.SQL(rawSql).Find(&anotherWarehouseRelations)
				if err != nil {
					return nil, err
				}
			}

			ids := make([]int, 0, len(oldRelationMap)+len(anotherWarehouseRelations))
			for k, v := range oldRelationMap {
				ids = append(ids, v.Id)
				content.WriteString(fmt.Sprintf("店铺(%s)手动解绑%s(%s,%s)；", k, enum.WarehouseCategoryMap[warehouse.Category], warehouse.Code, warehouse.Name))
			}
			for _, v := range anotherWarehouseRelations {
				ids = append(ids, v.Id)
				content.WriteString(fmt.Sprintf("店铺(%s)被动解绑仓库(%d)；", v.ShopId, v.WarehouseId))
			}
			_, err = session.In("id", ids).Delete(&models.WarehouseRelationship{})
			if err != nil {
				return nil, err
			}
		}

		if content.Len() > 0 {
			grpcContext := w.LoadGrpcContext(ctx) //grpcContext信息
			w.AddWarehouseLog(content.String(), grpcContext.UserInfo)
		}

		return nil, nil
	})

	if err != nil {
		glog.Errorf("WarehouseService/WarehouseRelation 执行业务处理异常:%+v %d", err, in.WarehouseId)
		result.Message = err.Error()
		return result, nil
	}

	// 更新店铺与仓库关联关系缓存
	//for _, v := range addShops {
	//	w.buildWarehouseRelationCache(conn, client, v.ShopId)
	//}
	//for k := range oldRelationMap {
	//	w.buildWarehouseRelationCache(conn, client, k)
	//}

	session := conn.NewSession()
	defer session.Close()
	// 查询需要删除relationshop关系
	var delRelationShops []models.WarehouseRelationShop
	if len(oldRelationMap) > 0 {
		shopIds := make([]string, 0, len(oldRelationMap))
		for _, v := range oldRelationMap {
			shopIds = append(shopIds, v.ShopId)
		}

		var relationList []models.WarehouseRelationShop
		err = session.Where("warehouse_id = ?", warehouse.Id).In("shop_id", shopIds).Find(&relationList)
		if err != nil {
			glog.Errorf("WarehouseService/WarehouseRelation 查询relationShop关系异常:%+v %s", err, kit.JsonEncode(warehouse))
		}
		delRelationShops = append(delRelationShops, relationList...)
		// 当解绑前置仓或者前置虚拟仓时需要同时删除同类仓库关系
		for _, v := range anotherWarehouseRelations {
			var relationList []models.WarehouseRelationShop
			err = session.Where("warehouse_id = ?", warehouse.Id).In("shop_id", shopIds).Find(&relationList)
			if err != nil {
				glog.Errorf("WarehouseService/WarehouseRelation 查询同类仓库relationShop关系异常:%+v %s", err, kit.JsonEncode(v))
				continue
			}
			delRelationShops = append(delRelationShops, relationList...)
		}
	}

	// 删除仓库各个渠道绑定关系
	if len(oldRelationMap) > 0 {
		shopIds := make([]string, 0, len(oldRelationMap))
		for _, v := range oldRelationMap {
			shopIds = append(shopIds, v.ShopId)
		}

		w.delWarehouseChannelRelationShop(session, warehouse.Id, shopIds)
		// 当解绑前置仓或者前置虚拟仓时需要同时删除同类仓库关系
		for _, v := range anotherWarehouseRelations {
			w.delWarehouseChannelRelationShop(session, v.WarehouseId, shopIds)
		}
	}

	// 记录删除仓库绑定关系日志信息
	go w.asyncLogSwitchWarehouse(ctx, delRelationShops, 3)

	result.Code = http.StatusOK
	result.Message = "Success"
	return result, nil
}

// 记录删除仓库绑定关系日志信息
func (w *WarehouseService) asyncLogSwitchWarehouse(ctx context.Context, list []models.WarehouseRelationShop, action int32) {
	if len(list) == 0 {
		return
	}

	var (
		ipAddr     string
		ipLocation string
	)
	grpcContext := w.LoadGrpcContext(ctx)
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if val := md.Get("ipAddr"); len(val) > 0 {
			ipAddr = val[0]
		}
		if val := md.Get("ipLocation"); len(val) > 0 {
			ipLocation = val[0]
		}
	}

	warehouseIds := make([]int, 0, len(list))
	for _, v := range list {
		warehouseIds = append(warehouseIds, v.WarehouseId)
	}

	conn := w.GetConn()
	defer conn.Close()

	var warehouseList []models.Warehouse
	err := conn.In("id", warehouseIds).Find(&warehouseList)
	if err != nil {
		glog.Errorf("WarehouseService/asyncLogSwitchWarehouse 查询仓库列表异常:%+v %s", err, kit.JsonEncode(list))
	}
	warehouseMap := make(map[int]models.Warehouse, len(warehouseList))
	for _, v := range warehouseList {
		warehouseMap[v.Id] = v
	}

	req := &pc.AddSwitchWarehouseLogRequest{}
	for _, v := range list {
		warehouse := warehouseMap[v.WarehouseId]
		req.List = append(req.List, &pc.SwitchWarehouseLog{
			ChannelId:            int32(v.ChannelId),
			Action:               action,
			FinanceCode:          v.ShopId,
			ShopName:             v.ShopName,
			SrcWarehouseCode:     warehouse.Code,
			SrcWarehouseName:     warehouse.Name,
			SrcWarehouseCategory: int32(warehouse.Category),
			Content:              fmt.Sprintf(`从“关联%s”中将其移除`, enum.WarehouseCategoryMap[warehouse.Category]),
			CreateId:             grpcContext.UserInfo.UserNo,
			CreateTime:           time.Now().Format("2006-01-02 15:04:05"),
			CreateName:           grpcContext.UserInfo.UserName,
			CreateIp:             ipAddr,
			IpLocation:           ipLocation,
		})
	}

	client := pc.GetDcProductClient()
	defer client.Close()
	_, err = client.RPC.AddSwitchWarehouseLog(client.Ctx, req)
	if err != nil {
		glog.Errorf("WarehouseService/asyncLogSwitchWarehouse 新增切仓日志异常:%+v %s", err, kit.JsonEncode(req))
	}
}

func (w *WarehouseService) validateWarehouseRelation(conn *xorm.Engine, warehouse models.Warehouse, list []*proto.ShopInfo) error {
	if len(list) == 0 {
		return nil
	}
	// 判断虚拟仓是不是被其他门店绑定了
	err := func() error {
		if warehouse.Category != 5 {
			return nil
		}

		// 虚拟仓只能被一个店铺绑定
		if len(list) > 1 {
			return errors.New("虚拟仓只能被一个店铺绑定")
		}

		// 查询现有仓库绑定关系
		var relationList []models.Warehouse
		err := conn.SQL("SELECT w.`id`,w.`code`,w.`name`,w.`category` FROM `warehouse` w  INNER JOIN `warehouse_relationship` wr ON w.`id`=wr.`warehouse_id` WHERE wr.`shop_id` = ? ", list[0].ShopId).Find(&relationList)
		if err != nil {
			return fmt.Errorf("查询店铺仓库绑定关系异常:%+v", err)
		}

		// 绑定虚拟仓之前需要确认是否已经绑定前置仓
		isBindQZC := false
		for _, v := range relationList {
			if v.Category == 4 {
				isBindQZC = true
				break
			}
		}
		if !isBindQZC {
			return fmt.Errorf("店铺(%s)未绑定前置仓，不能进行绑定虚拟仓(%s)操作", list[0].ShopId, warehouse.Name)
		}

		// 判断店铺自己是否绑定了其他虚拟仓
		for _, v := range relationList {
			if v.Category == 5 && v.Id != warehouse.Id {
				return fmt.Errorf("店铺(%s)已经绑定其他虚拟仓(%s)，请解绑后再操作", list[0].ShopId, v.Name)
			}
		}

		// 判断虚拟仓是否被别人绑定了
		var financeCode string
		ok, err := conn.SQL("SELECT wr.`shop_id` FROM `warehouse` w  INNER JOIN `warehouse_relationship` wr ON w.`id`=wr.`warehouse_id` WHERE w.`category`=5 AND w.`code` = ? ", warehouse.Code).Get(&financeCode)
		if err != nil {
			return fmt.Errorf("判断虚拟仓是否被别人绑定了异常:%+v", err)
		}
		if !ok {
			return nil
		}
		if financeCode != list[0].ShopId {
			return fmt.Errorf("虚拟仓(%s)已经被其他店铺(%s)绑定，请解绑后再操作", warehouse.Code, financeCode)
		}
		return nil
	}()
	if err != nil {
		return err
	}

	// 一个店铺只能绑定一个电商仓
	err = func() error {
		if warehouse.Category != 1 {
			return nil
		}
		var relationList []models.WarehouseRelationship
		codes := make([]string, 0, len(list))
		for _, v := range list {
			codes = append(codes, fmt.Sprintf("'%s'", v.ShopId))
		}
		err = conn.SQL("SELECT wr.`shop_id`,wr.`warehouse_id`,w.`name` shop_name FROM `warehouse` w  INNER JOIN `warehouse_relationship` wr ON w.`id`=wr.`warehouse_id` WHERE w.`category`=1 AND wr.`shop_id` in (" + strings.Join(codes, ",") + ") ").Find(&relationList)
		if err != nil {
			return fmt.Errorf("查询电商仓关联店铺异常:%+v", err)
		}

		relationMap := make(map[string]models.WarehouseRelationship, len(relationList))
		for _, v := range relationList {
			relationMap[v.ShopId] = v
		}

		for _, v := range list {
			if item, ok := relationMap[v.ShopId]; ok {
				if item.WarehouseId != warehouse.Id {
					return fmt.Errorf("店铺(%s)已关联其他仓库(%s)，一个店铺只能绑定一个电商仓", v.ShopId, item.ShopName)
				}
			}
		}
		return nil
	}()
	if err != nil {
		return err
	}

	// 如果是门店仓则判断是否已经绑定其他同类型仓库
	err = func() error {
		if warehouse.Category != 4 {
			return nil
		}
		var relationList []models.WarehouseRelationship
		codes := make([]string, 0, len(list))
		for _, v := range list {
			codes = append(codes, fmt.Sprintf("'%s'", v.ShopId))
		}
		err = conn.SQL("SELECT wr.`shop_id`,wr.`warehouse_id`,w.`name` shop_name FROM `warehouse` w  INNER JOIN `warehouse_relationship` wr ON w.`id`=wr.`warehouse_id` WHERE w.`category`=4 AND wr.`shop_id` in (" + strings.Join(codes, ",") + ") ").Find(&relationList)
		if err != nil {
			return fmt.Errorf("查询前置仓关联店铺异常:%+v", err)
		}

		relationMap := make(map[string]models.WarehouseRelationship, len(relationList))
		for _, v := range relationList {
			relationMap[v.ShopId] = v
		}

		for _, v := range list {
			if item, ok := relationMap[v.ShopId]; ok {
				if item.WarehouseId != warehouse.Id {
					return fmt.Errorf("店铺(%s)已关联其他仓库(%s)，一个店铺只能绑定一个前置仓", v.ShopId, item.ShopName)
				}
			}
		}
		return nil
	}()
	if err != nil {
		return err
	}

	return nil
}

// 删除仓库各个渠道绑定关系
func (w *WarehouseService) delWarehouseChannelRelationShop(session *xorm.Session, warehouseId int, shopIds []string) {
	var client = utils.ConnectClusterRedis()
	defer client.Close()

	_, err := session.Where("warehouse_id = ?", warehouseId).In("shop_id", shopIds).Delete(&models.WarehouseRelationShop{})
	if err != nil {
		glog.Errorf("WarehouseService/delWarehouseChannelRelationShop 清理仓库渠道绑定关系异常:%+v %d %s", err, warehouseId, kit.JsonEncode(shopIds))
		return
	}

	//更新redis
	delKeys := make([]string, 0, len(shopIds))
	for _, v := range shopIds {
		for channel := range enum.ChannelMap {
			relationShop := utils.LoadChannelWarehouseCache(client, v, channel)
			if relationShop == nil {
				continue
			}
			if relationShop.WarehouseId != int32(warehouseId) {
				continue
			}
			delKeys = append(delKeys, fmt.Sprintf("%s:%d", v, channel))
		}
	}
	if len(delKeys) == 0 {
		return
	}
	_, err = client.HDel("mt_shop_bind_warehouse", delKeys...).Result()
	if err != nil {
		glog.Errorf("WarehouseService/delWarehouseChannelRelationShop 清理仓库渠道缓存关系异常:%+v %d %s", err, warehouseId, kit.JsonEncode(shopIds))
	}
}

func GetInventoryGrpcConn() *grpc.ClientConn {
	grpcAddress := config.GetString("grpc.inventory-center")
	if grpcAddress == "" || runtime.GOOS == "windows" {
		grpcAddress = "127.0.0.1:11007"
	}
	conn, err := grpc.Dial(grpcAddress, grpc.WithInsecure())
	if err != nil {
		//glog.Fatalf("did not connect: %v", err)
	}
	return conn
}

// 请求header添加到grpc上下文
func AppendToOutgoingContextLoginUserInfo(ctx context.Context, c context.Context) context.Context {
	grpcContext := models.GrpcContext{}
	if md, ok := metadata.FromIncomingContext(c); ok {
		if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &grpcContext); err != nil {
			glog.Error(err)
		}
	}
	return metadata.AppendToOutgoingContext(ctx, "grpc_context", kit.JsonEncode(grpcContext))
}

// 区域仓关联前置仓
func (w *WarehouseService) PreposeWarehouseRelation(ctx context.Context, in *proto.PreposeWarehouseRelationRequest) (*proto.BaseResponse, error) {
	var result = new(proto.BaseResponse)
	result.Code = 400
	if in.RegionId <= 0 {
		result.Message = "关联的仓库id为空"
		return result, nil
	}

	// 先删除原有的关联关系，后新增当前的关联关系
	conn := w.GetConn()
	var content, regionName string
	var wareNameList []string
	_, err := conn.Select("name").Table("warehouse").Where("id = ?", in.RegionId).Get(&regionName)
	if err != nil {
		result.Message = "关联前置仓失败"
		result.Error = "查询仓库信息失败：err: " + err.Error()
		return result, nil
	}
	if len(in.PreposeIdList) > 0 {
		err := conn.Table("warehouse").Select("name").In("id", in.PreposeIdList).Find(&wareNameList)
		if err != nil {
			result.Message = "关联前置仓失败"
			result.Error = "查询仓库信息失败：err: " + err.Error()
			return result, nil
		}
		wareName := strings.Join(wareNameList, ",")
		content = fmt.Sprintf("区域仓%s关联了: %s", regionName, wareName)
	} else {
		err := conn.Table("warehouse").Alias("a").
			Join("inner", "sc_stock.warehouse_region b", "a.id=b.warehouse_id").
			Select("name").Where("b.region_id = ?", in.RegionId).Find(&wareNameList)
		if err != nil {
			result.Message = "关联前置仓失败"
			result.Error = "查询仓库信息失败：err: " + err.Error()
			return result, nil
		}
		wareName := strings.Join(wareNameList, ",")
		content = fmt.Sprintf("区域仓%s解绑了: %s", regionName, wareName)
	}
	session := conn.NewSession()
	session.Begin()

	// 删除
	_, err = session.Exec("delete from sc_stock.warehouse_region where region_id = ?;", in.RegionId)
	if err != nil {
		result.Message = "关联前置仓失败"
		result.Error = "删除区域仓关联关系失败：err: " + err.Error()
		session.Rollback()
		return result, nil
	}

	// 新增
	var warehouseRegionList []models.WarehouseRegion
	for _, v := range in.PreposeIdList {
		warehouseRegion := models.WarehouseRegion{
			RegionId:    int(in.RegionId),
			WarehouseId: int(v),
		}
		warehouseRegionList = append(warehouseRegionList, warehouseRegion)
	}
	_, err = session.Table("sc_stock.warehouse_region").Insert(&warehouseRegionList)
	if err != nil {
		result.Message = "关联前置仓失败"
		result.Error = "新增区域仓关联关系失败：err: " + err.Error()
		session.Rollback()
		return result, nil
	}
	if content != "" {
		grpcContext := w.LoadGrpcContext(ctx) //grpcContext信息
		w.AddWarehouseLog(content, grpcContext.UserInfo)
	}
	session.Commit()
	result.Code = 200
	return result, nil
}

// 根据id查询区域仓关联的前置仓信息列表
func (w *WarehouseService) GetPreposeWarehouseRelationList(ctx context.Context, in *proto.WarehouseRelationListRequest) (*proto.RegionRelationListRespon, error) {
	var result = new(proto.RegionRelationListRespon)
	result.Code = 400
	if in.WarehouseId <= 0 {
		result.Msg = "仓库id为空"
		return result, nil
	}

	conn := w.GetConn()
	// 查询关联关系
	var warehouseRegionList = []models.WarehouseRegion{}
	err := conn.Table("sc_stock.warehouse_region").Where("region_id = ?", in.WarehouseId).
		Find(&warehouseRegionList)
	if err != nil {
		result.Msg = "查询区域仓关联关系失败"
		glog.Error("查询区域仓关联关系失败：err: ", err.Error())
		return result, nil
	}

	if len(warehouseRegionList) == 0 {
		result.Data = make([]*proto.PreposeWarehouseInfo, 0)
		result.Code = 200
		return result, nil
	}

	var warehouseIdList []int
	for _, v := range warehouseRegionList {
		warehouseIdList = append(warehouseIdList, v.WarehouseId)
	}

	err = conn.Table("warehouse").Select("id warehouse_id, code warehouse_code, name warehouse_name").
		In("id", warehouseIdList).Find(&result.Data)

	result.Code = 200
	return result, err
}

// 查询前置仓关联的所有城市列表
func (w *WarehouseService) PreposeWarehouseCityList(ctx context.Context, in *proto.Empty) (*proto.PreposeCiytListResponse, error) {
	var result = new(proto.PreposeCiytListResponse)
	result.Code = 400
	conn := w.GetConn()
	err := conn.Table("warehouse").
		Select("city").GroupBy("city").
		Where("comefrom = 1 and category in (4, 5) and status = 1 and city != ''").
		Find(&result.Data)
	if err != nil {
		result.Msg = "查询前置仓信息失败"
		glog.Error("查询前置仓信息失败：err: ", err.Error())
		return result, nil
	}
	result.Code = 200
	return result, nil
}

// 根据仓库id获取已绑定的门店信息列表
func (w *WarehouseService) GetWarehouseRelationList(ctx context.Context, in *proto.WarehouseRelationListRequest) (*proto.WarehouseRelationListResponse, error) {
	var result = new(proto.WarehouseRelationListResponse)
	result.Code = 200
	result.Message = "Success"
	var list []models.WarehouseRelationship
	conn := w.GetConn()
	conn.Where("  warehouse_id = ? ", in.WarehouseId).Find(&list)
	for _, item := range list {
		var model proto.ShopInfo
		model.ShopId = item.ShopId
		model.ShopName = item.ShopName
		result.ShopInfoList = append(result.ShopInfoList, &model)
	}
	return result, nil
}

// 根据财务编码获取仓库类型
func (w *WarehouseService) GetWarehouseInfoByFanceCode(ctx context.Context, in *proto.GetWarehouseInfoByFanceCodeRequest) (*proto.GetWarehouseInfoByFanceCodeResponse, error) {
	glog.Info("GetWarehouseInfoByFanceCodedata FinanceCode : ", in.FinanceCode, " warehouseId: ", in.WarehouseId)
	var result = new(proto.GetWarehouseInfoByFanceCodeResponse)
	result.Code = 200
	result.Message = "Success"
	if len(in.FinanceCode) == 0 {
		result.Code = 400
		result.Message = "请求参数不能为空"
	}
	conn := w.GetConn()
	defer conn.Close()
	var list []models.Warehouse

	if in.WarehouseId > 0 {
		conn.ShowSQL(true)
		conn.Where("id = ?", in.WarehouseId).Find(&list)
	} else {
		if in.ChannelId > 0 {
			var relationShop models.WarehouseRelationShop
			conn.Where("shop_id = ? and channel_id = ?", in.FinanceCode, in.ChannelId).Get(&relationShop)
			if relationShop.Id > 0 {
				conn.Where("id = ?", relationShop.WarehouseId).Find(&list)
			}
		} else {
			var sql strings.Builder
			sql.WriteString(" SELECT ")
			sql.WriteString(" w.`id`,w.`thirdid`,w.`code`,w.`name`,w.`comefrom`,w.`level`,w.`category`,w.`address`,w.`contacts`,w.`tel`,w.`status`,w.`createdate`,w.`lastdate`,w.`subsystem`,w.`ratio`,w.`lng`,w.`lat` ")
			sql.WriteString(" FROM dc_dispatch.warehouse w WHERE w.`status` = 1 AND w.category = 3 AND w.`code` = ? ")
			sql.WriteString(" UNION ALL ")
			sql.WriteString(" SELECT ")
			sql.WriteString(" w.`id`,w.`thirdid`,w.`code`,w.`name`,w.`comefrom`,w.`level`,w.`category`,w.`address`,w.`contacts`,w.`tel`,w.`status`,w.`createdate`,w.`lastdate`,w.`subsystem`,w.`ratio`,w.`lng`,w.`lat` ")
			sql.WriteString(" FROM dc_dispatch.warehouse w INNER JOIN dc_dispatch.warehouse_relationship b ON w.id = b.warehouse_id ")
			sql.WriteString(" WHERE w.category in (4,1) AND w.`status` = 1 AND b.shop_id = ? ")

			_ = conn.SQL(sql.String(), in.FinanceCode, in.FinanceCode).Find(&list)
		}

	}

	glog.Info("查询的仓库信息", list)
	if len(list) == 0 {
		result.Code = 400
		result.Message = "仓库信息不存在，请核实仓库信息！编码：" + in.FinanceCode
		return result, errors.New(result.Message)
	}

	var out proto.WarehouseList
	for _, v := range list {
		if v.Category == 4 || v.Category == 5 || v.Category == 1 {
			out.Id = int32(list[0].Id)
			out.Thirdid = list[0].Thirdid
			out.Name = list[0].Name
			out.Code = list[0].Code
			out.Comefrom = int32(list[0].Comefrom)
			out.Level = int32(list[0].Level)
			out.Category = int32(list[0].Category)
			out.Address = list[0].Address
			out.Contacts = list[0].Contacts
			out.Tel = list[0].Tel
			out.Status = int32(list[0].Status)
			out.Createdate = list[0].Createdate.Format("2006-01-02 15:04:05")
			out.Lastdate = list[0].Lastdate.Format("2006-01-02 15:04:05")
			out.Subsystem = int32(list[0].Subsystem)
			out.Ratio = int32(list[0].Ratio)
			out.Lng = int64(list[0].Lng)
			out.Lat = int64(list[0].Lat)
			result.Warehouseinfo = &out
			break
		} else if v.Category == 3 {
			out.Id = int32(list[0].Id)
			out.Thirdid = list[0].Thirdid
			out.Name = list[0].Name
			out.Code = list[0].Code
			out.Comefrom = int32(list[0].Comefrom)
			out.Level = int32(list[0].Level)
			out.Category = int32(list[0].Category)
			out.Address = list[0].Address
			out.Contacts = list[0].Contacts
			out.Tel = list[0].Tel
			out.Status = int32(list[0].Status)
			out.Createdate = list[0].Createdate.Format("2006-01-02 15:04:05")
			out.Lastdate = list[0].Lastdate.Format("2006-01-02 15:04:05")
			out.Subsystem = int32(list[0].Subsystem)
			out.Ratio = int32(list[0].Ratio)
			out.Lng = int64(list[0].Lng)
			out.Lat = int64(list[0].Lat)
			result.Warehouseinfo = &out
		}
	}

	return result, nil
}

// 根据财务编码批量获取仓库信息
func (w *WarehouseService) GetWarehouseInfoByFanceCodes(ctx context.Context, in *proto.GetWarehouseInfoByFanceCodesRequest) (*proto.GetWarehouseInfoByFanceCodesResponse, error) {
	if len(in.FinanceCode) == 0 {
		return nil, status.Error(codes.Internal, "门店编码不能为空")
	}

	financeCodeStr := "'" + strings.Join(in.FinanceCode, "','") + "'"
	var list []*models.Warehouse

	if in.ChannelId > 0 {
		var relationShops []models.WarehouseRelationShop
		err := Engine.Where("channel_id = ?", in.ChannelId).In("shop_id", in.FinanceCode).Find(&relationShops)

		if err != nil {
			err = errors.New(utils.RunFuncName() + "，数据库查询失败，" + err.Error())
			glog.Errorf("GetWarehouseInfoByFanceCodes %+v %s", err, kit.JsonEncode(in))
			return nil, status.Error(codes.Internal, err.Error())
		}
		var warehouseIds []int
		for _, i := range relationShops {
			warehouseIds = append(warehouseIds, i.WarehouseId)
		}
		err = Engine.In("id", warehouseIds).Find(&list)
		if err != nil {
			err = errors.New(utils.RunFuncName() + "，数据库查询失败，" + err.Error())
			glog.Errorf("GetWarehouseInfoByFanceCodes %+v %s", err, kit.JsonEncode(in))
			return nil, status.Error(codes.Internal, err.Error())
		}
	} else {
		err := Engine.Alias("w").Select("CASE w.category WHEN 3 THEN `code` ELSE wr.shop_id END code,w.id,w.thirdid,w.name,w.comefrom,w.level,w.category,w.address,w.contacts,w.tel,w.status,w.createdate,w.lastdate,w.subsystem,w.ratio,w.lng,w.lat").
			Join("left", "warehouse_relationship wr", "wr.`warehouse_id`=w.`id`").
			Where("w.`status`=1 AND ((w.category = 3 AND w.`code` in(" + financeCodeStr + ")) OR (w.category = 4 AND wr.`shop_id` in(" + financeCodeStr + ")))").
			Find(&list)
		if err != nil {
			err = errors.New(utils.RunFuncName() + "，数据库查询失败，" + err.Error())
			glog.Errorf("GetWarehouseInfoByFanceCodes %+v %s", err, kit.JsonEncode(in))
			return nil, status.Error(codes.Internal, err.Error())
		}
	}

	if len(list) == 0 {
		err := errors.New(utils.RunFuncName() + "，未查询到门店仓库信息查询失败")
		glog.Errorf("GetWarehouseInfoByFanceCodes %+v %s", err, kit.JsonEncode(in))
		return nil, status.Error(codes.NotFound, fmt.Sprintf("门店(%s)查询不到启用状态仓库信息,请确认是否关联仓库或仓库已禁用状态", financeCodeStr))
	}

	var out []*proto.WarehouseList
	for _, v := range list {
		if v.Category != 4 && v.Category != 3 {
			continue
		}
		out = append(out, &proto.WarehouseList{
			Id:         int32(v.Id),
			Thirdid:    v.Thirdid,
			Name:       v.Name,
			Code:       v.Code,
			Comefrom:   int32(v.Comefrom),
			Level:      int32(v.Level),
			Category:   int32(v.Category),
			Address:    v.Address,
			Contacts:   v.Contacts,
			Tel:        v.Tel,
			Status:     int32(v.Status),
			Createdate: v.Createdate.Format("2006-01-02 15:04:05"),
			Lastdate:   v.Lastdate.Format("2006-01-02 15:04:05"),
			Subsystem:  int32(v.Subsystem),
			Ratio:      int32(v.Ratio),
			Lng:        int64(v.Lng),
			Lat:        int64(v.Lat),
		})
	}

	return &proto.GetWarehouseInfoByFanceCodesResponse{
		Data: out,
	}, nil
}

// 根据条件获取仓库信息（V3.1版本的需要）
func (w *WarehouseService) GetWarehouseInfoByCondition(ctx context.Context, in *proto.GetWarehouseInfoByConditionRequest) (*proto.GetWarehouseInfoByConditionResponse, error) {
	var out = new(proto.GetWarehouseInfoByConditionResponse)
	out.Code = 200
	out.Message = "Success"
	list := w.GetWarehouseInfoByConditionCommon(in.FinanceCode, in.Category)
	if len(list) > 0 {
		for _, model := range list {
			outmodel := proto.WarehouseList{}
			outmodel.Id = int32(model.Id)
			outmodel.Thirdid = model.Thirdid
			outmodel.Name = model.Name
			outmodel.Code = model.Code
			outmodel.Comefrom = int32(model.Comefrom)
			outmodel.Level = int32(model.Level)
			outmodel.Category = int32(model.Category)
			outmodel.Address = model.Address
			outmodel.Contacts = model.Contacts
			outmodel.Tel = model.Tel
			outmodel.Status = int32(model.Status)
			outmodel.Createdate = model.Createdate.Format("2006-01-02 15:04:05")
			outmodel.Lastdate = model.Lastdate.Format("2006-01-02 15:04:05")
			outmodel.Subsystem = int32(model.Subsystem)
			outmodel.Ratio = int32(model.Ratio)
			outmodel.Lng = int64(model.Lng)
			outmodel.Lat = int64(model.Lat)
			out.Warehouseinfo = append(out.Warehouseinfo, &outmodel)
		}
	}
	return out, nil
}

// 根据条件获取仓库信息公共部分
func (w *WarehouseService) GetWarehouseInfoByConditionCommon(FinanceCode string, Category int32) []models.Warehouse {
	conn := w.GetConn()
	defer conn.Close()
	var list []models.Warehouse
	var sql strings.Builder
	if Category > 0 {
		if Category == 3 {
			sql.WriteString("SELECT w.`id`,  w.`thirdid`,  w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` FROM warehouse w  WHERE  w.`status`=1 AND w.category = 3")

			if FinanceCode != "" {
				sql.WriteString(" and  w.`code`='" + FinanceCode + "'")
			}
		} else if Category == 4 {
			//sql.WriteString(" select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` from warehouse w where w.`code` in (SELECT  wr.`shop_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")
			if FinanceCode != "" {
				sql.WriteString(" select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` from warehouse w where w.`id` in (SELECT  wr.`warehouse_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4 AND w.`code`='" + FinanceCode + "') ")
			} else {
				sql.WriteString(" select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` from warehouse w where w.`id` in (SELECT  wr.`warehouse_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")
			}

			//sql.WriteString(" select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` from warehouse w where w.`code` in (SELECT  wr.`shop_id` FROM warehouse w  inner join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")

		}
		conn.SQL(sql.String()).Find(&list)
		return list
	} else {

		//sql.WriteString("SELECT w.`id`,  w.`thirdid`,  w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` FROM warehouse w WHERE  w.`status`=1 AND w.category = 3 union all select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat`  from warehouse w where w.`code` in (SELECT  wr.`shop_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")
		if FinanceCode != "" {
			sql.WriteString("SELECT w.`id`,  w.`thirdid`,  w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` FROM warehouse w WHERE  w.`status`=1 AND w.category = 3 and w.`code`='" + FinanceCode + "' union all select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat`  from warehouse w where w.`id` in (SELECT  wr.`warehouse_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4 AND wr.`shop_id`='" + FinanceCode + "') ")
		} else {
			sql.WriteString("SELECT w.`id`,  w.`thirdid`,  w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` FROM warehouse w WHERE  w.`status`=1 AND w.category = 3 union all select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat`  from warehouse w where w.`id` in (SELECT  wr.`warehouse_id` FROM warehouse w  left join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")
		}
		//sql.WriteString("SELECT w.`id`,  w.`thirdid`,  w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat` FROM warehouse w WHERE  w.`status`=1 AND w.category = 3 union all select w.`id`,  w.`thirdid`, w.`code`,  w.`name`,  w.`comefrom`,  w.`level`,  w.`category`,  w.`address`,  w.`contacts`,  w.`tel`,  w.`status`,  w.`createdate`,  w.`lastdate`,  w.`subsystem`,  w.`ratio`,w.`lng`,w.`lat`  from warehouse w where w.`code` in (SELECT  wr.`shop_id` FROM warehouse w  inner join `warehouse_relationship` wr on wr.`warehouse_id`=w.`id` WHERE  w.`status`=1 AND w.category = 4) ")
		conn.SQL(sql.String()).Find(&list)
		return list
	}
}

func (w *WarehouseService) GetStoreListByCategory(ctx context.Context, in *proto.GetStoreListByCategoryRequest) (*proto.GetStoreListByCategoryResponse, error) {
	out := &proto.GetStoreListByCategoryResponse{
		Code: 200,
	}

	query := Engine.Table("warehouse_relation_shop").Alias("r").Join("inner", "warehouse w", "w.id = r.warehouse_id")
	if in.Category == 4 { // 前置（虚拟）仓
		query.Where("w.category in (4,5)")
	} else if in.Category > 0 {
		query.Where("w.category = ?", in.Category)
	}

	if in.ChannelId > 0 {
		query.Where("r.channel_id = ?", in.ChannelId)
	}

	if err := query.Select("distinct r.shop_id").Find(&out.FinanceCode); err != nil {
		out.Code = 400
		out.Message = err.Error()
	}

	return out, nil
}

func (w *WarehouseService) GetWarehouseRelation(ctx context.Context, in *proto.Empty) (*proto.GetWarehouseRelationResponse, error) {
	out := &proto.GetWarehouseRelationResponse{
		Code:    200,
		Message: "",
		Error:   "",
	}

	var list []*proto.WarehouseRelationList
	conn := w.GetConn()
	session := conn.NewSession()
	session.Close()
	session = conn.Table("warehouse_relationship").Alias("wr").Join("LEFT", "warehouse as w", "wr.warehouse_id = w.id")
	count, _ := session.Clone().Count()
	if err := session.Select("w.id, w.name, wr.shop_id").Find(&list); err != nil {
		out.Code = 400
		out.Error = err.Error()
	}
	out.TotalCount = int32(count)
	out.WarehouseRelationAarray = list
	return out, nil
}

// 仓库操作日志
func (w *WarehouseService) AddWarehouseLog(content string, userinfo models.LoginUserInfo) {
	session := Engine.NewSession()
	defer session.Close()

	var add models.WarehouseLog
	add.Content = content
	if len(userinfo.RealName) > 0 {
		add.Name, _ = url.QueryUnescape(userinfo.RealName)
	}
	if len(add.Name) <= 0 {
		add.Name = userinfo.UserNo
	}
	sh, _ := time.LoadLocation("Asia/Shanghai")
	add.Createdate = time.Now().In(sh)
	_, err := session.Insert(&add)
	if err != nil {
		glog.Error("AddWarehouseLog 新增仓库操作日志失败")
	}
}

func (w *WarehouseService) WarehouseLog(ctx context.Context, params *proto.WarehouseLogRequest) (*proto.WarehouseLogResponse, error) {
	response := &proto.WarehouseLogResponse{
		Code:    200,
		Message: "ok",
		Error:   "",
	}

	var data []*proto.WarehouseLog
	conn := w.GetConn()
	session := conn.NewSession()
	defer session.Close()

	// 分页
	page := params.Pageindex
	size := params.Pagesize
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10
	}
	offset := (page - 1) * size

	// 时间
	starttime := params.Starttime
	endtime := params.Endtime
	if len(starttime) < 1 {
		starttime = "2021-06-22 00:00:00"
	}
	if len(endtime) < 1 {
		endtime = fmt.Sprintf("%d-%02d-%02d 23:59:59", time.Now().Year(), time.Now().Month(), time.Now().Day())
	}

	countSql := "SELECT count(1) as total FROM dc_dispatch.`warehouse_log` WHERE createdate BETWEEN  ? AND  ? "
	var total int64
	session.SQL(countSql, starttime, endtime).Get(&total)
	//strSql := "SELECT * FROM dc_dispatch.`warehouse_log` WHERE createdate BETWEEN  ? AND  ? ORDER BY id DESC LIMIT ?, ?"
	strSql := "SELECT * FROM dc_dispatch.`warehouse_log` WHERE id <= (SELECT id FROM dc_dispatch.`warehouse_log` WHERE createdate BETWEEN  ? AND  ? ORDER BY id DESC LIMIT ?, 1 ) AND createdate BETWEEN  ? AND  ? ORDER BY id DESC LIMIT ?"
	session.SQL(strSql, starttime, endtime, offset, starttime, endtime, size).Find(&data)
	response.WarehouseLogAarray = data
	response.TotalCount = int32(total)

	return response, nil
}

func (w *WarehouseService) LoadGrpcContext(ctx context.Context) *models.GrpcContext {
	var GrpcContext models.GrpcContext
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if err := json.Unmarshal([]byte(md.Get("grpc_context")[0]), &GrpcContext); err != nil {
			glog.Error(err)
		}
	} else {
		_, str, isOk := metadata.FromOutgoingContextRaw(ctx)
		if isOk {
			if err := json.Unmarshal([]byte(str[0][1]), &GrpcContext); err != nil {
				glog.Error(err)
			}
		} else {
			glog.Error("grpc context 加载用户登录信息失败")
		}

	}

	return &GrpcContext
}

// 根据财务编码获取门店是否绑定了虚拟前置仓
func (w *WarehouseService) GetShopWarehouseInfoByFinanceCode(ctx context.Context, in *proto.GetShopWarehouseInfoByFinanceCodeRequest) (*proto.GetShopWarehouseInfoByFinanceCodeResponse, error) {
	var result = new(proto.GetShopWarehouseInfoByFinanceCodeResponse)
	result.Code = 200
	result.Message = "Success"
	if len(in.FinanceCode) == 0 {
		result.Code = 400
		result.Message = "请求参数不能为空"
	}
	conn := w.GetConn()
	defer conn.Close()
	var list []models.Warehouse

	var sql strings.Builder

	sql.WriteString(" SELECT ")
	sql.WriteString(" w.`id`,w.`thirdid`,w.`code`,w.`name`,w.`comefrom`,w.`level`,w.`category`,w.`address`,w.`contacts`,w.`tel`,w.`status`,w.`createdate`,w.`lastdate`,w.`subsystem`,w.`ratio`,w.`lng`,w.`lat` ")
	sql.WriteString(" FROM dc_dispatch.warehouse w INNER JOIN dc_dispatch.warehouse_relation_shop b ON w.id = b.warehouse_id ")
	sql.WriteString(" WHERE b.shop_id = ? AND b.channel_id = ? and w.org_id =1")

	_ = conn.SQL(sql.String(), in.FinanceCode, in.ChannelId).Find(&list)

	glog.Info("查询的仓库信息", list)
	if len(list) == 0 {
		result.Code = 400
		result.Message = "仓库信息不存在，请核实仓库信息！编码：" + in.FinanceCode
		return result, errors.New(result.Message)
	}

	var out proto.WarehouseList
	out.Id = int32(list[0].Id)
	out.Thirdid = list[0].Thirdid
	out.Name = list[0].Name
	out.Code = list[0].Code
	out.Comefrom = int32(list[0].Comefrom)
	out.Level = int32(list[0].Level)
	out.Category = int32(list[0].Category)
	out.Address = list[0].Address
	out.Contacts = list[0].Contacts
	out.Tel = list[0].Tel
	out.Status = int32(list[0].Status)
	out.Createdate = list[0].Createdate.Format("2006-01-02 15:04:05")
	out.Lastdate = list[0].Lastdate.Format("2006-01-02 15:04:05")
	out.Subsystem = int32(list[0].Subsystem)
	out.Ratio = int32(list[0].Ratio)
	out.Lng = int64(list[0].Lng)
	out.Lat = int64(list[0].Lat)
	result.Warehouseinfo = &out

	return result, nil
}

// 门店绑定信息列表
func (w *WarehouseService) ShopBindInfoList(ctx context.Context, in *proto.ShopBindInfoRequest) (*proto.ShopBindInfoRespond, error) {
	var result = new(proto.ShopBindInfoRespond)
	result.Code = 200
	result.Message = "Success"

	conn := w.GetConn()
	defer conn.Close()
	conn.ShowSQL(true)

	session := conn.Table("warehouse_relation_shop").
		Join("left", "warehouse", " warehouse_relation_shop.`warehouse_id` = warehouse.`id`").
		Join("inner", "datacenter.store ds", "warehouse_relation_shop.shop_id = ds.finance_code and ds.org_id = 1").
		Where("warehouse_relation_shop.`channel_id` = ? ", in.ChannelId)

	switch in.BindType {
	case 1:
		session = session.And("warehouse.`category` = 1")
	case 3:
		session = session.And("warehouse.`category` = 3")
	case 4:
		session = session.And("warehouse.`category` = 4")
	case 5:
		session = session.And("warehouse.`category` = 5")
	}

	if len(in.Search) > 0 {
		session = session.And("warehouse_relation_shop.shop_name like ? or warehouse_relation_shop.shop_id = ?", "%"+in.Search+"%", in.Search)
	}

	sessionCounter := session.Clone()
	defer sessionCounter.Close()

	total, err := sessionCounter.Count(&models.WarehouseRelationShop{})
	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("统计总数失败,err : <%v>", err)
		return result, errors.New(result.Message)
	}

	result.TotalCount = int32(total)

	if total > 0 {
		err = session.Select("warehouse_relation_shop.`id`,warehouse_relation_shop.`shop_id`,warehouse_relation_shop.`shop_name`,warehouse_relation_shop.`warehouse_id`,warehouse_relation_shop.`warehouse_name`,warehouse_relation_shop.`channel_id`,warehouse.`category`, warehouse.`code`").
			Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
			OrderBy("warehouse_relation_shop.`create_time` DESC ").Find(&result.Info)

		if err != nil {
			result.Code = 400
			result.Message = fmt.Sprintf("查询失败 err:<%v>", err)
			return result, errors.New(result.Message)
		}
	}

	return result, nil
}

// 使用店铺id获取其绑定仓库信息
func (w *WarehouseService) ShopBindInfoListByShopId(ctx context.Context, in *proto.ShopBindInfoByShopIdRequest) (*proto.ShopBindInfoByShopIdRespond, error) {
	var result = new(proto.ShopBindInfoByShopIdRespond)
	result.Code = 200
	result.Message = "Success"

	if len(in.ShopId) == 0 {
		result.Code = 400
		result.Message = "请求数据为空"
		return result, errors.New("请求数据为空")
	}

	//先查redis
	var client = utils.ConnectClusterRedis()
	defer client.Close()

	var shopIds []string
	//使用一个map来记录未缓存的店铺
	uncacheMap := make(map[string]bool)
	for _, v := range in.ShopId {
		shopIds = append(shopIds, fmt.Sprintf("%s:%d", v, in.ChannelId))
		uncacheMap[v] = true
	}

	val, _ := client.HMGet("mt_shop_bind_warehouse", shopIds...).Result()
	var md []models.WarehouseRelationShopRedis
	for _, v := range val {
		if v == nil {
			continue
		}
		m := models.WarehouseRelationShopRedis{}
		if err := json.Unmarshal([]byte(v.(string)), &m); err != nil {
			continue
		}
		md = append(md, m)
	}

	for _, single := range md {
		delete(uncacheMap, single.ShopId)
		result.Info = append(result.Info, &proto.ShopBindInfo{
			ChannelId:     single.ChannelId,
			ShopId:        single.ShopId,
			ShopName:      single.ShopName,
			WarehouseId:   single.WarehouseId,
			WarehouseName: single.WarehouseName,
			Code:          single.Code,
			Category:      single.Category,
		})
	}

	if len(uncacheMap) == 0 {
		return result, nil
	}

	//后查mysql

	var shops []string
	for k, _ := range uncacheMap {
		shops = append(shops, k)
	}
	conn := w.GetConn()
	defer conn.Close()
	conn.ShowSQL(true)

	session := conn.Table("warehouse_relation_shop").
		Join("left", "warehouse", " warehouse_relation_shop.`warehouse_id` = warehouse.`id`")

	if in.ChannelId != 0 {
		session = session.Where("warehouse_relation_shop.`channel_id` = ? ", in.ChannelId)
	}

	session = session.In("warehouse_relation_shop.shop_id", shops)

	var info []proto.ShopBindInfo
	err := session.Select("warehouse_relation_shop.`id`,warehouse_relation_shop.`shop_id`,warehouse_relation_shop.`shop_name`,warehouse_relation_shop.`warehouse_id`,warehouse_relation_shop.`warehouse_name`,warehouse_relation_shop.`channel_id`,warehouse.`category`, warehouse.`code`").
		Find(&info)

	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("查询失败 err:<%v>", err)
		return result, errors.New(result.Message)
	}

	//将这些数据缓存到redis
	var data []models.WarehouseRelationShopRedis
	for _, v := range info {
		result.Info = append(result.Info, &v)
		//
		one := models.WarehouseRelationShopRedis{
			WarehouseId:   v.WarehouseId,
			WarehouseName: v.WarehouseName,
			Code:          v.Code,
			ShopId:        v.ShopId,
			ShopName:      v.ShopName,
			ChannelId:     v.ChannelId,
			Category:      v.Category,
		}
		data = append(data, one)
	}

	redisMap := make(map[string]interface{}, 0)
	for _, v := range data {
		modelStr, _ := json.Marshal(v)
		redisMap[fmt.Sprintf("%s:%d", v.ShopId, in.ChannelId)] = modelStr
	}
	if len(redisMap) > 0 {
		err = client.HMSet("mt_shop_bind_warehouse", redisMap).Err()
		if err != nil {
			glog.Info("ShopBindInfoListByShopId redis error :", err.Error(), " 参数:", kit.JsonEncode(redisMap))
		}
	}

	return result, nil
}

// 获取绑定门店数量
func (w *WarehouseService) BindShops(ctx context.Context, in *proto.BindShopsRequest) (*proto.BindShopsRespond, error) {
	var result = new(proto.BindShopsRespond)
	result.Code = 200
	result.Message = "Success"

	conn := w.GetConn()
	defer conn.Close()

	//1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
	channelMap := make(map[int32]string)
	channelMap[1] = "阿闻渠道-外卖"
	channelMap[2] = "美团"
	channelMap[3] = "饿了么"
	channelMap[4] = "京东到家"
	channelMap[9] = "互联网医院"
	channelMap[10] = "阿闻渠道-自提"

	type Temp struct {
		Category int `json:"category"`
		Num      int `json:"num"`
	}

	channelIds := []int32{1, 2, 3, 4, 9, 10}
	for _, channelId := range channelIds {
		session := conn.Table("warehouse_relation_shop").
			Join("left", "warehouse", " warehouse_relation_shop.`warehouse_id` = warehouse.`id`").
			Join("inner", "datacenter.store ds", "warehouse_relation_shop.shop_id = ds.finance_code and ds.org_id = 1").
			GroupBy("warehouse.`category`").
			Where("warehouse_relation_shop.`channel_id` = ?", channelId)

		var rs []Temp
		err := session.Select("warehouse.`category` , count(*) as num").
			Find(&rs)
		if err != nil {
			result.Message += fmt.Sprintf("[%s查询出错,%s]", channelMap[channelId], err.Error())
			continue
		}

		info := &proto.BindInfo{
			Channel: channelId,
		}
		for _, v := range rs {
			switch v.Category {
			case 1:
				info.ShopNumDs = int32(v.Num)
			case 3:
				info.ShopNum = int32(v.Num)
			case 4:
				info.ShopNumFront = int32(v.Num)
			case 5:
				info.ShopNumVirtual = int32(v.Num)
			}
		}

		result.Info = append(result.Info, info)
	}

	return result, nil
}

func (w *WarehouseService) StoreWarehouseRelationShop(ctx context.Context, in *proto.StoreWarehouseRelationShopRequest) (*proto.StoreWarehouseRelationShopRespond, error) {
	glog.Infof("StoreWarehouseRelationShop 请求入参:%s", kit.JsonEncode(in))

	var result = new(proto.StoreWarehouseRelationShopRespond)
	result.Code = 200
	result.Message = "Success"

	if len(in.Wrs) == 0 {
		result.Code = 400
		result.Message = "数据为空"
		return result, errors.New("数据为空")
	}

	conn := w.GetConn()
	defer conn.Close()

	//存储redis
	var client = utils.ConnectClusterRedis()
	defer client.Close()

	var info []models.WarehouseRelationShopRedis
	var cstSh, _ = time.LoadLocation("Asia/Shanghai") //上海

	for _, v := range in.Wrs {
		single := models.WarehouseRelationShopRedis{
			WarehouseId:   v.WarehouseId,
			WarehouseName: v.WarehouseName,
			Code:          v.Code,
			ShopId:        v.ShopId,
			ShopName:      v.ShopName,
			ChannelId:     in.ChannelId,
			Category:      v.Category,
			CreateTime:    time.Now().In(cstSh),
		}
		info = append(info, single)
	}

	var failed []string
	var succeed []string
	var err error
	for _, v := range info {
		err = w.bindRelationShop(client, conn, in.ChannelId, v)
		if err != nil {
			failed = append(failed, v.ShopId)
			continue
		}
		succeed = append(succeed, v.ShopId)
	}

	glog.Info("StoreWarehouseRelationShop succeed : ", succeed)
	result.ShopIds = failed

	return result, nil
}

// 绑定渠道仓库关系
func (w *WarehouseService) bindRelationShop(redisClient *redis.Client, dbConn *xorm.Engine, channelId int32, data models.WarehouseRelationShopRedis) error {
	var (
		anotherRelationShop *utils.WarehouseRelationShop
		isNeedRemoveAnother bool
	)
	switch channelId {
	case enum.ChannelAwenId:
		anotherRelationShop = utils.LoadChannelWarehouseCache(redisClient, data.ShopId, enum.ChannelAwenPickUpId)
	case enum.ChannelAwenPickUpId:
		anotherRelationShop = utils.LoadChannelWarehouseCache(redisClient, data.ShopId, enum.ChannelAwenId)
	}

	// 判断是门店仓且另一个渠道绑定不是自己的仓库是则删除
	if data.Category == 3 {
		if anotherRelationShop != nil && anotherRelationShop.WarehouseId != data.WarehouseId {
			isNeedRemoveAnother = true
		}
	} else { //如果目标仓库是前置仓或者前置虚拟仓，那么对应的另一个渠道只要不是前置仓或者前置虚拟仓就清掉
		if anotherRelationShop != nil && anotherRelationShop.Category != 4 && anotherRelationShop.Category != 5 {
			isNeedRemoveAnother = true
		}
	}

	sql := "INSERT INTO warehouse_relation_shop (shop_id, shop_name, warehouse_id, warehouse_name, channel_id, create_time)" +
		"VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE  warehouse_id= ?, warehouse_name=?"
	_, err := dbConn.Transaction(func(s *xorm.Session) (interface{}, error) {
		modelStr, _ := json.Marshal(data)
		err := redisClient.HSet("mt_shop_bind_warehouse", fmt.Sprintf("%s:%d", data.ShopId, channelId), modelStr).Err()
		if err != nil {
			return nil, fmt.Errorf("保存到redis异常:%+v", err)
		}
		_, err = dbConn.Exec(sql, data.ShopId, data.ShopName, data.WarehouseId, data.WarehouseName, data.ChannelId, data.CreateTime, data.WarehouseId, data.WarehouseName)
		if err != nil {
			return nil, fmt.Errorf("保存到mysql异常:%+v", err)
		}
		return nil, nil
	})
	if err != nil {
		glog.Errorf("StoreWarehouseRelationShop %s 插入失败 : <%v>", data.ShopId, err)
	}

	if isNeedRemoveAnother {
		_, err = dbConn.Where("channel_id=? and shop_id=?", anotherRelationShop.ChannelId, data.ShopId).Delete(&models.WarehouseRelationShop{})
		if err != nil {
			glog.Errorf("StoreWarehouseRelationShop %s %s 清理mysql同类仓库关联关系 : <%v>", data.ShopId, kit.JsonEncode(anotherRelationShop), err)
		}
		err = redisClient.HDel("mt_shop_bind_warehouse", fmt.Sprintf("%s:%d", data.ShopId, anotherRelationShop.ChannelId)).Err()
		if err != nil {
			glog.Errorf("StoreWarehouseRelationShop %s %s 清理redis同类仓库关联关系 : <%v>", data.ShopId, kit.JsonEncode(anotherRelationShop), err)
		}

		glog.Infof("StoreWarehouseRelationShop: %s,%s,驱动:%s 被删除 %s",
			data.ShopId,
			enum.ChannelMap[int(data.ChannelId)],
			enum.ChannelMap[int(anotherRelationShop.ChannelId)],
			kit.JsonEncode(anotherRelationShop),
		)
	}

	return nil
}

// 同步仓库信息列表
func (w *WarehouseService) GetA8WareHouseList(ctx context.Context, params *proto.GetA8WareHouseListRequest) (*proto.GetA8WareHouseListResponse, error) {
	out := &proto.GetA8WareHouseListResponse{
		TotalCount: 0,
	}
	var data []*proto.A8WareHouse

	msScrm := MssqlEngine()
	myScrm := GetDBConnRSCRM()
	defer msScrm.Close()
	defer myScrm.Close()

	//每次删除，取最新的数据
	var maxID []int
	_, err := myScrm.Exec("DELETE FROM stock;")
	if err != nil {
		maxID = append(maxID, 0)
		glog.Errorf("GetA8WareHouseList 删除瑞鹏所有库存异常:%+v", err)
	}

	var stockList []*models.Stock

	if err = msScrm.SQL("select k.kid Kid, k.usercode Usercode,s.fullname fullname_stype,k.fullname fullname_stock,k.`Add` Address,k.typeId type_id  from stock k left join stype s on k.stypeid = s.typeid WHERE k.kid > 0").Find(&stockList); err != nil {
		glog.Errorf("GetA8WareHouseList 查询A8所有仓库异常:%+v", err)
		return nil, err
	}

	_, err = myScrm.Insert(stockList)
	if err != nil {
		glog.Errorf("GetA8WareHouseList 批量插入瑞鹏仓库异常:%+v", err)
		return nil, err
	}

	session := myScrm.NewSession()
	if params.IsAll == 0 {
		session = session.Where("NOT EXISTS(SELECT 1 FROM warehouse WHERE warehouse.`id` = stock.`kid`)")
	}
	if params.Condition != "" {
		session.And(" (fullname_stock like ? OR usercode like ?)", "%"+params.Condition+"%", "%"+params.Condition+"%")
		//session.And("usercode like ?", "%"+model.Condition+"%")
	}

	sessionCopy := *session
	//关闭连接池
	defer session.Close()
	defer sessionCopy.Close()

	totalCount, err := session.Table("stock").Count()
	if err != nil {
		glog.Errorf("GetA8WareHouseList 查询瑞鹏仓库总数量异常:%+v", err)
		return nil, err
	}
	err = sessionCopy.Table("stock").Select("kid,type_id,usercode,fullname_stype,fullname_stock,address").Limit(cast.ToInt(params.PageSize), cast.ToInt((params.PageIndex-1)*params.PageSize)).Find(&data)

	if err != nil {
		glog.Errorf("GetA8WareHouseList 分页查询瑞鹏仓库异常:%+v", err)
		return nil, err
	}
	out.TotalCount = cast.ToInt32(totalCount)
	out.Data = data
	return out, nil
}

func (w *WarehouseService) InitBindShopData(ctx context.Context, params *proto.InitShopDataRequest) (*proto.InitShopDataRespond, error) {
	result := new(proto.InitShopDataRespond)
	result.Code = 200
	result.Message = "Success"

	conn := w.GetConn()
	defer conn.Close()
	var errMsgs []string

	if params.Step == 1 {
		type Tmp struct {
			Id          int32  `json:"id"`
			Name        string `json:"name"`
			Category    int32  `json:"category"`
			FinanceCode string `json:"finance_code"`
			ShopName    string `json:"shop_name"`
			Code        string `json:"code"`
		}
		var list []Tmp

		var sql strings.Builder

		sql.WriteString("select a.*, s.name as shop_name from ( ")
		sql.WriteString("select CASE w.category ")
		sql.WriteString("WHEN 3 THEN w.`code` ELSE wr.shop_id END finance_code,w.id,w.name,w.category,w.status,w.code ")
		sql.WriteString("from dc_dispatch.warehouse w ")
		sql.WriteString("inner join dc_dispatch.warehouse_relationship wr on w.id = wr.warehouse_id ")
		sql.WriteString("where w.status = 1 AND w.category in (3,4,5)) a ")
		sql.WriteString("inner join datacenter.store s ON a.finance_code = s.finance_code ")
		sql.WriteString("UNION ")
		sql.WriteString("SELECT s.finance_code , w.id , w.name , w.category, w.status , w.code , s.name as shop_name ")
		sql.WriteString("from warehouse w INNER JOIN datacenter.store s ON w.code = s.finance_code ")
		sql.WriteString("where w.category = 3 and w.status = 1")

		err := conn.SQL(sql.String()).Find(&list)
		if err != nil {
			result.Code = 400
			result.Message = fmt.Sprintf("%v", err)
			return result, err
		}

		glog.Info("查询的仓库信息", kit.JsonEncode(list))

		//1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
		channelMap := make(map[int32]string)
		channelMap[1] = "阿闻渠道-外卖"
		channelMap[2] = "美团"
		channelMap[3] = "饿了么"
		channelMap[4] = "京东到家"
		//channelMap[9] = "互联网医院"
		channelMap[10] = "阿闻渠道-自提"

		// 处理前置仓 虚拟仓问题
		warehouseMap := make(map[string]Tmp)
		vMap := make(map[string]Tmp)

		for _, v := range list {
			if v.Category == 3 {
				warehouseMap[v.FinanceCode] = v
				vMap[v.FinanceCode] = v
			}
		}

		for _, v := range list {
			if v.Category == 5 {
				warehouseMap[v.FinanceCode] = v
			}
			if v.Category == 4 {
				vMap[v.FinanceCode] = v
			}
		}
		for _, v := range list {
			if v.Category == 4 {
				warehouseMap[v.FinanceCode] = v
			}
			if v.Category == 5 {
				vMap[v.FinanceCode] = v
			}
		}

		for channelId, channel := range channelMap {
			request := new(proto.StoreWarehouseRelationShopRequest)
			request.ChannelId = channelId
			switch channelId {
			case 1, 3, 4:
				for _, v := range warehouseMap {
					single := &proto.WarehouseRelationShop{
						ShopId:        v.FinanceCode,
						ShopName:      v.ShopName,
						WarehouseId:   v.Id,
						WarehouseName: v.Name,
						Category:      v.Category,
						Code:          v.Code,
					}

					request.Wrs = append(request.Wrs, single)
				}
			case 2:
				for _, v := range list {
					if v.Category == 3 {
						single := &proto.WarehouseRelationShop{
							ShopId:        v.FinanceCode,
							ShopName:      v.ShopName,
							WarehouseId:   v.Id,
							WarehouseName: v.Name,
							Category:      v.Category,
							Code:          v.Code,
						}

						request.Wrs = append(request.Wrs, single)
					}
				}
			case 10:
				for _, v := range vMap {
					single := &proto.WarehouseRelationShop{
						ShopId:        v.FinanceCode,
						ShopName:      v.ShopName,
						WarehouseId:   v.Id,
						WarehouseName: v.Name,
						Category:      v.Category,
						Code:          v.Code,
					}
					request.Wrs = append(request.Wrs, single)
				}
			}

			_, err = w.StoreWarehouseRelationShop(ctx, request)
			if err != nil {
				errMsgs = append(errMsgs, fmt.Sprintf("%s 初始化失败,error: %s", channel, err.Error()))
			}
		}
	} else {
		//更新门店仓状态
		_, err := conn.Exec("update warehouse set status = 1 where category = 3")
		if err != nil {
			glog.Errorf("InitBindShopData 更新门店仓状态异常:%+v", err)
			result.Code = 400
			result.Message = fmt.Sprintf("%v", err)
			return result, err
		}
	}

	result.Message = strings.Join(errMsgs, ";")
	return result, nil
}

// 修复mt数据异常
func (w *WarehouseService) InitBindMTShopData(ctx context.Context, params *proto.InitShopDataRequest) (*proto.InitShopDataRespond, error) {

	result := new(proto.InitShopDataRespond)
	result.Code = 200
	result.Message = "Success"

	conn := w.GetConn()
	defer conn.Close()
	var redisClient = utils.ConnectClusterRedis()
	defer redisClient.Close()

	session := conn.Table("warehouse_relation_shop").
		Join("left", "warehouse", " warehouse_relation_shop.`warehouse_id` = warehouse.`id`")

	session = session.Where("warehouse_relation_shop.`channel_id` = ? ", 2)

	var info []proto.ShopBindInfo
	err := session.Select("warehouse_relation_shop.`id`,warehouse_relation_shop.`shop_id`,warehouse_relation_shop.`shop_name`,warehouse_relation_shop.`warehouse_id`,warehouse_relation_shop.`warehouse_name`,warehouse_relation_shop.`channel_id`,warehouse.`category`, warehouse.`code`").
		Find(&info)

	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("查询失败 err:<%v>", err)
		return result, errors.New(result.Message)
	}

	//将这些数据缓存到redis
	var data []models.WarehouseRelationShopRedis
	for _, v := range info {
		one := models.WarehouseRelationShopRedis{
			WarehouseId:   v.WarehouseId,
			WarehouseName: v.WarehouseName,
			Code:          v.Code,
			ShopId:        v.ShopId,
			ShopName:      v.ShopName,
			ChannelId:     v.ChannelId,
			Category:      v.Category,
		}
		data = append(data, one)
	}

	redisMap := make(map[string]interface{}, 0)
	for _, v := range data {
		modelStr, _ := json.Marshal(v)
		fmt.Println(string(modelStr))
		redisMap[fmt.Sprintf("%s:%d", v.ShopId, 2)] = modelStr
	}
	err = redisClient.HMSet("mt_shop_bind_warehouse", redisMap).Err()

	if err != nil {
		glog.Info("ShopBindInfoListByShopId redis error :", err.Error())
		result.Code = 400
		result.Message = fmt.Sprintf("存redis失败 err:<%v>", err)
		return result, errors.New(result.Message)
	}

	return result, nil
}

func (w *WarehouseService) GetWarehouseInfoByCode(ctx context.Context, in *proto.GetWarehouseInfoByCodeRequest) (*proto.GetWarehouseInfoByCodeResponse, error) {
	glog.Info("GetWarehouseInfoByCode Code : ", in.Code, " category: ", in.Category)
	var result = new(proto.GetWarehouseInfoByCodeResponse)
	result.Code = 200
	result.Message = "Success"
	if len(in.Code) == 0 {
		result.Code = 400
		result.Message = "请求参数不能为空"
	}
	conn := w.GetConn()
	defer conn.Close()

	var wh models.Warehouse
	var isExist bool
	var err error

	if in.Category == 3 {
		isExist, err = conn.Where("code = ? AND category = 3", in.Code).Get(&wh)
	} else if in.Category == 7 {
		isExist, err = conn.Where("code = ? AND category in (4 , 5)", in.Code).Get(&wh)
	} else {
		isExist, err = conn.Where("code = ? ", in.Code).Get(&wh)
	}

	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("请求数据库失败 %s", err.Error())

		return result, err
	}

	if !isExist {
		result.Code = 400
		result.Message = fmt.Sprintf("该仓库不存在")

		return result, errors.New("该仓库不存在")
	}

	result.Warehouseinfo = &proto.WarehouseList{
		Id:         int32(wh.Id),
		Thirdid:    wh.Thirdid,
		Code:       wh.Code,
		Name:       wh.Name,
		Comefrom:   int32(wh.Comefrom),
		Level:      int32(wh.Level),
		Category:   int32(wh.Category),
		Address:    wh.Address,
		Contacts:   wh.Contacts,
		Tel:        wh.Tel,
		Status:     int32(wh.Status),
		Createdate: wh.Createdate.Format("2006-01-02 15:04:05"),
		Lastdate:   wh.Lastdate.Format("2006-01-02 15:04:05"),
		Subsystem:  int32(wh.Subsystem),
		Ratio:      int32(wh.Ratio),
		Lng:        int64(wh.Lng),
		Lat:        int64(wh.Lat),
		Region:     wh.Region,
		City:       wh.City,
	}

	return result, nil
}

// 查询店铺绑定的所有仓库(门店仓、前置仓、虚拟仓)
// 建议调用方先从缓存中取数据，取不到再调用此接口查询
func (w *WarehouseService) ShopWarehouseList(ctx context.Context, req *proto.ShopWarehouseListRequest) (*proto.WarehouseListResponse, error) {
	if req.ShopId == "" {
		glog.Errorf("ShopWarehouseList 参数财务编码为空")
		return nil, errors.New("财务编码不能为空")
	}
	conn := w.GetConn()
	defer conn.Close()

	//var client = utils.ConnectClusterRedis()
	//defer client.Close()
	//
	//// 强制刷新redis缓存
	//w.buildWarehouseRelationCache(conn, client, req.ShopId)
	var warehouseList []models.Warehouse
	//warehouseList := utils.LoadWarehouseRelationCache(client, req.ShopId)
	conn.SQL("SELECT DISTINCT a.* FROM  warehouse a INNER JOIN `warehouse_relation_shop` b ON a.id=b.warehouse_id WHERE b.shop_id=?", req.ShopId).Find(&warehouseList)

	glog.Infof("ShopWarehouseList 门店(%s)最新仓库绑定关系:%s", req.ShopId, kit.JsonEncode(warehouseList))

	resp := &proto.WarehouseListResponse{
		Code:       http.StatusOK,
		TotalCount: int32(len(warehouseList)),
	}
	for _, v := range warehouseList {
		resp.WarehouseAarray = append(resp.WarehouseAarray, &proto.WarehouseList{
			Id:            int32(v.Id),
			Thirdid:       v.Thirdid,
			Code:          v.Code,
			Name:          v.Name,
			Comefrom:      int32(v.Comefrom),
			Level:         int32(v.Level),
			Category:      int32(v.Category),
			Address:       v.Address,
			Contacts:      v.Contacts,
			Tel:           v.Tel,
			Status:        int32(v.Status),
			Createdate:    v.Createdate.Format("2006-01-02 15:04:05"),
			Lastdate:      v.Lastdate.Format("2006-01-02 15:04:05"),
			Subsystem:     int32(v.Subsystem),
			Ratio:         int32(v.Ratio),
			WarehouseInfo: []string{},
			Lng:           int64(v.Lng),
			Lat:           int64(v.Lat),
			Region:        v.Region,
			City:          v.City,
		})
	}
	return resp, nil
}

// 构建更新店铺仓库关系缓存
//func (w *WarehouseService) buildWarehouseRelationCache(db *xorm.Engine, client *redis.Client, financeCode string) {
//	var list []models.Warehouse
//	_, err := db.Transaction(func(session *xorm.Session) (interface{}, error) {
//		var relationList []models.WarehouseRelationship
//		err := session.SQL("select * from warehouse_relationship where shop_id=?", financeCode).Find(&relationList)
//		if err != nil {
//			return nil, err
//		}
//		warehouseIds := make([]string, 0, len(relationList))
//		for _, v := range relationList {
//			warehouseIds = append(warehouseIds, cast.ToString(v.WarehouseId))
//		}
//
//		sqlParams := []interface{}{
//			financeCode,
//		}
//		rawSql := "(select * from warehouse where category=3 and code=?)"
//		if len(relationList) > 0 {
//			rawSql += " union all (select * from warehouse where id in (" + strings.Join(warehouseIds, ",") + "))"
//		}
//
//		err = session.SQL(rawSql, sqlParams...).Find(&list)
//		return nil, err
//	})
//
//	if err != nil {
//		glog.Errorf("WarehouseService/buildWarehouseRelationCache 查询店铺(%s)所有仓库异常:%+v", financeCode, err)
//		return
//	}
//
//}

func (w *WarehouseService) InitMTBindShopData(ctx context.Context, params *proto.InitShopDataRequest) (*proto.InitShopDataRespond, error) {
	result := new(proto.InitShopDataRespond)
	result.Code = 200
	result.Message = "Success"

	conn := w.GetConn()
	defer conn.Close()

	type Tmp struct {
		Id          int32  `json:"id"`
		Name        string `json:"name"`
		Category    int32  `json:"category"`
		FinanceCode string `json:"finance_code"`
		ShopName    string `json:"shop_name"`
		Code        string `json:"code"`
	}
	var list []Tmp

	var sql strings.Builder

	sql.WriteString("select b.`shop_id` as finance_code, s.`name` as shop_name , w.`id` , w.`category`,w.`name`, w.`code` from dc_dispatch.warehouse_relationship b")
	sql.WriteString(" INNER JOIN dc_dispatch.warehouse w ON w.id = b.warehouse_id")
	sql.WriteString(" INNER JOIN datacenter.store s ON b.shop_id = s.finance_code where w.category = 4")

	err := conn.SQL(sql.String()).Find(&list)
	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("%v", err)
		return result, err
	}

	glog.Info("查询的仓库信息", list)

	request := new(proto.StoreWarehouseRelationShopRequest)
	request.ChannelId = 2

	for _, v := range list {
		single := &proto.WarehouseRelationShop{
			ShopId:        v.FinanceCode,
			ShopName:      v.ShopName,
			WarehouseId:   v.Id,
			WarehouseName: v.Name,
			Category:      v.Category,
			Code:          v.Code,
		}

		request.Wrs = append(request.Wrs, single)
	}

	_, err = w.StoreWarehouseRelationShop(ctx, request)
	if err != nil {
		result.Code = 400
		result.Message = fmt.Sprintf("StoreWarehouseRelationShop 存储失败 <%v>", err)
		return result, err
	}

	return result, nil
}

// 初始化仓库绑定关系缓存数据
//func (w *WarehouseService) initAllWarehouseRelationCache(db *xorm.Engine) error {
//	var client = utils.ConnectClusterRedis()
//	defer client.Close()
//	// 查询所有门店仓
//	var list []models.Warehouse
//	err := db.SQL("select id,code,name from dc_dispatch.warehouse where category=3").Find(&list)
//	if err != nil {
//		return err
//	}
//	glog.Infof("共有%d个门店需要初始化仓库绑定关系缓存数据", len(list))
//	for _, v := range list {
//		w.buildWarehouseRelationCache(db, client, v.Code)
//	}
//	return nil
//}

func (w *WarehouseService) RemoveShop(ctx context.Context, params *proto.RemoveShopRequest) (*proto.RemoveShopRespond, error) {
	var result = new(proto.RemoveShopRespond)
	result.Code = 200
	result.Message = "解绑成功"

	conn := w.GetConn()
	defer conn.Close()

	var relationList []models.WarehouseRelationShop
	err := conn.Where("channel_id = ? and shop_id=?", params.ChannelId, params.ShopId).Find(&relationList)
	if err != nil {
		glog.Errorf("WarehouseService/RemoveShop 查询relationShop关系异常:%+v %s", err, kit.JsonEncode(params))
	}

	var ws models.WarehouseRelationShop
	_, err = conn.Table("warehouse_relation_shop").
		Where("shop_id = ? AND channel_id = ?", params.ShopId, params.ChannelId).
		Delete(&ws)

	if err != nil {
		result.Code = 400
		result.Error = fmt.Sprintf("解绑失败：%v", err)
		return result, err
	}

	//移除redis
	var client = utils.ConnectClusterRedis()
	defer client.Close()
	key := fmt.Sprintf("%s:%d", params.ShopId, params.ChannelId)
	_, err = client.HDel("mt_shop_bind_warehouse", key).Result()

	if err != nil {
		glog.Errorf("RemoveShop 清理仓库渠道缓存关系异常:%+v %s", err, params.ShopId)
	}

	// 记录切仓操作日志
	go w.asyncLogSwitchWarehouse(ctx, relationList, 2)

	return result, nil
}

// WarehouseRelationShopList 门店仓库管理列表
func (w *WarehouseService) WarehouseRelationShopList(ctx context.Context, in *proto.ShopBindWarehouseReq) (out *proto.ShopBindWarehouseRes, e error) {
	out = new(proto.ShopBindWarehouseRes)
	defer func() {
		if out.Code != 200 {
			glog.Info("WarehouseRelationShopList 入参：", kit.JsonEncode(in), "，返回：", kit.JsonEncode(out))
		}
	}()

	conn := w.GetConn()
	defer conn.Close()
	conn.ShowSQL(true)

	session := conn.Table("datacenter.store").Alias("s").Where("s.org_id = 1").
		Join("left", "warehouse_relation_shop wrs", " wrs.`shop_id` = s.`finance_code`").
		Join("left", "warehouse w", " wrs.`warehouse_id` = w.`id`")

	if in.ChannelId > 0 {
		session = session.And("wrs.`channel_id` = ?", in.ChannelId)
	}

	if in.BindType > 0 && in.BindType < 6 {
		session = session.And("w.`category` = ?", in.BindType)
	} else if in.BindType == 6 { //门店前置仓-不是绑定自己的仓库
		session = session.And("w.`category` = 3 and wrs.shop_id <> w.code")
	}

	//门店查询
	if len(in.ShopSearch) > 0 {
		session = session.And("s.name like ? or s.finance_code = ?", "%"+in.ShopSearch+"%", in.ShopSearch)
	}
	//仓库查询
	if len(in.WarehouseSearch) > 0 {
		session = session.And("w.name like ? or w.code = ?", "%"+in.WarehouseSearch+"%", in.WarehouseSearch)
	}

	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	if in.PageIndex <= 0 {
		in.PageIndex = 1
	}
	NewSession := session
	NewSession.Distinct("s.finance_code")
	if total, err := NewSession.Clone().Count(); err != nil {
		out.Message = err.Error()
		return
	} else if total > 0 {
		err = session.Select("s.id,s.name as shop_name,s.finance_code as shop_id,wrs.warehouse_name,wrs.warehouse_id,wrs.channel_id,w.`category`, w.`code`,s.`custom_code`").
			Limit(int(in.PageSize), int(in.PageIndex*in.PageSize)-int(in.PageSize)).
			OrderBy("wrs.`create_time` DESC ").GroupBy("wrs.shop_id").
			Find(&out.Info)
		if err != nil {
			out.Code = 400
			out.Message = fmt.Sprintf("查询失败 err:<%v>", err)
		}

		out.TotalCount = int32(total)

		if len(out.Info) > 0 {
			var ShopIdMap []string
			for _, v := range out.Info {
				ShopIdMap = append(ShopIdMap, v.ShopId)
			}
			var info = make([]*proto.WarehouseInfo, 0)
			if err = conn.Table("warehouse_relation_shop").Alias("wrs").
				Join("left", "warehouse w", "wrs.warehouse_id = w.id").
				Select("wrs.shop_id,wrs.channel_id,wrs.warehouse_id,w.code,w.name,w.category").
				In("wrs.shop_id", ShopIdMap).Find(&info); err != nil {
				out.Code = 400
				out.Message = fmt.Sprintf("查询仓库信息失败 err:%s", err.Error())
			}
			var WarehouseInfoMap = make(map[string][]*proto.WarehouseInfo)
			for _, v := range info {
				WarehouseInfoMap[v.ShopId] = append(WarehouseInfoMap[v.ShopId], v)
			}

			for _, v := range out.Info {
				if _, ok := WarehouseInfoMap[v.ShopId]; ok {
					list := WarehouseInfoMap[v.ShopId]
					for _, vv := range list {
						//if vv.Category == 3 && v.ShopId != vv.Code {
						//	vv.Category = 6
						//}
						CategoryName := CategoryTypeMap[vv.Category]
						ChannelName := fmt.Sprintf("%s:%s(%s)", CategoryName, vv.Name, vv.Code)
						switch vv.ChannelId {
						case 1:
							v.AwenDeliveryWarehourse = ChannelName
						case 2:
							v.MtWarehourse = ChannelName
						case 3:
							v.EleWarehourse = ChannelName
						case 4:
							v.JdWarehourse = ChannelName
						case 9:
							v.HospitalsWarehourse = ChannelName
						case 10:
							v.AwenPickupWarehourse = ChannelName
						}
					}
				}
			}
		}
	}
	out.Code = 200
	out.Message = "Success"
	return
}
