package services

import (
	"bytes"
	"database/sql"
	"dispatch-center/utils"
	_ "github.com/denisenkom/go-mssqldb"
	_ "github.com/go-sql-driver/mysql"
	"github.com/go-xorm/xorm"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/streadway/amqp"
	kit "github.com/tricobbler/rp-kit"
	"time"
)

type DBService struct {
}

var (
	Engine     *xorm.Engine
	mqEndPoint string
	mqUserName string
	mqPassWord string
	mqHostName string
)

func init() {
	mySqlStr := config.GetString("mysql.dc_dispatch")
	mqEndPoint = config.GetString("mq.EndPoint")
	mqUserName = config.GetString("mq.UserName")
	mqPassWord = config.GetString("mq.PassWord")
	mqHostName = config.GetString("mq.HostName")

	// mqUserName = "admin"
	// mqPassWord = "admin"
	//mqHostName = "*********:4672"
	// mqHostName = "*********:5672"
	//mqEndPoint = ""

	glog.Info("mq.EndPoint ", mqEndPoint)
	if len(mySqlStr) == 0 {
		glog.Fatal("can't find mysql url")
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mysql", mySqlStr)

	if err != nil {
		glog.Fatal("mysql connect fail", err)
		panic(err)
	}
	engine.ShowSQL()
	/*engine.ShowSQL()
	  err = engine.Ping()
	  if err != nil {
	     golog.Fatal("mysql ping fail", err)
	     panic(err)
	  }*/
	location, err := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)
	Engine = engine

}

func NewMqConn() *amqp.Connection {
	var buf bytes.Buffer

	if mqUserName == "" && mqPassWord == "" {
		ak := "LTAI4FkL4aBWVWYca18gQazX"
		sk := "******************************"
		var resourceOwnerId uint64 = 1361651336438271 // 请替换成您自己的userId
		mqUserName = utils.GetUserName(ak, resourceOwnerId)
		mqPassWord = utils.GetPassword(sk)
	}

	buf.WriteString("amqp://")
	buf.WriteString(mqUserName)
	buf.WriteString(":")
	buf.WriteString(mqPassWord)

	// <Your End Point> 请从控制台获取。如果你使用的是杭州Region，那么Endpoint会形如 137000000010111.mq-amqp.cn-hangzhou-a.aliyuncs.com
	buf.WriteString("@")
	buf.WriteString(mqHostName)
	buf.WriteString("/")
	buf.WriteString(mqEndPoint)

	url := buf.String()
	glog.Info("RabbitMQ ", url)
	conn, err := amqp.Dial(url)
	if err != nil {
		glog.Error("mq.NewMqConn", err)
		panic(err)
	}
	return conn
}

func NewMqChannel(conn *amqp.Connection) *amqp.Channel {
	c, err := conn.Channel()
	if err != nil {
		glog.Error("mq.NewMqChannel", err)
		panic(err)
	}
	return c
}

func (b *DBService) GetConnNative() *sql.DB {
	params := config.GetString("mysql.dc_dispatch")
	var err error
	engine, err := sql.Open("mysql", params)
	if err != nil {
		panic(err)
	}
	err = engine.Ping()
	return engine
}

func (b *DBService) GetConn() *xorm.Engine {
	params := config.GetString("mysql.dc_dispatch")
	engine, err := xorm.NewEngine("mysql", params)
	if err != nil {
		panic(err)
	}
	return engine

}

//根据语句查询总记录数
func (b *DBService) CountPage(sql string, params []interface{}) int {
	services := DBService{}
	conn := services.GetConnNative()
	totle := 0
	stmt, err := conn.Prepare(sql)
	if err != nil {
		return 0
	}
	result, err := stmt.Query(params...)
	if err != nil {
		return 0
	}
	result.Next()
	result.Scan(&totle)
	return totle
}

func GetDBConnRSCRM(mySqlStr ...string) *xorm.Engine {
	if len(mySqlStr) == 0 {
		mySqlStr = append(mySqlStr, config.GetString("Mysql_rhscm"))
	}
	if len(mySqlStr[0]) == 0 {
		glog.Fatal("can't find mysql url")
		panic("can't find mysql url")
	}
	return kit.NewDBEngine(mySqlStr[0]).Engine.(*xorm.Engine)
}
//A8数据库连接，sql server
func MssqlEngine() *xorm.Engine {
	mySqlStr := config.GetString("MsSql_rhscm")
	if len(mySqlStr) == 0 {
		panic("can't find mysql url")
	}

	engine, err := xorm.NewEngine("mssql", mySqlStr)
	if err != nil {
		panic(err)
	}

	// if Debug {
	// 	engine.ShowSQL()
	// 	engine.ShowExecTime()
	// }

	//空闲关闭时间
	engine.SetConnMaxLifetime(60 * time.Second)
	//最大空闲连接
	engine.SetMaxIdleConns(10)
	//最大连接数
	engine.SetMaxOpenConns(500)

	location, _ := time.LoadLocation("Asia/Shanghai")
	engine.SetTZLocation(location)

	return engine
}

