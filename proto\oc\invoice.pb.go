// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oc/invoice.proto

package oc

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type InvoiceResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceResponse) Reset()         { *m = InvoiceResponse{} }
func (m *InvoiceResponse) String() string { return proto.CompactTextString(m) }
func (*InvoiceResponse) ProtoMessage()    {}
func (*InvoiceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{0}
}

func (m *InvoiceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceResponse.Unmarshal(m, b)
}
func (m *InvoiceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceResponse.Marshal(b, m, deterministic)
}
func (m *InvoiceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceResponse.Merge(m, src)
}
func (m *InvoiceResponse) XXX_Size() int {
	return xxx_messageInfo_InvoiceResponse.Size(m)
}
func (m *InvoiceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceResponse proto.InternalMessageInfo

func (m *InvoiceResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InvoiceResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// 开票请求参数
type CreateInvoiceRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 前端不需要传
	Channel int32 `protobuf:"varint,2,opt,name=channel,proto3" json:"channel"`
	// 发票类型 1个人、2企业
	InvoiceTt int32 `protobuf:"varint,3,opt,name=invoice_tt,json=invoiceTt,proto3" json:"invoice_tt"`
	// 手机号码/企业电话
	Mobile string `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile"`
	// 电子邮箱
	Email string `protobuf:"bytes,5,opt,name=email,proto3" json:"email"`
	// 前端不需要传
	CompanyCode string `protobuf:"bytes,6,opt,name=company_code,json=companyCode,proto3" json:"company_code"`
	// 纳税人识别号
	IdentifyNumber string `protobuf:"bytes,7,opt,name=identify_number,json=identifyNumber,proto3" json:"identify_number"`
	// 开户银行
	BankName string `protobuf:"bytes,8,opt,name=bank_name,json=bankName,proto3" json:"bank_name"`
	// 银行卡号
	BankNumber string `protobuf:"bytes,9,opt,name=bank_number,json=bankNumber,proto3" json:"bank_number"`
	// 企业地址
	CompanyAddress string `protobuf:"bytes,10,opt,name=company_address,json=companyAddress,proto3" json:"company_address"`
	// 企业名称
	CompanyName string `protobuf:"bytes,11,opt,name=company_name,json=companyName,proto3" json:"company_name"`
	// 前端不需要传，仅用于标记用户
	ScrmId string `protobuf:"bytes,12,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	// 支付时间
	PayTime              int64    `protobuf:"varint,13,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInvoiceRequest) Reset()         { *m = CreateInvoiceRequest{} }
func (m *CreateInvoiceRequest) String() string { return proto.CompactTextString(m) }
func (*CreateInvoiceRequest) ProtoMessage()    {}
func (*CreateInvoiceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{1}
}

func (m *CreateInvoiceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInvoiceRequest.Unmarshal(m, b)
}
func (m *CreateInvoiceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInvoiceRequest.Marshal(b, m, deterministic)
}
func (m *CreateInvoiceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInvoiceRequest.Merge(m, src)
}
func (m *CreateInvoiceRequest) XXX_Size() int {
	return xxx_messageInfo_CreateInvoiceRequest.Size(m)
}
func (m *CreateInvoiceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInvoiceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInvoiceRequest proto.InternalMessageInfo

func (m *CreateInvoiceRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *CreateInvoiceRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *CreateInvoiceRequest) GetInvoiceTt() int32 {
	if m != nil {
		return m.InvoiceTt
	}
	return 0
}

func (m *CreateInvoiceRequest) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *CreateInvoiceRequest) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *CreateInvoiceRequest) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *CreateInvoiceRequest) GetIdentifyNumber() string {
	if m != nil {
		return m.IdentifyNumber
	}
	return ""
}

func (m *CreateInvoiceRequest) GetBankName() string {
	if m != nil {
		return m.BankName
	}
	return ""
}

func (m *CreateInvoiceRequest) GetBankNumber() string {
	if m != nil {
		return m.BankNumber
	}
	return ""
}

func (m *CreateInvoiceRequest) GetCompanyAddress() string {
	if m != nil {
		return m.CompanyAddress
	}
	return ""
}

func (m *CreateInvoiceRequest) GetCompanyName() string {
	if m != nil {
		return m.CompanyName
	}
	return ""
}

func (m *CreateInvoiceRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

func (m *CreateInvoiceRequest) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

// 开票返回参数
type CreateInvoiceResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string                     `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *CreateInvoiceResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *CreateInvoiceResponse) Reset()         { *m = CreateInvoiceResponse{} }
func (m *CreateInvoiceResponse) String() string { return proto.CompactTextString(m) }
func (*CreateInvoiceResponse) ProtoMessage()    {}
func (*CreateInvoiceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{2}
}

func (m *CreateInvoiceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInvoiceResponse.Unmarshal(m, b)
}
func (m *CreateInvoiceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInvoiceResponse.Marshal(b, m, deterministic)
}
func (m *CreateInvoiceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInvoiceResponse.Merge(m, src)
}
func (m *CreateInvoiceResponse) XXX_Size() int {
	return xxx_messageInfo_CreateInvoiceResponse.Size(m)
}
func (m *CreateInvoiceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInvoiceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInvoiceResponse proto.InternalMessageInfo

func (m *CreateInvoiceResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CreateInvoiceResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *CreateInvoiceResponse) GetData() *CreateInvoiceResponseData {
	if m != nil {
		return m.Data
	}
	return nil
}

type CreateInvoiceResponseData struct {
	Source               int32    `protobuf:"varint,1,opt,name=Source,proto3" json:"Source"`
	CompanyCode          string   `protobuf:"bytes,2,opt,name=CompanyCode,proto3" json:"CompanyCode"`
	OrderNo              string   `protobuf:"bytes,3,opt,name=OrderNo,proto3" json:"OrderNo"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInvoiceResponseData) Reset()         { *m = CreateInvoiceResponseData{} }
func (m *CreateInvoiceResponseData) String() string { return proto.CompactTextString(m) }
func (*CreateInvoiceResponseData) ProtoMessage()    {}
func (*CreateInvoiceResponseData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{3}
}

func (m *CreateInvoiceResponseData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInvoiceResponseData.Unmarshal(m, b)
}
func (m *CreateInvoiceResponseData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInvoiceResponseData.Marshal(b, m, deterministic)
}
func (m *CreateInvoiceResponseData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInvoiceResponseData.Merge(m, src)
}
func (m *CreateInvoiceResponseData) XXX_Size() int {
	return xxx_messageInfo_CreateInvoiceResponseData.Size(m)
}
func (m *CreateInvoiceResponseData) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInvoiceResponseData.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInvoiceResponseData proto.InternalMessageInfo

func (m *CreateInvoiceResponseData) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *CreateInvoiceResponseData) GetCompanyCode() string {
	if m != nil {
		return m.CompanyCode
	}
	return ""
}

func (m *CreateInvoiceResponseData) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

// 退款开票申请
type RefundInvoiceRequest struct {
	RefundSn             string   `protobuf:"bytes,1,opt,name=refund_sn,json=refundSn,proto3" json:"refund_sn"`
	Channel              int32    `protobuf:"varint,2,opt,name=channel,proto3" json:"channel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundInvoiceRequest) Reset()         { *m = RefundInvoiceRequest{} }
func (m *RefundInvoiceRequest) String() string { return proto.CompactTextString(m) }
func (*RefundInvoiceRequest) ProtoMessage()    {}
func (*RefundInvoiceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{4}
}

func (m *RefundInvoiceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundInvoiceRequest.Unmarshal(m, b)
}
func (m *RefundInvoiceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundInvoiceRequest.Marshal(b, m, deterministic)
}
func (m *RefundInvoiceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundInvoiceRequest.Merge(m, src)
}
func (m *RefundInvoiceRequest) XXX_Size() int {
	return xxx_messageInfo_RefundInvoiceRequest.Size(m)
}
func (m *RefundInvoiceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundInvoiceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefundInvoiceRequest proto.InternalMessageInfo

func (m *RefundInvoiceRequest) GetRefundSn() string {
	if m != nil {
		return m.RefundSn
	}
	return ""
}

func (m *RefundInvoiceRequest) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

// 税收编码请求参数
type TaxCodeRequest struct {
	StructCode           string   `protobuf:"bytes,1,opt,name=struct_code,json=structCode,proto3" json:"struct_code"`
	ThirdSkuIds          []string `protobuf:"bytes,2,rep,name=third_sku_ids,json=thirdSkuIds,proto3" json:"third_sku_ids"`
	NuanId               []string `protobuf:"bytes,6,rep,name=nuan_id,json=nuanId,proto3" json:"nuan_id"`
	Page                 int32    `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	Size                 int32    `protobuf:"varint,4,opt,name=size,proto3" json:"size"`
	Source               int32    `protobuf:"varint,5,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaxCodeRequest) Reset()         { *m = TaxCodeRequest{} }
func (m *TaxCodeRequest) String() string { return proto.CompactTextString(m) }
func (*TaxCodeRequest) ProtoMessage()    {}
func (*TaxCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{5}
}

func (m *TaxCodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaxCodeRequest.Unmarshal(m, b)
}
func (m *TaxCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaxCodeRequest.Marshal(b, m, deterministic)
}
func (m *TaxCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaxCodeRequest.Merge(m, src)
}
func (m *TaxCodeRequest) XXX_Size() int {
	return xxx_messageInfo_TaxCodeRequest.Size(m)
}
func (m *TaxCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TaxCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TaxCodeRequest proto.InternalMessageInfo

func (m *TaxCodeRequest) GetStructCode() string {
	if m != nil {
		return m.StructCode
	}
	return ""
}

func (m *TaxCodeRequest) GetThirdSkuIds() []string {
	if m != nil {
		return m.ThirdSkuIds
	}
	return nil
}

func (m *TaxCodeRequest) GetNuanId() []string {
	if m != nil {
		return m.NuanId
	}
	return nil
}

func (m *TaxCodeRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TaxCodeRequest) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *TaxCodeRequest) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type TaxCodeResponse struct {
	Code                 int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*TaxCodeData `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TaxCodeResponse) Reset()         { *m = TaxCodeResponse{} }
func (m *TaxCodeResponse) String() string { return proto.CompactTextString(m) }
func (*TaxCodeResponse) ProtoMessage()    {}
func (*TaxCodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{6}
}

func (m *TaxCodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaxCodeResponse.Unmarshal(m, b)
}
func (m *TaxCodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaxCodeResponse.Marshal(b, m, deterministic)
}
func (m *TaxCodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaxCodeResponse.Merge(m, src)
}
func (m *TaxCodeResponse) XXX_Size() int {
	return xxx_messageInfo_TaxCodeResponse.Size(m)
}
func (m *TaxCodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaxCodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaxCodeResponse proto.InternalMessageInfo

func (m *TaxCodeResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TaxCodeResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *TaxCodeResponse) GetData() []*TaxCodeData {
	if m != nil {
		return m.Data
	}
	return nil
}

type TaxCodeData struct {
	ThirdSkuId           string   `protobuf:"bytes,1,opt,name=third_sku_id,json=thirdSkuId,proto3" json:"third_sku_id"`
	TaxCode              string   `protobuf:"bytes,2,opt,name=tax_code,json=taxCode,proto3" json:"tax_code"`
	CategoryCode         string   `protobuf:"bytes,3,opt,name=category_code,json=categoryCode,proto3" json:"category_code"`
	TaxValue             float32  `protobuf:"fixed32,4,opt,name=tax_value,json=taxValue,proto3" json:"tax_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaxCodeData) Reset()         { *m = TaxCodeData{} }
func (m *TaxCodeData) String() string { return proto.CompactTextString(m) }
func (*TaxCodeData) ProtoMessage()    {}
func (*TaxCodeData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{7}
}

func (m *TaxCodeData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaxCodeData.Unmarshal(m, b)
}
func (m *TaxCodeData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaxCodeData.Marshal(b, m, deterministic)
}
func (m *TaxCodeData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaxCodeData.Merge(m, src)
}
func (m *TaxCodeData) XXX_Size() int {
	return xxx_messageInfo_TaxCodeData.Size(m)
}
func (m *TaxCodeData) XXX_DiscardUnknown() {
	xxx_messageInfo_TaxCodeData.DiscardUnknown(m)
}

var xxx_messageInfo_TaxCodeData proto.InternalMessageInfo

func (m *TaxCodeData) GetThirdSkuId() string {
	if m != nil {
		return m.ThirdSkuId
	}
	return ""
}

func (m *TaxCodeData) GetTaxCode() string {
	if m != nil {
		return m.TaxCode
	}
	return ""
}

func (m *TaxCodeData) GetCategoryCode() string {
	if m != nil {
		return m.CategoryCode
	}
	return ""
}

func (m *TaxCodeData) GetTaxValue() float32 {
	if m != nil {
		return m.TaxValue
	}
	return 0
}

type QueryInvoiceCompanyRequest struct {
	Keyword              string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryInvoiceCompanyRequest) Reset()         { *m = QueryInvoiceCompanyRequest{} }
func (m *QueryInvoiceCompanyRequest) String() string { return proto.CompactTextString(m) }
func (*QueryInvoiceCompanyRequest) ProtoMessage()    {}
func (*QueryInvoiceCompanyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{8}
}

func (m *QueryInvoiceCompanyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryInvoiceCompanyRequest.Unmarshal(m, b)
}
func (m *QueryInvoiceCompanyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryInvoiceCompanyRequest.Marshal(b, m, deterministic)
}
func (m *QueryInvoiceCompanyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryInvoiceCompanyRequest.Merge(m, src)
}
func (m *QueryInvoiceCompanyRequest) XXX_Size() int {
	return xxx_messageInfo_QueryInvoiceCompanyRequest.Size(m)
}
func (m *QueryInvoiceCompanyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryInvoiceCompanyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryInvoiceCompanyRequest proto.InternalMessageInfo

func (m *QueryInvoiceCompanyRequest) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type CompanyList struct {
	// 公司编码
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	// 公司名称
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompanyList) Reset()         { *m = CompanyList{} }
func (m *CompanyList) String() string { return proto.CompactTextString(m) }
func (*CompanyList) ProtoMessage()    {}
func (*CompanyList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{9}
}

func (m *CompanyList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompanyList.Unmarshal(m, b)
}
func (m *CompanyList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompanyList.Marshal(b, m, deterministic)
}
func (m *CompanyList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompanyList.Merge(m, src)
}
func (m *CompanyList) XXX_Size() int {
	return xxx_messageInfo_CompanyList.Size(m)
}
func (m *CompanyList) XXX_DiscardUnknown() {
	xxx_messageInfo_CompanyList.DiscardUnknown(m)
}

var xxx_messageInfo_CompanyList proto.InternalMessageInfo

func (m *CompanyList) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *CompanyList) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type QueryInvoiceCompanyResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message              string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 []*CompanyList `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *QueryInvoiceCompanyResponse) Reset()         { *m = QueryInvoiceCompanyResponse{} }
func (m *QueryInvoiceCompanyResponse) String() string { return proto.CompactTextString(m) }
func (*QueryInvoiceCompanyResponse) ProtoMessage()    {}
func (*QueryInvoiceCompanyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{10}
}

func (m *QueryInvoiceCompanyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryInvoiceCompanyResponse.Unmarshal(m, b)
}
func (m *QueryInvoiceCompanyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryInvoiceCompanyResponse.Marshal(b, m, deterministic)
}
func (m *QueryInvoiceCompanyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryInvoiceCompanyResponse.Merge(m, src)
}
func (m *QueryInvoiceCompanyResponse) XXX_Size() int {
	return xxx_messageInfo_QueryInvoiceCompanyResponse.Size(m)
}
func (m *QueryInvoiceCompanyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryInvoiceCompanyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryInvoiceCompanyResponse proto.InternalMessageInfo

func (m *QueryInvoiceCompanyResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *QueryInvoiceCompanyResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *QueryInvoiceCompanyResponse) GetData() []*CompanyList {
	if m != nil {
		return m.Data
	}
	return nil
}

type InvoiceCompanyInfoRequest struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceCompanyInfoRequest) Reset()         { *m = InvoiceCompanyInfoRequest{} }
func (m *InvoiceCompanyInfoRequest) String() string { return proto.CompactTextString(m) }
func (*InvoiceCompanyInfoRequest) ProtoMessage()    {}
func (*InvoiceCompanyInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{11}
}

func (m *InvoiceCompanyInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceCompanyInfoRequest.Unmarshal(m, b)
}
func (m *InvoiceCompanyInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceCompanyInfoRequest.Marshal(b, m, deterministic)
}
func (m *InvoiceCompanyInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceCompanyInfoRequest.Merge(m, src)
}
func (m *InvoiceCompanyInfoRequest) XXX_Size() int {
	return xxx_messageInfo_InvoiceCompanyInfoRequest.Size(m)
}
func (m *InvoiceCompanyInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceCompanyInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceCompanyInfoRequest proto.InternalMessageInfo

func (m *InvoiceCompanyInfoRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type Company struct {
	// 公司编码
	SpeedCode string `protobuf:"bytes,1,opt,name=speedCode,proto3" json:"speedCode"`
	// 公司名称
	CorpName string `protobuf:"bytes,2,opt,name=corpName,proto3" json:"corpName"`
	// 税号
	CorpTaxNo string `protobuf:"bytes,3,opt,name=corpTaxNo,proto3" json:"corpTaxNo"`
	// 企业地址
	CorpAddress string `protobuf:"bytes,4,opt,name=corpAddress,proto3" json:"corpAddress"`
	// 企业电话
	CorpTelephone string `protobuf:"bytes,5,opt,name=corpTelephone,proto3" json:"corpTelephone"`
	// 开户银行
	CorpBank string `protobuf:"bytes,6,opt,name=corpBank,proto3" json:"corpBank"`
	// 银行卡号
	CorpAccount          string   `protobuf:"bytes,7,opt,name=corpAccount,proto3" json:"corpAccount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Company) Reset()         { *m = Company{} }
func (m *Company) String() string { return proto.CompactTextString(m) }
func (*Company) ProtoMessage()    {}
func (*Company) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{12}
}

func (m *Company) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Company.Unmarshal(m, b)
}
func (m *Company) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Company.Marshal(b, m, deterministic)
}
func (m *Company) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Company.Merge(m, src)
}
func (m *Company) XXX_Size() int {
	return xxx_messageInfo_Company.Size(m)
}
func (m *Company) XXX_DiscardUnknown() {
	xxx_messageInfo_Company.DiscardUnknown(m)
}

var xxx_messageInfo_Company proto.InternalMessageInfo

func (m *Company) GetSpeedCode() string {
	if m != nil {
		return m.SpeedCode
	}
	return ""
}

func (m *Company) GetCorpName() string {
	if m != nil {
		return m.CorpName
	}
	return ""
}

func (m *Company) GetCorpTaxNo() string {
	if m != nil {
		return m.CorpTaxNo
	}
	return ""
}

func (m *Company) GetCorpAddress() string {
	if m != nil {
		return m.CorpAddress
	}
	return ""
}

func (m *Company) GetCorpTelephone() string {
	if m != nil {
		return m.CorpTelephone
	}
	return ""
}

func (m *Company) GetCorpBank() string {
	if m != nil {
		return m.CorpBank
	}
	return ""
}

func (m *Company) GetCorpAccount() string {
	if m != nil {
		return m.CorpAccount
	}
	return ""
}

type InvoiceCompanyInfoResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *Company `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceCompanyInfoResponse) Reset()         { *m = InvoiceCompanyInfoResponse{} }
func (m *InvoiceCompanyInfoResponse) String() string { return proto.CompactTextString(m) }
func (*InvoiceCompanyInfoResponse) ProtoMessage()    {}
func (*InvoiceCompanyInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{13}
}

func (m *InvoiceCompanyInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceCompanyInfoResponse.Unmarshal(m, b)
}
func (m *InvoiceCompanyInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceCompanyInfoResponse.Marshal(b, m, deterministic)
}
func (m *InvoiceCompanyInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceCompanyInfoResponse.Merge(m, src)
}
func (m *InvoiceCompanyInfoResponse) XXX_Size() int {
	return xxx_messageInfo_InvoiceCompanyInfoResponse.Size(m)
}
func (m *InvoiceCompanyInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceCompanyInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceCompanyInfoResponse proto.InternalMessageInfo

func (m *InvoiceCompanyInfoResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InvoiceCompanyInfoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InvoiceCompanyInfoResponse) GetData() *Company {
	if m != nil {
		return m.Data
	}
	return nil
}

type InvoiceDetailRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,12,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceDetailRequest) Reset()         { *m = InvoiceDetailRequest{} }
func (m *InvoiceDetailRequest) String() string { return proto.CompactTextString(m) }
func (*InvoiceDetailRequest) ProtoMessage()    {}
func (*InvoiceDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{14}
}

func (m *InvoiceDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceDetailRequest.Unmarshal(m, b)
}
func (m *InvoiceDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceDetailRequest.Marshal(b, m, deterministic)
}
func (m *InvoiceDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceDetailRequest.Merge(m, src)
}
func (m *InvoiceDetailRequest) XXX_Size() int {
	return xxx_messageInfo_InvoiceDetailRequest.Size(m)
}
func (m *InvoiceDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceDetailRequest proto.InternalMessageInfo

func (m *InvoiceDetailRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *InvoiceDetailRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type InvoiceDetailData struct {
	// 申请发票信息
	Apply *CreateInvoiceRequest `protobuf:"bytes,1,opt,name=apply,proto3" json:"apply"`
	// 开票状态，0未开票、1开票成功、2开票中、3开票失败
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	// 失败原因，当开票失败（status = 3）时返回
	FailReason string `protobuf:"bytes,3,opt,name=fail_reason,json=failReason,proto3" json:"fail_reason"`
	// 申请时间
	CreatedAt string `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 发票链接，多张用逗号分割
	Invoices             string   `protobuf:"bytes,5,opt,name=invoices,proto3" json:"invoices"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceDetailData) Reset()         { *m = InvoiceDetailData{} }
func (m *InvoiceDetailData) String() string { return proto.CompactTextString(m) }
func (*InvoiceDetailData) ProtoMessage()    {}
func (*InvoiceDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{15}
}

func (m *InvoiceDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceDetailData.Unmarshal(m, b)
}
func (m *InvoiceDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceDetailData.Marshal(b, m, deterministic)
}
func (m *InvoiceDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceDetailData.Merge(m, src)
}
func (m *InvoiceDetailData) XXX_Size() int {
	return xxx_messageInfo_InvoiceDetailData.Size(m)
}
func (m *InvoiceDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceDetailData proto.InternalMessageInfo

func (m *InvoiceDetailData) GetApply() *CreateInvoiceRequest {
	if m != nil {
		return m.Apply
	}
	return nil
}

func (m *InvoiceDetailData) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *InvoiceDetailData) GetFailReason() string {
	if m != nil {
		return m.FailReason
	}
	return ""
}

func (m *InvoiceDetailData) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *InvoiceDetailData) GetInvoices() string {
	if m != nil {
		return m.Invoices
	}
	return ""
}

type InvoiceDetailResponse struct {
	//状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	//消息
	Message              string             `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data                 *InvoiceDetailData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *InvoiceDetailResponse) Reset()         { *m = InvoiceDetailResponse{} }
func (m *InvoiceDetailResponse) String() string { return proto.CompactTextString(m) }
func (*InvoiceDetailResponse) ProtoMessage()    {}
func (*InvoiceDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{16}
}

func (m *InvoiceDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceDetailResponse.Unmarshal(m, b)
}
func (m *InvoiceDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceDetailResponse.Marshal(b, m, deterministic)
}
func (m *InvoiceDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceDetailResponse.Merge(m, src)
}
func (m *InvoiceDetailResponse) XXX_Size() int {
	return xxx_messageInfo_InvoiceDetailResponse.Size(m)
}
func (m *InvoiceDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceDetailResponse proto.InternalMessageInfo

func (m *InvoiceDetailResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InvoiceDetailResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InvoiceDetailResponse) GetData() *InvoiceDetailData {
	if m != nil {
		return m.Data
	}
	return nil
}

type InvoiceSendEmailRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	Email   string `protobuf:"bytes,2,opt,name=email,proto3" json:"email"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceSendEmailRequest) Reset()         { *m = InvoiceSendEmailRequest{} }
func (m *InvoiceSendEmailRequest) String() string { return proto.CompactTextString(m) }
func (*InvoiceSendEmailRequest) ProtoMessage()    {}
func (*InvoiceSendEmailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{17}
}

func (m *InvoiceSendEmailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceSendEmailRequest.Unmarshal(m, b)
}
func (m *InvoiceSendEmailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceSendEmailRequest.Marshal(b, m, deterministic)
}
func (m *InvoiceSendEmailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceSendEmailRequest.Merge(m, src)
}
func (m *InvoiceSendEmailRequest) XXX_Size() int {
	return xxx_messageInfo_InvoiceSendEmailRequest.Size(m)
}
func (m *InvoiceSendEmailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceSendEmailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceSendEmailRequest proto.InternalMessageInfo

func (m *InvoiceSendEmailRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *InvoiceSendEmailRequest) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *InvoiceSendEmailRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type InvoiceCallbackRequest struct {
	// 回调参数
	ParamsJson           string   `protobuf:"bytes,1,opt,name=params_json,json=paramsJson,proto3" json:"params_json"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceCallbackRequest) Reset()         { *m = InvoiceCallbackRequest{} }
func (m *InvoiceCallbackRequest) String() string { return proto.CompactTextString(m) }
func (*InvoiceCallbackRequest) ProtoMessage()    {}
func (*InvoiceCallbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{18}
}

func (m *InvoiceCallbackRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceCallbackRequest.Unmarshal(m, b)
}
func (m *InvoiceCallbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceCallbackRequest.Marshal(b, m, deterministic)
}
func (m *InvoiceCallbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceCallbackRequest.Merge(m, src)
}
func (m *InvoiceCallbackRequest) XXX_Size() int {
	return xxx_messageInfo_InvoiceCallbackRequest.Size(m)
}
func (m *InvoiceCallbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceCallbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceCallbackRequest proto.InternalMessageInfo

func (m *InvoiceCallbackRequest) GetParamsJson() string {
	if m != nil {
		return m.ParamsJson
	}
	return ""
}

type InvoiceStatusRequest struct {
	// 订单号
	OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
	// 前端不需要传，仅用于rpc标记用户
	ScrmId               string   `protobuf:"bytes,3,opt,name=scrm_id,json=scrmId,proto3" json:"scrm_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceStatusRequest) Reset()         { *m = InvoiceStatusRequest{} }
func (m *InvoiceStatusRequest) String() string { return proto.CompactTextString(m) }
func (*InvoiceStatusRequest) ProtoMessage()    {}
func (*InvoiceStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{19}
}

func (m *InvoiceStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceStatusRequest.Unmarshal(m, b)
}
func (m *InvoiceStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceStatusRequest.Marshal(b, m, deterministic)
}
func (m *InvoiceStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceStatusRequest.Merge(m, src)
}
func (m *InvoiceStatusRequest) XXX_Size() int {
	return xxx_messageInfo_InvoiceStatusRequest.Size(m)
}
func (m *InvoiceStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceStatusRequest proto.InternalMessageInfo

func (m *InvoiceStatusRequest) GetOrderSn() string {
	if m != nil {
		return m.OrderSn
	}
	return ""
}

func (m *InvoiceStatusRequest) GetScrmId() string {
	if m != nil {
		return m.ScrmId
	}
	return ""
}

type InvoiceStatusResponse struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 0未开票、1开票成功、2开票中、3开票失败、8开票关闭、9开票过期
	InvoiceStatus        int32    `protobuf:"varint,3,opt,name=invoice_status,json=invoiceStatus,proto3" json:"invoice_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvoiceStatusResponse) Reset()         { *m = InvoiceStatusResponse{} }
func (m *InvoiceStatusResponse) String() string { return proto.CompactTextString(m) }
func (*InvoiceStatusResponse) ProtoMessage()    {}
func (*InvoiceStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f6b5fa752a04b18, []int{20}
}

func (m *InvoiceStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvoiceStatusResponse.Unmarshal(m, b)
}
func (m *InvoiceStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvoiceStatusResponse.Marshal(b, m, deterministic)
}
func (m *InvoiceStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvoiceStatusResponse.Merge(m, src)
}
func (m *InvoiceStatusResponse) XXX_Size() int {
	return xxx_messageInfo_InvoiceStatusResponse.Size(m)
}
func (m *InvoiceStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InvoiceStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InvoiceStatusResponse proto.InternalMessageInfo

func (m *InvoiceStatusResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *InvoiceStatusResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *InvoiceStatusResponse) GetInvoiceStatus() int32 {
	if m != nil {
		return m.InvoiceStatus
	}
	return 0
}

func init() {
	proto.RegisterType((*InvoiceResponse)(nil), "oc.InvoiceResponse")
	proto.RegisterType((*CreateInvoiceRequest)(nil), "oc.CreateInvoiceRequest")
	proto.RegisterType((*CreateInvoiceResponse)(nil), "oc.CreateInvoiceResponse")
	proto.RegisterType((*CreateInvoiceResponseData)(nil), "oc.CreateInvoiceResponseData")
	proto.RegisterType((*RefundInvoiceRequest)(nil), "oc.RefundInvoiceRequest")
	proto.RegisterType((*TaxCodeRequest)(nil), "oc.TaxCodeRequest")
	proto.RegisterType((*TaxCodeResponse)(nil), "oc.TaxCodeResponse")
	proto.RegisterType((*TaxCodeData)(nil), "oc.TaxCodeData")
	proto.RegisterType((*QueryInvoiceCompanyRequest)(nil), "oc.QueryInvoiceCompanyRequest")
	proto.RegisterType((*CompanyList)(nil), "oc.CompanyList")
	proto.RegisterType((*QueryInvoiceCompanyResponse)(nil), "oc.QueryInvoiceCompanyResponse")
	proto.RegisterType((*InvoiceCompanyInfoRequest)(nil), "oc.InvoiceCompanyInfoRequest")
	proto.RegisterType((*Company)(nil), "oc.Company")
	proto.RegisterType((*InvoiceCompanyInfoResponse)(nil), "oc.InvoiceCompanyInfoResponse")
	proto.RegisterType((*InvoiceDetailRequest)(nil), "oc.InvoiceDetailRequest")
	proto.RegisterType((*InvoiceDetailData)(nil), "oc.InvoiceDetailData")
	proto.RegisterType((*InvoiceDetailResponse)(nil), "oc.InvoiceDetailResponse")
	proto.RegisterType((*InvoiceSendEmailRequest)(nil), "oc.InvoiceSendEmailRequest")
	proto.RegisterType((*InvoiceCallbackRequest)(nil), "oc.InvoiceCallbackRequest")
	proto.RegisterType((*InvoiceStatusRequest)(nil), "oc.InvoiceStatusRequest")
	proto.RegisterType((*InvoiceStatusResponse)(nil), "oc.InvoiceStatusResponse")
}

func init() { proto.RegisterFile("oc/invoice.proto", fileDescriptor_6f6b5fa752a04b18) }

var fileDescriptor_6f6b5fa752a04b18 = []byte{
	// 1109 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0x1e, 0xc7, 0x71, 0x1c, 0x1d, 0xc5, 0x71, 0xd9, 0x26, 0xad, 0x62, 0x93, 0xc6, 0xa8, 0x30,
	0x0d, 0x37, 0xe9, 0x10, 0x06, 0x66, 0xb8, 0x00, 0x26, 0x4d, 0xb8, 0x70, 0x07, 0xc2, 0x20, 0x67,
	0x7a, 0xab, 0x59, 0x4b, 0xeb, 0x46, 0x58, 0xda, 0x15, 0xda, 0x55, 0x88, 0x79, 0x06, 0x9e, 0x82,
	0x07, 0xe0, 0x96, 0xf7, 0xe0, 0x25, 0x78, 0x0d, 0x66, 0x7f, 0x24, 0x4b, 0x8e, 0xcc, 0x14, 0x4f,
	0xef, 0x74, 0xbe, 0x5d, 0x9d, 0xf3, 0xed, 0xf7, 0x9d, 0x3d, 0x96, 0xe1, 0x11, 0x0b, 0x5e, 0x46,
	0xf4, 0x8e, 0x45, 0x01, 0x39, 0x4b, 0x33, 0x26, 0x18, 0xda, 0x62, 0x81, 0xfb, 0x2d, 0xf4, 0xc7,
	0x1a, 0xf4, 0x08, 0x4f, 0x19, 0xe5, 0x04, 0x21, 0xd8, 0x0e, 0x58, 0x48, 0x9c, 0xd6, 0xa8, 0x75,
	0xda, 0xf1, 0xd4, 0x33, 0x72, 0xa0, 0x9b, 0x10, 0xce, 0xf1, 0x5b, 0xe2, 0x6c, 0x8d, 0x5a, 0xa7,
	0x96, 0x57, 0x84, 0xee, 0x1f, 0x6d, 0x38, 0xb8, 0xcc, 0x08, 0x16, 0xa4, 0xcc, 0xf3, 0x4b, 0x4e,
	0xb8, 0x40, 0x47, 0xb0, 0xcb, 0xb2, 0x90, 0x64, 0x3e, 0xa7, 0x2a, 0x95, 0xe5, 0x75, 0x55, 0x3c,
	0xa1, 0x32, 0x5b, 0x70, 0x8b, 0x29, 0x25, 0xb1, 0xca, 0xd6, 0xf1, 0x8a, 0x10, 0x1d, 0x03, 0x18,
	0x8e, 0xbe, 0x10, 0x4e, 0x5b, 0x2d, 0x5a, 0x06, 0xb9, 0x11, 0xe8, 0x09, 0xec, 0x24, 0x6c, 0x1a,
	0xc5, 0xc4, 0xd9, 0x56, 0x19, 0x4d, 0x84, 0x0e, 0xa0, 0x43, 0x12, 0x1c, 0xc5, 0x4e, 0x47, 0xc1,
	0x3a, 0x40, 0x1f, 0xc1, 0x5e, 0xc0, 0x92, 0x14, 0xd3, 0x85, 0xaf, 0x0e, 0xb4, 0xa3, 0x16, 0x6d,
	0x83, 0x5d, 0xca, 0x73, 0xbd, 0x80, 0x7e, 0x14, 0x12, 0x2a, 0xa2, 0xd9, 0xc2, 0xa7, 0x79, 0x32,
	0x25, 0x99, 0xd3, 0x55, 0xbb, 0xf6, 0x0b, 0xf8, 0x5a, 0xa1, 0x68, 0x08, 0xd6, 0x14, 0xd3, 0xb9,
	0x4f, 0x71, 0x42, 0x9c, 0x5d, 0xb5, 0x65, 0x57, 0x02, 0xd7, 0x38, 0x21, 0xe8, 0x04, 0x6c, 0xbd,
	0xa8, 0x33, 0x58, 0x6a, 0x19, 0xd4, 0xb2, 0x7e, 0xfb, 0x05, 0xf4, 0x0b, 0x26, 0x38, 0x0c, 0x33,
	0xc2, 0xb9, 0x03, 0xba, 0x8c, 0x81, 0x2f, 0x34, 0x5a, 0xa5, 0xac, 0x2a, 0xd9, 0x35, 0xca, 0xaa,
	0xd8, 0x53, 0xe8, 0xf2, 0x20, 0x4b, 0xfc, 0x28, 0x74, 0xf6, 0xb4, 0x08, 0x32, 0x1c, 0x87, 0x52,
	0xf0, 0x14, 0x2f, 0x7c, 0x11, 0x25, 0xc4, 0xe9, 0x8d, 0x5a, 0xa7, 0x6d, 0xaf, 0x9b, 0xe2, 0xc5,
	0x4d, 0x94, 0x10, 0xf7, 0x1e, 0x0e, 0x57, 0x3c, 0xda, 0xc4, 0x6b, 0xf4, 0x19, 0x6c, 0x87, 0x58,
	0x60, 0xe5, 0x8b, 0x7d, 0x7e, 0x7c, 0xc6, 0x82, 0xb3, 0xc6, 0xb4, 0x57, 0x58, 0x60, 0x4f, 0x6d,
	0x75, 0x19, 0x1c, 0xad, 0xdd, 0x22, 0xed, 0x9c, 0xb0, 0x3c, 0x0b, 0x8a, 0xfa, 0x26, 0x42, 0x23,
	0xb0, 0x2f, 0x97, 0x26, 0x19, 0x16, 0x55, 0x48, 0x72, 0xfc, 0x51, 0x36, 0xd3, 0x35, 0x53, 0x64,
	0x2c, 0xaf, 0x08, 0xdd, 0x1f, 0xe0, 0xc0, 0x23, 0xb3, 0x9c, 0x86, 0x2b, 0xed, 0x38, 0x04, 0x2b,
	0x53, 0xf8, 0xb2, 0x1f, 0x77, 0x35, 0xf0, 0x5f, 0x0d, 0xe9, 0xfe, 0xd9, 0x82, 0xfd, 0x1b, 0x7c,
	0x2f, 0x8b, 0x16, 0x99, 0x4e, 0xc0, 0xe6, 0x22, 0xcb, 0x03, 0xe1, 0x97, 0xd2, 0x59, 0x1e, 0x68,
	0x48, 0x91, 0x73, 0xa1, 0x27, 0x6e, 0xa3, 0x2c, 0xf4, 0xf9, 0x3c, 0xf7, 0xa3, 0x90, 0x3b, 0x5b,
	0xa3, 0xb6, 0x3c, 0x80, 0x02, 0x27, 0xf3, 0x7c, 0x1c, 0x72, 0xe9, 0x22, 0xcd, 0x31, 0x95, 0x2e,
	0xee, 0xa8, 0xd5, 0x1d, 0x19, 0x8e, 0x43, 0xe9, 0x48, 0x2a, 0xa5, 0xd7, 0xbd, 0xaf, 0x9e, 0x25,
	0xc6, 0xa3, 0xdf, 0x74, 0xd3, 0x77, 0x3c, 0xf5, 0x2c, 0xb5, 0xe3, 0x5a, 0xbb, 0x8e, 0xd6, 0x4e,
	0x47, 0x6e, 0x08, 0xfd, 0x92, 0xef, 0x46, 0x26, 0x3f, 0x2f, 0x4d, 0x6e, 0x9f, 0xda, 0xe7, 0x7d,
	0x69, 0xb2, 0x49, 0x58, 0xb1, 0xf5, 0xf7, 0x16, 0xd8, 0x15, 0x14, 0x8d, 0x60, 0xaf, 0x7a, 0xe4,
	0x42, 0x94, 0xe5, 0x89, 0x65, 0x77, 0x0a, 0x7c, 0xaf, 0x25, 0x33, 0x15, 0x85, 0x4e, 0x80, 0x9e,
	0x43, 0x2f, 0xc0, 0x82, 0xbc, 0x65, 0x99, 0xb9, 0xa8, 0xda, 0xd2, 0xbd, 0x02, 0x54, 0x9b, 0x86,
	0x60, 0xc9, 0xf7, 0xef, 0x70, 0x9c, 0x6b, 0x21, 0xb6, 0x3c, 0x99, 0xf0, 0x8d, 0x8c, 0xdd, 0x2f,
	0x61, 0xf0, 0x53, 0x4e, 0xb2, 0x85, 0xf1, 0xdc, 0x74, 0x4a, 0x61, 0x98, 0x03, 0xdd, 0x39, 0x59,
	0xfc, 0xca, 0xb2, 0x82, 0x57, 0x11, 0xba, 0x5f, 0x94, 0x8d, 0xf6, 0x7d, 0xc4, 0x45, 0x4d, 0x28,
	0xcb, 0x08, 0x85, 0x60, 0x5b, 0xdd, 0x44, 0xcd, 0x59, 0x3d, 0xbb, 0x29, 0x0c, 0x1b, 0xcb, 0xbd,
	0x2f, 0xbd, 0x2b, 0x9c, 0x8c, 0xde, 0x2f, 0xe1, 0xa8, 0x5e, 0x6c, 0x4c, 0x67, 0xac, 0x38, 0x5f,
	0x03, 0x6d, 0xf7, 0x9f, 0x16, 0x74, 0xcd, 0x56, 0xf4, 0x21, 0x58, 0x3c, 0x25, 0x24, 0xbc, 0x5c,
	0x6e, 0x5a, 0x02, 0x68, 0x00, 0xbb, 0x01, 0xcb, 0xd2, 0xeb, 0xe5, 0x21, 0xcb, 0x58, 0xbe, 0x29,
	0x9f, 0x6f, 0xf0, 0x7d, 0x79, 0xd1, 0x96, 0x80, 0xbc, 0xa6, 0x32, 0x30, 0xb3, 0xcb, 0x8c, 0xe4,
	0x2a, 0x84, 0x3e, 0x86, 0x9e, 0xda, 0x4e, 0x62, 0x92, 0xde, 0x32, 0x4a, 0xcc, 0x7c, 0xae, 0x83,
	0x05, 0x83, 0x57, 0x98, 0xce, 0xcd, 0x8c, 0x2e, 0xe3, 0xb2, 0x46, 0x10, 0xb0, 0x9c, 0x0a, 0x33,
	0x9c, 0xab, 0x90, 0x3b, 0x87, 0x41, 0x93, 0x34, 0x1b, 0x79, 0x71, 0x52, 0x1b, 0x70, 0x76, 0xc5,
	0x0b, 0xe3, 0xc3, 0x6b, 0x38, 0x30, 0xc5, 0xae, 0x88, 0xc0, 0x51, 0xfc, 0x0e, 0x3f, 0x76, 0xeb,
	0xe6, 0xb5, 0xfb, 0x57, 0x0b, 0x3e, 0xa8, 0x25, 0x53, 0x37, 0xe9, 0x0c, 0x3a, 0x38, 0x4d, 0xe3,
	0x85, 0x4a, 0x63, 0x9f, 0x3b, 0x0d, 0x43, 0x56, 0x95, 0xf4, 0xf4, 0x36, 0x35, 0x07, 0x04, 0x16,
	0x39, 0x37, 0x93, 0xcb, 0x44, 0x72, 0x4a, 0xcd, 0x70, 0x14, 0xfb, 0x19, 0xc1, 0x9c, 0x51, 0x63,
	0x1e, 0xcc, 0x14, 0x67, 0x89, 0xc8, 0x9f, 0xda, 0x40, 0xe5, 0x0d, 0x7d, 0x2c, 0x8c, 0x79, 0x96,
	0x41, 0x2e, 0x84, 0x34, 0xc5, 0xfc, 0xee, 0x72, 0xe3, 0x5a, 0x19, 0xbb, 0x29, 0x1c, 0xae, 0xa8,
	0xb0, 0x91, 0xda, 0x9f, 0xd6, 0xd4, 0x3e, 0x94, 0x27, 0x7d, 0xa0, 0x87, 0xd1, 0x3d, 0x80, 0xa7,
	0x66, 0x69, 0x42, 0x68, 0xf8, 0x5d, 0xf2, 0x6e, 0xd2, 0x97, 0x9f, 0x05, 0x5b, 0xd5, 0xcf, 0x82,
	0x8a, 0x21, 0xed, 0x9a, 0x21, 0x5f, 0xc1, 0x93, 0xa2, 0x93, 0x70, 0x1c, 0x4f, 0x71, 0x30, 0xaf,
	0x8c, 0xfc, 0x14, 0x67, 0x38, 0xe1, 0xfe, 0xcf, 0x52, 0x4c, 0x33, 0xdd, 0x34, 0xf4, 0x9a, 0x33,
	0x5a, 0xe9, 0x8b, 0x89, 0x92, 0xff, 0xff, 0xf5, 0x45, 0x9d, 0x46, 0x5c, 0xaa, 0x5b, 0xe4, 0xda,
	0x48, 0xdd, 0x4f, 0x60, 0xbf, 0xf8, 0x94, 0x32, 0x0d, 0xa2, 0x7f, 0x52, 0x7a, 0x51, 0x35, 0xf9,
	0xf9, 0xdf, 0xdb, 0xb0, 0x5f, 0x4a, 0x9b, 0xdd, 0x45, 0x01, 0x41, 0x6f, 0xe0, 0x71, 0xc3, 0x78,
	0x43, 0xcf, 0xa4, 0x41, 0xeb, 0xc7, 0xec, 0xe0, 0x64, 0xed, 0xba, 0xe1, 0x3f, 0x01, 0xf4, 0xf0,
	0xa6, 0xa2, 0xe3, 0x8a, 0xef, 0x0f, 0x87, 0xdb, 0xe0, 0xd9, 0xba, 0x65, 0x93, 0xf4, 0x0a, 0x7a,
	0x35, 0xb5, 0x90, 0x53, 0x79, 0xa1, 0x66, 0xc6, 0xe0, 0xa8, 0x61, 0xc5, 0x64, 0xf9, 0x1a, 0xf6,
	0xcc, 0xc2, 0x85, 0xba, 0x55, 0x6b, 0xaf, 0xdd, 0xe0, 0x71, 0x25, 0x49, 0x03, 0x09, 0xdd, 0xb9,
	0x35, 0x12, 0xb5, 0x49, 0x51, 0x23, 0xb1, 0x72, 0x7b, 0xae, 0xe0, 0xd1, 0x6a, 0x93, 0xa3, 0x61,
	0x95, 0xf3, 0x4a, 0xeb, 0x37, 0x73, 0x79, 0x55, 0x7e, 0xd1, 0x17, 0x5d, 0x8c, 0x06, 0x55, 0x0d,
	0xeb, 0xad, 0xdd, 0x9c, 0xe3, 0x1b, 0xe8, 0xd5, 0x3e, 0xa2, 0xf4, 0x79, 0x9a, 0xbe, 0xab, 0x1a,
	0xdf, 0x9f, 0xee, 0xa8, 0x3f, 0x18, 0x9f, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x7a, 0x03, 0xa4,
	0xb7, 0x74, 0x0c, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// InvoiceServiceClient is the client API for InvoiceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InvoiceServiceClient interface {
	// 模糊查询开票公司
	QueryInvoiceCompany(ctx context.Context, in *QueryInvoiceCompanyRequest, opts ...grpc.CallOption) (*QueryInvoiceCompanyResponse, error)
	InvoiceCompanyInfo(ctx context.Context, in *InvoiceCompanyInfoRequest, opts ...grpc.CallOption) (*InvoiceCompanyInfoResponse, error)
	// 开票状态
	InvoiceStatus(ctx context.Context, in *InvoiceStatusRequest, opts ...grpc.CallOption) (*InvoiceStatusResponse, error)
	// 开票申请
	InvoiceApply(ctx context.Context, in *CreateInvoiceRequest, opts ...grpc.CallOption) (*InvoiceResponse, error)
	// 发票详情
	InvoiceDetail(ctx context.Context, in *InvoiceDetailRequest, opts ...grpc.CallOption) (*InvoiceDetailResponse, error)
	// 发送邮件
	InvoiceSendEmail(ctx context.Context, in *InvoiceSendEmailRequest, opts ...grpc.CallOption) (*InvoiceResponse, error)
	// 发票回调
	InvoiceCallback(ctx context.Context, in *InvoiceCallbackRequest, opts ...grpc.CallOption) (*InvoiceResponse, error)
	// 发票退款，红冲操作
	RefundInvoice(ctx context.Context, in *RefundInvoiceRequest, opts ...grpc.CallOption) (*InvoiceResponse, error)
}

type invoiceServiceClient struct {
	cc *grpc.ClientConn
}

func NewInvoiceServiceClient(cc *grpc.ClientConn) InvoiceServiceClient {
	return &invoiceServiceClient{cc}
}

func (c *invoiceServiceClient) QueryInvoiceCompany(ctx context.Context, in *QueryInvoiceCompanyRequest, opts ...grpc.CallOption) (*QueryInvoiceCompanyResponse, error) {
	out := new(QueryInvoiceCompanyResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/QueryInvoiceCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceCompanyInfo(ctx context.Context, in *InvoiceCompanyInfoRequest, opts ...grpc.CallOption) (*InvoiceCompanyInfoResponse, error) {
	out := new(InvoiceCompanyInfoResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceCompanyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceStatus(ctx context.Context, in *InvoiceStatusRequest, opts ...grpc.CallOption) (*InvoiceStatusResponse, error) {
	out := new(InvoiceStatusResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceApply(ctx context.Context, in *CreateInvoiceRequest, opts ...grpc.CallOption) (*InvoiceResponse, error) {
	out := new(InvoiceResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceDetail(ctx context.Context, in *InvoiceDetailRequest, opts ...grpc.CallOption) (*InvoiceDetailResponse, error) {
	out := new(InvoiceDetailResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceSendEmail(ctx context.Context, in *InvoiceSendEmailRequest, opts ...grpc.CallOption) (*InvoiceResponse, error) {
	out := new(InvoiceResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceSendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) InvoiceCallback(ctx context.Context, in *InvoiceCallbackRequest, opts ...grpc.CallOption) (*InvoiceResponse, error) {
	out := new(InvoiceResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/InvoiceCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *invoiceServiceClient) RefundInvoice(ctx context.Context, in *RefundInvoiceRequest, opts ...grpc.CallOption) (*InvoiceResponse, error) {
	out := new(InvoiceResponse)
	err := c.cc.Invoke(ctx, "/oc.InvoiceService/RefundInvoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InvoiceServiceServer is the server API for InvoiceService service.
type InvoiceServiceServer interface {
	// 模糊查询开票公司
	QueryInvoiceCompany(context.Context, *QueryInvoiceCompanyRequest) (*QueryInvoiceCompanyResponse, error)
	InvoiceCompanyInfo(context.Context, *InvoiceCompanyInfoRequest) (*InvoiceCompanyInfoResponse, error)
	// 开票状态
	InvoiceStatus(context.Context, *InvoiceStatusRequest) (*InvoiceStatusResponse, error)
	// 开票申请
	InvoiceApply(context.Context, *CreateInvoiceRequest) (*InvoiceResponse, error)
	// 发票详情
	InvoiceDetail(context.Context, *InvoiceDetailRequest) (*InvoiceDetailResponse, error)
	// 发送邮件
	InvoiceSendEmail(context.Context, *InvoiceSendEmailRequest) (*InvoiceResponse, error)
	// 发票回调
	InvoiceCallback(context.Context, *InvoiceCallbackRequest) (*InvoiceResponse, error)
	// 发票退款，红冲操作
	RefundInvoice(context.Context, *RefundInvoiceRequest) (*InvoiceResponse, error)
}

// UnimplementedInvoiceServiceServer can be embedded to have forward compatible implementations.
type UnimplementedInvoiceServiceServer struct {
}

func (*UnimplementedInvoiceServiceServer) QueryInvoiceCompany(ctx context.Context, req *QueryInvoiceCompanyRequest) (*QueryInvoiceCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryInvoiceCompany not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceCompanyInfo(ctx context.Context, req *InvoiceCompanyInfoRequest) (*InvoiceCompanyInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceCompanyInfo not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceStatus(ctx context.Context, req *InvoiceStatusRequest) (*InvoiceStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceStatus not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceApply(ctx context.Context, req *CreateInvoiceRequest) (*InvoiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceApply not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceDetail(ctx context.Context, req *InvoiceDetailRequest) (*InvoiceDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceDetail not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceSendEmail(ctx context.Context, req *InvoiceSendEmailRequest) (*InvoiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceSendEmail not implemented")
}
func (*UnimplementedInvoiceServiceServer) InvoiceCallback(ctx context.Context, req *InvoiceCallbackRequest) (*InvoiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvoiceCallback not implemented")
}
func (*UnimplementedInvoiceServiceServer) RefundInvoice(ctx context.Context, req *RefundInvoiceRequest) (*InvoiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundInvoice not implemented")
}

func RegisterInvoiceServiceServer(s *grpc.Server, srv InvoiceServiceServer) {
	s.RegisterService(&_InvoiceService_serviceDesc, srv)
}

func _InvoiceService_QueryInvoiceCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryInvoiceCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).QueryInvoiceCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/QueryInvoiceCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).QueryInvoiceCompany(ctx, req.(*QueryInvoiceCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceCompanyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoiceCompanyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceCompanyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceCompanyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceCompanyInfo(ctx, req.(*InvoiceCompanyInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoiceStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceStatus(ctx, req.(*InvoiceStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInvoiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceApply(ctx, req.(*CreateInvoiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoiceDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceDetail(ctx, req.(*InvoiceDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceSendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoiceSendEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceSendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceSendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceSendEmail(ctx, req.(*InvoiceSendEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_InvoiceCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoiceCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).InvoiceCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/InvoiceCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).InvoiceCallback(ctx, req.(*InvoiceCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InvoiceService_RefundInvoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundInvoiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceServiceServer).RefundInvoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oc.InvoiceService/RefundInvoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceServiceServer).RefundInvoice(ctx, req.(*RefundInvoiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _InvoiceService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oc.InvoiceService",
	HandlerType: (*InvoiceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryInvoiceCompany",
			Handler:    _InvoiceService_QueryInvoiceCompany_Handler,
		},
		{
			MethodName: "InvoiceCompanyInfo",
			Handler:    _InvoiceService_InvoiceCompanyInfo_Handler,
		},
		{
			MethodName: "InvoiceStatus",
			Handler:    _InvoiceService_InvoiceStatus_Handler,
		},
		{
			MethodName: "InvoiceApply",
			Handler:    _InvoiceService_InvoiceApply_Handler,
		},
		{
			MethodName: "InvoiceDetail",
			Handler:    _InvoiceService_InvoiceDetail_Handler,
		},
		{
			MethodName: "InvoiceSendEmail",
			Handler:    _InvoiceService_InvoiceSendEmail_Handler,
		},
		{
			MethodName: "InvoiceCallback",
			Handler:    _InvoiceService_InvoiceCallback_Handler,
		},
		{
			MethodName: "RefundInvoice",
			Handler:    _InvoiceService_RefundInvoice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oc/invoice.proto",
}
