package services

import (
	"context"
	"dispatch-center/dtos"
	"dispatch-center/models"
	"dispatch-center/proto/ic"
	"reflect"
	"testing"

	"github.com/limitedlee/microservice/example/proto"
	"google.golang.org/grpc"
)

func TestDemolitionOrderService_GetWarehouse(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		comefrom int
		province string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
		{name: "获取仓库信息", args: args{
			comefrom: 1,
			province: "北京",
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.GetWarehouse(tt.args.comefrom, tt.args.province); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("GetWarehouse() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_FirstToWarehouse(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouses []*dtos.WarehouseModel
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
		want1  int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			got, got1 := d.FirstToWarehouse(tt.args.warehouses)
			if got != tt.want {
				t.Errorf("FirstToWarehouse() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("FirstToWarehouse() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestDemolitionOrderService_FreezeStock(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouseOrders []*models.WarehouseOrder
		memberId        string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			got, err := d.FreezeStock(tt.args.warehouseOrders, tt.args.memberId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FreezeStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FreezeStock() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_GetGoodsStore(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		goodsId     string
		warehouseId int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   int
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.GetGoodsStore(tt.args.goodsId, tt.args.warehouseId); got != tt.want {
				t.Errorf("GetGoodsStore() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_GetWarehouseArea(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		comefrom int
		province string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*models.WarehouseArea
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.GetWarehouseArea(tt.args.comefrom, tt.args.province); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseArea() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_GetWarehouseModels(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		comefrom int
		province string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
		{name: "dd",
			args: args{
				comefrom: 1,
				province: "北京",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.GetWarehouseModels(tt.args.comefrom, tt.args.province); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseModels() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_SetWarehouseOrder(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		goodsStores []*dtos.GoodsStoreDto
		orderId     string
		warehouseId int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*models.WarehouseOrder
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.SetWarehouseOrder(tt.args.goodsStores, tt.args.orderId, tt.args.warehouseId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetWarehouseOrder() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_WarehouseSortAreaLevel(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouses []*dtos.WarehouseModel
		number     int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.WarehouseSortAreaLevel(tt.args.warehouses, tt.args.number); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WarehouseSortAreaLevel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_WarehouseSortGoodsNumber(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouses []*dtos.WarehouseModel
		number     int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.WarehouseSortGoodsNumber(tt.args.warehouses, tt.args.number); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WarehouseSortGoodsNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_WarehouseSortGoodsType(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouses []*dtos.WarehouseModel
		number     int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.WarehouseSortGoodsType(tt.args.warehouses, tt.args.number); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WarehouseSortGoodsType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDemolitionOrderService_WarehouseSortLevel(t *testing.T) {
	type fields struct {
		DBService DBService
	}
	type args struct {
		warehouses []*dtos.WarehouseModel
		number     int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*dtos.WarehouseModel
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DemolitionOrderService{
				DBService: tt.fields.DBService,
			}
			if got := d.WarehouseSortLevel(tt.args.warehouses, tt.args.number); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WarehouseSortLevel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRemoveRepByMap(t *testing.T) {
	type args struct {
		slc []dtos.ProductOrderDto
	}
	tests := []struct {
		name string
		args args
		want []dtos.ProductOrderDto
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveRepByMap(tt.args.slc); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveRepByMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getClient(t *testing.T) {
	tests := []struct {
		name  string
		want  *grpc.ClientConn
		want1 context.Context
		want2 ic.InventoryServiceClient
		want3 context.CancelFunc
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := getClient()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getClient() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getClient() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("getClient() got2 = %v, want %v", got2, tt.want2)
			}
			if !reflect.DeepEqual(got3, tt.want3) {
				t.Errorf("getClient() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}

func Test_get_Map_MaxMap_int(t *testing.T) {
	type args struct {
		mp map[int]int
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}

func Test_get_Map_MaxMap_int32(t *testing.T) {
	type args struct {
		mp map[int]int32
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}
