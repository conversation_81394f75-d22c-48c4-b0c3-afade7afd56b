package models

import (
	"time"
)

type OrderInfo struct {
	Id         int64   `xorm:"pk autoincr index BIGINT(20)"`
	Orderid    string  `xorm:"not null comment('主键') unique CHAR(80)"`
	Memberid   string  `xorm:"not null comment('用户编号') index CHAR(32)"`
	Ordermoney int `xorm:"not null comment('订单金额') int"`
	Ordertype  int     `xorm:"not null comment('1 咨询订单) TINYINT(255)"`
	Ordertypedetail int `xorm:"default NULL comment('订单类型详细的子类型') TINYINT(255)"`
	Orderstate      int `xorm:"not null comment('订单状态：1-未支付，2-已支付，3-已退款，4-已取消，5-退款中') INT(11)"`
	Orderdetail       string    `xorm:"default 'NULL' comment('订单明细，暂用 json存数据') TEXT"`
	Useragent         int       `xorm:"default NULL INT(11)"`
	Platformid        int       `xorm:"not null comment('订单所属平台') INT(11)"`
	Belonghospitalid  string    `xorm:"default ''0'' comment('订单的所属分院') VARCHAR(50)"`
	Createtime        time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') TIMESTAMP"`
	Lasttime          time.Time `xorm:"not null default 'current_timestamp()' comment('最后修改时间') index TIMESTAMP"`
	Orderchildenstate int       `xorm:"not null default 0 comment('订单子状态：') INT(11)"`
	Isevaluate  int `xorm:"not null default 0 comment('是否评价：0-未评价，1-已评价') INT(11)"`
	Ispostupet  int `xorm:"default 0 comment('是否推送电商(0-否，1-是)') INT(11)"`
	Isnotify    int `xorm:"default 0 comment('是否发送5分钟通知(0-否，1-是)') INT(11)"`
	Ordersource int `xorm:"default 0 comment('1.A8 ,2管易 ,3门店') INT(11)"`
	Status      int `xorm:"default 0 comment('0.初始化') INT(11)"`
	Province   string    `xorm:"not null default '''' comment('收货地址城市') VARCHAR(100)"`
	City       string    `xorm:"not null default '''' comment('收货地址城市') VARCHAR(100)"`
	Region     string    `xorm:"not null default '''' comment('收货地址区域') VARCHAR(100)"`
}
