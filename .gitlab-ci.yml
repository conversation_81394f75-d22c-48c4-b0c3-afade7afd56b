## dockerfile  .gitlab-ci ŵ
stages:
    - deploy
 
deploy_dev_job:
    stage: deploy
    before_script:
        - go env
        - go mod tidy
        - go mod download
        - GOOS=linux  go build -ldflags="-s -w" -o  ./dispatchcenter
    script:
        - docker build -t dispatchcenter .
        - docker login --username=rppet *************:5000 --password=H2cB3^A@h^K65dAB99kj
        - docker tag dispatchcenter *************:5000/datacenter/dispatchcenter:latest && docker push *************:5000/datacenter/dispatchcenter:latest && docker rmi *************:5000/datacenter/dispatchcenter:latest
        - if [ "$(docker ps -a -q -f name=dispatchcenter)" ]; then docker rm -f dispatchcenter; fi
        - docker run -d -p 11006:11006 --net=host --restart=always -e ASPNETCORE_ENVIRONMENT='Staging' -v /usr/share/zoneinfo/Asia/Shanghai:/usr/share/zoneinfo/Asia/Shanghai -v /etc/localtime:/etc/localtime -v /var/log/docker:/logs -v /config/dispatchcenter/appsetting.toml:/appsetting.toml --name dispatchcenter dispatchcenter
