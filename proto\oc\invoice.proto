syntax = "proto3";
package oc;

service InvoiceService {
    // 模糊查询开票公司
    rpc QueryInvoiceCompany (QueryInvoiceCompanyRequest) returns (QueryInvoiceCompanyResponse);
    rpc InvoiceCompanyInfo (InvoiceCompanyInfoRequest) returns (InvoiceCompanyInfoResponse);
    // 开票状态
    rpc InvoiceStatus (InvoiceStatusRequest) returns (InvoiceStatusResponse);
    // 开票申请
    rpc InvoiceApply (CreateInvoiceRequest) returns (InvoiceResponse);
    // 发票详情
    rpc InvoiceDetail (InvoiceDetailRequest) returns (InvoiceDetailResponse);
    // 发送邮件
    rpc InvoiceSendEmail (InvoiceSendEmailRequest) returns (InvoiceResponse);
    // 发票回调
    rpc InvoiceCallback (InvoiceCallbackRequest) returns (InvoiceResponse);
    // 发票退款，红冲操作
    rpc RefundInvoice (RefundInvoiceRequest) returns (InvoiceResponse);
}

message InvoiceResponse {
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
}

// 开票请求参数
message CreateInvoiceRequest {
    // 订单号
    string order_sn = 1;
    // 前端不需要传
    int32 channel = 2;
    // 发票类型 1个人、2企业
    int32 invoice_tt = 3;
    // 手机号码/企业电话
    string mobile = 4;
    // 电子邮箱
    string email = 5;
    // 前端不需要传
    string company_code = 6;
    // 纳税人识别号
    string identify_number = 7;
    // 开户银行
    string bank_name = 8;
    // 银行卡号
    string bank_number = 9;
    // 企业地址
    string company_address = 10;
    // 企业名称
    string company_name = 11;
    // 前端不需要传，仅用于标记用户
    string scrm_id = 12;
    // 支付时间
    int64 pay_time = 13;
}

// 开票返回参数
message CreateInvoiceResponse {
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    CreateInvoiceResponseData data = 3;
}
message CreateInvoiceResponseData {
    int32 Source = 1;
    string CompanyCode = 2;
    string OrderNo = 3; // 开票申请单号
}

// 退款开票申请
message RefundInvoiceRequest {
    string refund_sn = 1;
    int32 channel = 2;
}

// 税收编码请求参数
message TaxCodeRequest {
    string struct_code = 1; // 财务编码
    repeated string third_sku_ids = 2;
    repeated string nuan_id = 6;  // 包装费和配送费查询才会用
    int32 page = 3;
    int32 size = 4;
    int32 source = 5;
}

message TaxCodeResponse {
    int32 code = 1;
    string message = 2;
    repeated TaxCodeData data = 3;
}
message TaxCodeData {
    string third_sku_id = 1;
    string tax_code = 2;
    string category_code = 3;
    float  tax_value=4;
}

message QueryInvoiceCompanyRequest {
    string keyword = 1;
}

message CompanyList {
    // 公司编码
    string code = 1;
    // 公司名称
    string name = 2;
}

message QueryInvoiceCompanyResponse {
    // 状态码
    int32 code = 1;
    // 错误信息
    string message = 2;
    repeated CompanyList data = 3;
}

message InvoiceCompanyInfoRequest {
    string code = 1;
}

message Company {
    // 公司编码
    string speedCode = 1;
    // 公司名称
    string corpName = 2;
    // 税号
    string corpTaxNo = 3;
    // 企业地址
    string corpAddress = 4;
    // 企业电话
    string corpTelephone = 5;
    // 开户银行
    string corpBank = 6;
    // 银行卡号
    string corpAccount = 7;
}

message InvoiceCompanyInfoResponse {
    // 状态码
    int32 code = 1;
    // 错误信息
    string message = 2;
    Company data = 3;
}

message InvoiceDetailRequest {
    // 订单号
    string order_sn = 1;
    // 前端不需要传，仅用于rpc标记用户
    string scrm_id = 12;
}

message InvoiceDetailData {
    // 申请发票信息
    CreateInvoiceRequest apply = 1;
    // 开票状态，0未开票、1开票成功、2开票中、3开票失败
    int32 status = 2;
    // 失败原因，当开票失败（status = 3）时返回
    string fail_reason = 3;
    // 申请时间
    string created_at = 4;
    // 发票链接，多张用逗号分割
    string invoices = 5;
}

message InvoiceDetailResponse {
    //状态码
    int32 code = 1;
    //消息
    string message = 2;
    InvoiceDetailData data = 3;
}

message InvoiceSendEmailRequest {
    // 订单号
    string order_sn = 1;
    string email = 2;
    // 前端不需要传，仅用于rpc标记用户
    string scrm_id = 3;
}

message InvoiceCallbackRequest {
    // 回调参数
    string params_json = 1;
}

message InvoiceStatusRequest {
    // 订单号
    string order_sn = 1;
    // 前端不需要传，仅用于rpc标记用户
    string scrm_id = 3;
}

message InvoiceStatusResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 0未开票、1开票成功、2开票中、3开票失败、8开票关闭、9开票过期
    int32 invoice_status = 3;
}