package models

import (
	"time"
)

type OrderGoodsDetail struct {
	Id              string  `xorm:"not null pk comment('子订单编号(guid)') VARCHAR(32)"`
	Orderid         string  `xorm:"not null comment('订单编号(guid)') index VARCHAR(80)"`
	Goodsid         string  `xorm:"not null comment('商品编号(货号)') index VARCHAR(50)"`
	Barcode         string  `xorm:"default 'NULL' comment('商品主编码') VARCHAR(50)"`
	Name            string  `xorm:"not null comment('商品名称') VARCHAR(255)"`
	Goodsimage      string  `xorm:"default 'NULL' comment('商品图片') TEXT"`
	Univalence      int `xorm:"not null comment('商品单价') INT"`
	Sellprice       int `xorm:"not null comment('商品售价') INT"`
	Quantity        int     `xorm:"not null comment('商品数量') INT(11)"`
	Unit            string  `xorm:"not null comment('商品销售单位') VARCHAR(10)"`
	Applyhospitalid string  `xorm:"default ''0'' comment('商品适用分院（0--所有分院，其他对应分院编号）') VARCHAR(50)"`
	Chargeoff       int     `xorm:"not null comment('1-不用核销 2-需要核销 3-已核销') INT(4)"`
 	Chargeoffcode       string    `xorm:"default 'NULL' comment('核销码') VARCHAR(60)"`
	Chargeoffhospitalid string    `xorm:"default 'NULL' comment('核销对象-分院编号') VARCHAR(255)"`
	Chargeofftime       time.Time `xorm:"default '2000-01-01 00:00:00' comment('核销时间') DATETIME"`
	Createtime          time.Time `xorm:"not null default 'current_timestamp()' comment('创建时间') DATETIME"`
	Lasttime            time.Time `xorm:"not null default 'current_timestamp()' comment('最后操作时间') DATETIME"`
	Source              int       `xorm:"default NULL comment('来源 从请求头获取') INT(11)"`
	Useragent           int       `xorm:"default NULL comment('UA 从请求头获取') INT(11)"`
	Chargeoffmemberid   string    `xorm:"default '0' comment('核销对象-用户id') VARCHAR(100)"`
}
