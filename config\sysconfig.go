package config

import (
	"github.com/BurntSushi/toml"
	"log"
)

type SysConfig struct {
	//数据库链接字符串
	MsSql struct {
		Host    string
		Port    int
		User    string
		Pwd     string
		Default string
	}

	Aes struct {
		Ciphertext string
	}

	Cache struct {
		Host    string
		Port    int
		Pwd     string
		Default int
	}

	Service struct {
		Name        string
		Displayname string
		Version     string
		Host        string
		Port        int
	}

	InventoryCenterService struct {
		Appid string
		Address string

	}
}

func (m *SysConfig) LoadConfig() (service SysConfig) {
	_, err := toml.DecodeFile("appsetting.toml", &m)
	if err != nil {
		log.Fatal(err)
	}
	return *m
}
