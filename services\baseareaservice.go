package services

import (
	"context"
	"dispatch-center/dtos"
	"dispatch-center/models"
	proto "dispatch-center/proto/dc"
	"dispatch-center/utils"
	"encoding/json"
)

type BaseAreaService struct {
	DBService
}

// BaseArea 获取区域数据
func (s *BaseAreaService) BaseArea(ctx context.Context, params *proto.BaseAreaRequest) (*proto.BaseAreaResponse, error) {
	res := new(proto.BaseAreaResponse)
	var list []models.BaseArea
	var client = utils.ConnectClusterRedis()
	defer client.Close()
	val, err := client.Get("dispatchcenter:area").Result()
	if err != nil || len(val) == 0 || val == "" || val == "null" {
		//if err == redis.Nil {
		//}
		sqlStr := "SELECT area_id,area_name,area_parent_id,area_sort,area_deep,area_region FROM  base_area  "
		dbConn := s.GetConn()
		defer dbConn.Close()

		dbConn.SQL(sqlStr).Find(&list)
		data, _ := json.Marshal(list)
		client.Set("dispatchcenter:area", string(data), 0)
	}
	json.Unmarshal([]byte(val), &list)

	//获取大区
	if len(list) > 0 {
		for a := 0; a < len(list); a++ {
			if list[a].AreaRegion != "" {
				var model proto.RegionList
				model.Arearegion = list[a].AreaRegion
				if len(res.Regionarray) > 0 {
					regionCount := 0
					for b := 0; b < len(res.Regionarray); b++ {
						if res.Regionarray[b].Arearegion == list[a].AreaRegion {
							regionCount++
							break
						}
					}
					if regionCount == 0 {
						res.Regionarray = append(res.Regionarray, &model)
					}

				} else {
					res.Regionarray = append(res.Regionarray, &model)
				}

			}
		}

	}

	//获取省
	if params.Areadeep == 1 || params.Areadeep == 2 || params.Areadeep == 3 {
		if len(res.Regionarray) > 0 {
			for a := 0; a < len(list); a++ {
				if list[a].AreaDeep == 1 {
					var model proto.ProvinceList
					model.Areaid = int32(list[a].AreaId)
					model.Areaname = list[a].AreaName
					model.Areaparentid = int32(list[a].AreaParentId)
					model.Areasort = int32(list[a].AreaSort)
					model.Areadeep = int32(list[a].AreaDeep)
					for b := 0; b < len(res.Regionarray); b++ {
						if res.Regionarray[b].Arearegion == list[a].AreaRegion {
							res.Regionarray[b].Children = append(res.Regionarray[b].Children, &model)
						}

					}
				}
			}
		}
	}

	//获取市
	if params.Areadeep == 2 || params.Areadeep == 3 {
		if len(res.Regionarray) > 0 {
			for a := 0; a < len(list); a++ {
				if list[a].AreaDeep == 2 {
					var model proto.CityList
					model.Areaid = int32(list[a].AreaId)
					model.Areaname = list[a].AreaName
					model.Areaparentid = int32(list[a].AreaParentId)
					model.Areasort = int32(list[a].AreaSort)
					model.Areadeep = int32(list[a].AreaDeep)
					for b := 0; b < len(res.Regionarray); b++ {
						for c := 0; c < len(res.Regionarray[b].Children); c++ {
							if res.Regionarray[b].Children[c].Areaid == int32(list[a].AreaParentId) {
								res.Regionarray[b].Children[c].Children = append(res.Regionarray[b].Children[c].Children, &model)
							}
						}

					}
				}
			}
		}
	}

	//获县
	if params.Areadeep == 3 {
		if len(res.Regionarray) > 0 {
			for a := 0; a < len(list); a++ {
				if list[a].AreaDeep == 3 {
					var model proto.CountyList
					model.Areaid = int32(list[a].AreaId)
					model.Areaname = list[a].AreaName
					model.Areaparentid = int32(list[a].AreaParentId)
					model.Areasort = int32(list[a].AreaSort)
					model.Areadeep = int32(list[a].AreaDeep)
					for b := 0; b < len(res.Regionarray); b++ {
						for c := 0; c < len(res.Regionarray[b].Children); c++ {
							for d := 0; d < len(res.Regionarray[b].Children[c].Children); d++ {
								if res.Regionarray[b].Children[c].Children[d].Areaid == int32(list[a].AreaParentId) {
									res.Regionarray[b].Children[c].Children[d].Children = append(res.Regionarray[b].Children[c].Children[d].Children, &model)
								}
							}
						}

					}
				}
			}
		}
	}

	res.Code = 200
	res.Message = "ok"
	return res, nil
}

//根据大区获取大区下面直属区域
//func (s *BaseAreaService) AreaList(arearegion string) []*proto.AreaList {
//	var list []models.BaseArea
//	var lists []*proto.AreaList
//	sqlStr := "SELECT area_id,area_name,area_parent_id,area_sort,area_deep,area_region FROM   `base_area` WHERE  area_deep=1  AND   area_region='"+arearegion+"'   ORDER BY  area_sort ASC "
//	Engine.SQL(sqlStr).Find(&list);
//	if len(list) > 0 {
//		for _, v := range list {
//			var model proto.AreaList
//			model.Areaid = int32(v.AreaId)
//			model.Areaname = v.AreaName
//			//model.Areaparentid = int32(v.AreaParentId)
//			model.Areasort = int32(v.AreaSort)
//			//model.Areadeep = int32(v.AreaDeep)
//			//model.Arearegion = v.AreaRegion
//			lists = append(lists, &model)
//		}
//	}
//	return lists
//}

//根据仓库ID获取仓库配送区域
func (s *BaseAreaService) GetAreaByWarehouseId(ctx context.Context, params *proto.GetAreaByWarehouseIdRequest) (*proto.GetAreaByWarehouseIdResponse, error) {
	res := new(proto.GetAreaByWarehouseIdResponse)
	var list []dtos.WarehouseAreaDto
	sqlStr := "SELECT t2.area_name,t1.level,t1.areaid FROM  warehouse_area  AS t1 INNER JOIN `base_area` AS t2 ON t1.areaid=t2.area_id WHERE warehouseid=? ORDER BY   t1.level ASC   "
	Engine.SQL(sqlStr, params.Warehouseid).Find(&list)
	if len(list) > 0 {
		for _, v := range list {
			var model proto.WarehouseAreaDto
			model.Areaid = int32(v.Areaid)
			model.Areaname = v.AreaName
			model.Level = int32(v.Level)
			res.WarehouseArea = append(res.WarehouseArea, &model)
		}

	}
	res.Code = 200
	res.Message = "ok"
	return res, nil
}
