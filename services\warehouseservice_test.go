package services

import (
	"context"
	"dispatch-center/models"
	proto "dispatch-center/proto/dc"
	"dispatch-center/utils"
	"fmt"
	"reflect"
	"testing"

	"github.com/go-redis/redis"
	"github.com/go-xorm/xorm"
	kit "github.com/tricobbler/rp-kit"
)

func TestWarehouseService_AddWarehouse(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.AddWarehouseRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "test"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			//got, err := w.AddWarehouse(tt.args.ctx, tt.args.params)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("AddWarehouse() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("AddWarehouse() got = %v, want %v", got, tt.want)
			//}
			w.AddWarehouse(context.Background(), &proto.AddWarehouseRequest{
				Thirdid:   "test",
				Code:      "2222266",
				Name:      "testName",
				Comefrom:  2,
				Level:     5,
				Category:  1,
				Address:   "testAddress",
				Contacts:  "testContacts",
				Tel:       "testTel",
				Subsystem: 1,
				Ratio:     1,
			})
		})
	}
}

func TestWarehouseService_EditWarehouse(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.EditWarehouseRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "test"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			//got, err := w.EditWarehouse(tt.args.ctx, tt.args.params)
			//if (err != nil) != tt.wantErr {
			//	t.Errorf("EditWarehouse() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("EditWarehouse() got = %v, want %v", got, tt.want)
			//}
			w.EditWarehouse(context.Background(), &proto.EditWarehouseRequest{
				Id:        100,
				Code:      "22222",
				Name:      "testName",
				Comefrom:  2,
				Level:     5,
				Category:  1,
				Address:   "testAddress",
				Contacts:  "testContacts",
				Tel:       "testTel",
				Subsystem: 1,
				Ratio:     1,
			})

		})
	}
}

func TestWarehouseService_AddWarehouse1(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.AddWarehouseRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.AddWarehouse(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddWarehouse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddWarehouse() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_AddWarehouseArea(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.AddWarehouseAreaRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.AddWarehouseArea(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddWarehouseArea() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddWarehouseArea() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_EditWarehouse1(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.EditWarehouseRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.EditWarehouse(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("EditWarehouse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EditWarehouse() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWareHouseByAreaId(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		comefrom int
		areaId   int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := w.GetWareHouseByAreaId(tt.args.comefrom, tt.args.areaId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWareHouseByAreaId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseByArea(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.WarehouseByAreaRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.WarehouseByAreaResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseByArea(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseByArea() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseByArea() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseById(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.GetWarehouseByIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseByIdResponse
		wantErr bool
	}{
		{
			args: args{params: &proto.GetWarehouseByIdRequest{
				Id: 12,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseById(tt.args.ctx, tt.args.params)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("GetWarehouseById() got = %v, want %v", got, tt.want)
			//}
		})
	}
}

func TestWarehouseService_GetWarehouseByThirdId(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		thirdId  string
		comeFrom int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   models.Warehouse
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := w.GetWarehouseByThirdId(tt.args.thirdId, tt.args.comeFrom); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseByThirdId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseInfoByFanceCode(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetWarehouseInfoByFanceCodeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseInfoByFanceCodeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseInfoByFanceCode(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseInfoByFanceCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseInfoByFanceCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//根据财务编码获取仓库信息
func BenchmarkWarehouseService_GetWarehouseInfoByFanceCode(b *testing.B) {
	service := WarehouseService{}
	for i := 0; i < b.N; i++ {
		var p = new(proto.GetWarehouseInfoByFanceCodeRequest)
		p.FinanceCode = "1"
		service.GetWarehouseInfoByFanceCode(context.Background(), p)
	}
}

func TestWarehouseService_GetWarehouseLevel(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx   context.Context
		empty *proto.Empty
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseLevelResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseLevel(tt.args.ctx, tt.args.empty)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseModel(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		id int32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   models.Warehouse
	}{
		// TODO: Add test cases.
		{name: "測試redis"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			redisconn := utils.ConnectClusterRedis()
			//var keylist []string
			//fmt.Sprintf("*", 737)
			result, _ := redisconn.HScan("stock_hash55555:1000068001", 0, fmt.Sprintf("%d", 737), redisconn.HLen("stock_hash55555:1000068001").Val()*2).Val()
			pipe := redisconn.Pipeline()
			for i, s := range result {
				if i%2 == 1 {
					//i, _ := strconv.Atoi(s)
					pipe.HDel(s, s)
				}
			}
			//redisconn.HDel()

			s := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := s.GetWarehouseModel(tt.args.id); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseRelationList(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.WarehouseRelationListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.WarehouseRelationListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseRelationList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseRelationList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseRelationList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseType(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx   context.Context
		empty *proto.Empty
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseTypeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseType(tt.args.ctx, tt.args.empty)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseType() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_IsExistWarehouse(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		model models.Warehouse
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := s.IsExistWarehouse(tt.args.model); got != tt.want {
				t.Errorf("IsExistWarehouse() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_IsExistWarehouseExcludeId(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		model models.Warehouse
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := s.IsExistWarehouseExcludeId(tt.args.model); got != tt.want {
				t.Errorf("IsExistWarehouseExcludeId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_UpdateWarehouseStatus(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.UpdateWarehouseStatusRequest
	}

	request := proto.UpdateWarehouseStatusRequest{Id: 233, Status: "0"}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "状态修改",
			args: args{
				ctx:    nil,
				params: &request,
			},
		},

		/*	{name: "拆单",
			args: args{
				ctx:    nil,
				params: &DemoRequest,
			},
		},*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := s.UpdateWarehouseStatus(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateWarehouseStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateWarehouseStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_WarehouseList(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.WarehouseListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.WarehouseListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				params: &proto.WarehouseListRequest{
					Pageindex: 1,
					Pagesize:  10,

					KeyWord: "宠颐生合肥宠巢",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := s.WarehouseList(tt.args.ctx, tt.args.params)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestWarehouseService_WarehouseRelation(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.WarehouseRelationRequest
	}
	shoplist := []*proto.ShopInfo{}
	shoplist = append(shoplist, &proto.ShopInfo{
		ShopId:   "RP0039",
		ShopName: "",
	})
	shoplist = append(shoplist, &proto.ShopInfo{
		ShopId:   "AB0032",
		ShopName: "",
	})
	shoplist = append(shoplist, &proto.ShopInfo{
		ShopId:   "AA0150",
		ShopName: "",
	})

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BaseResponse
		wantErr bool
	}{
		{name: "门店关系",
			args: args{
				ctx: nil,
				in: &proto.WarehouseRelationRequest{
					WarehouseId:  10,
					ShopInfoList: shoplist,
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.WarehouseRelation(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("WarehouseRelation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WarehouseRelation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseInfoByCondition(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetWarehouseInfoByConditionRequest
	}
	var params = proto.GetWarehouseInfoByConditionRequest{
		Category: 0,
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseInfoByConditionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "GetWarehouseInfoByCondition", args: args{
			ctx: nil,
			in:  &params,
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseInfoByCondition(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseInfoByCondition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseInfoByCondition() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetStoreListByCategory(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetStoreListByCategoryRequest
	}
	var params = proto.GetStoreListByCategoryRequest{
		Category:  4,
		ChannelId: 10,
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetStoreListByCategoryResponse
		wantErr bool
	}{
		{name: "GetWarehouseInfoByCondition", args: args{
			ctx: nil,
			in:  &params,
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetStoreListByCategory(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStoreListByCategory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStoreListByCategory() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseInfoByFanceCodes(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetWarehouseInfoByFanceCodesRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseInfoByFanceCodesResponse
		wantErr bool
	}{
		{
			name: "根据财务编码批量获取仓库信息",
			args: args{
				ctx: context.Background(),
				in: &proto.GetWarehouseInfoByFanceCodesRequest{
					FinanceCode: []string{"CX0013", "CX0010"},
					ChannelId:   9,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{}
			got, err := w.GetWarehouseInfoByFanceCodes(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseInfoByFanceCodes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for _, v := range got.Data {
				t.Log(v)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseInfoByConditionCommon(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		FinanceCode string
		Category    int32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []models.Warehouse
	}{
		// TODO: Add test cases.
		{name: "仓库查询",
			args: args{
				FinanceCode: "RP0158",
				Category:    0,
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if got := w.GetWarehouseInfoByConditionCommon(tt.args.FinanceCode, tt.args.Category); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseInfoByConditionCommon() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_GetWarehouseByCondition(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		condition models.Warehouse
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   models.Warehouse
	}{
		// TODO: Add test cases.
		{name: "asdsa"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			var result models.Warehouse
			result.Category = 5
			result.Code = "ZLH0003"

			if got := w.GetWarehouseByCondition(result); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWarehouseByCondition() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWarehouseService_StoreWarehouseRelationShop(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.StoreWarehouseRelationShopRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.StoreWarehouseRelationShopRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &proto.StoreWarehouseRelationShopRequest{
					ChannelId: 1,
					Wrs: []*proto.WarehouseRelationShop{
						//&proto.WarehouseRelationShop{
						//	ShopId:        "CX0011",
						//	ShopName:      "宠颐生北京爱之源",
						//	WarehouseId:   223,
						//	WarehouseName: "宠颐生动物医院(爱之都店)",
						//	Category:      3,
						//	Code:          "CX0004",
						//},
						//&proto.WarehouseRelationShop{
						//	ShopId:        "CX0013",
						//	ShopName:      "宠颐生北京爱之源",
						//	WarehouseId:   223,
						//	WarehouseName: "宠颐生动物医院(爱之都店)",
						//	Category:      3,
						//	Code:          "CX0004",
						//},
						&proto.WarehouseRelationShop{
							ShopId:        "AA0034",
							ShopName:      "AMC南京长白街分院",
							WarehouseId:   1010191,
							WarehouseName: "瑞鹏深圳鼎泰风华",
							Category:      3,
							Code:          "RP0167",
						},
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.StoreWarehouseRelationShop(tt.args.ctx, tt.args.in)
			if err != nil {
				fmt.Println(err)
			} else {
				fmt.Println(got)
			}
		})
	}
}
func TestWarehouseService_GetA8WareHouseList(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.GetA8WareHouseListRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetA8WareHouseListResponse
		wantErr bool
	}{
		{name: "111",
			args: args{
				params: &proto.GetA8WareHouseListRequest{
					IsAll: 1,
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetA8WareHouseList(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetA8WareHouseList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(kit.JsonEncode(got))
		})
	}
}

func TestWarehouseService_GetShopWarehouseInfoByFinanceCode(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetShopWarehouseInfoByFinanceCodeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetShopWarehouseInfoByFinanceCodeResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &proto.GetShopWarehouseInfoByFinanceCodeRequest{
					ChannelId:   2,
					FinanceCode: "RP0005",
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetShopWarehouseInfoByFinanceCode(tt.args.ctx, tt.args.in)
			if err != nil {
				fmt.Println(err)
			} else {
				fmt.Println(got)
			}
		})
	}
}

func TestWarehouseService_ShopBindInfoListByShopId(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.ShopBindInfoByShopIdRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.ShopBindInfoByShopIdRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &proto.ShopBindInfoByShopIdRequest{
					ShopId:    []string{"SW0013", "RP0233"},
					ChannelId: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.ShopBindInfoListByShopId(tt.args.ctx, tt.args.in)
			if err != nil {
				fmt.Println(err)
			}
			fmt.Println(got.Code, got.Message)
			for _, v := range got.Info {
				fmt.Println(v)
			}
		})
	}
}

func TestWarehouseService_InitBindShopData(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.InitShopDataRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.InitShopDataRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				params: &proto.InitShopDataRequest{
					Step: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.InitBindShopData(tt.args.ctx, tt.args.params)
			if err != nil {
				fmt.Println(err)
			} else {
				fmt.Println(got)
			}
		})
	}
}

func TestWarehouseService_BindShops(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.BindShopsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.BindShopsRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &proto.BindShopsRequest{
					ChannelId: 0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.BindShops(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestWarehouseService_GetWarehouseInfoByFanceCode1(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.GetWarehouseInfoByFanceCodeRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.GetWarehouseInfoByFanceCodeResponse
		wantErr bool
	}{
		{name: "获取仓库",
			args: args{
				ctx: context.Background(),
				in: &proto.GetWarehouseInfoByFanceCodeRequest{
					FinanceCode: "RP0248",
					WarehouseId: 0,
					ChannelId:   9,
				},
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.GetWarehouseInfoByFanceCode(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWarehouseInfoByFanceCode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestWarehouseService_ShopBindInfoList(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.ShopBindInfoRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.ShopBindInfoRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				in: &proto.ShopBindInfoRequest{
					ChannelId: 1,
					BindType:  3,
					PageIndex: 1,
					PageSize:  10,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.ShopBindInfoList(tt.args.ctx, tt.args.in)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestWarehouseService_RemoveShop(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.RemoveShopRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.RemoveShopRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				params: &proto.RemoveShopRequest{
					ChannelId: 4,
					ShopId:    "ZLH0001",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.RemoveShop(tt.args.ctx, tt.args.params)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestWarehouseService_InitBindMTShopData(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx    context.Context
		params *proto.InitShopDataRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.InitShopDataRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: context.Background(),
				params: &proto.InitShopDataRequest{
					Step: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.InitBindMTShopData(tt.args.ctx, tt.args.params)
			fmt.Println(got)
			fmt.Println(err)
		})
	}
}

func TestWarehouseService_WarehouseRelationShopList(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		ctx context.Context
		in  *proto.ShopBindWarehouseReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *proto.ShopBindInfoRespond
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "WarehouseRelationShopListTest",
			args: args{
				ctx: context.Background(),
				in: &proto.ShopBindWarehouseReq{
					ChannelId:       0,
					BindType:        0,
					WarehouseSearch: "",
					ShopSearch:      "CX0042",
					PageSize:        10,
					PageIndex:       1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			got, err := w.WarehouseRelationShopList(tt.args.ctx, tt.args.in)
			t.Log(kit.JsonEncode(got))
			if (err != nil) != tt.wantErr {
				t.Errorf("WarehouseRelationShopList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestWarehouseService_bindRelationShop(t *testing.T) {
	type fields struct {
		DBService       DBService
		BaseAreaService BaseAreaService
	}
	type args struct {
		redisClient *redis.Client
		dbConn      *xorm.Engine
		channelId   int32
		data        models.WarehouseRelationShopRedis
	}
	var client = utils.ConnectClusterRedis()
	defer client.Close()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				data: models.WarehouseRelationShopRedis{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WarehouseService{
				DBService:       tt.fields.DBService,
				BaseAreaService: tt.fields.BaseAreaService,
			}
			if err := w.bindRelationShop(tt.args.redisClient, tt.args.dbConn, tt.args.channelId, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("bindRelationShop() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
