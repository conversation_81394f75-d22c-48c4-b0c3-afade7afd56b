// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mk/service.proto

package mk

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("mk/service.proto", fileDescriptor_6eb994e6a97c9f62) }

var fileDescriptor_6eb994e6a97c9f62 = []byte{
	// 1407 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x98, 0xe9, 0x6e, 0x1c, 0x45,
	0x10, 0xc7, 0x45, 0x22, 0x42, 0x68, 0xcb, 0xb1, 0xdd, 0x3e, 0xb3, 0x71, 0x7c, 0x06, 0x3e, 0x70,
	0xd8, 0xc2, 0xdc, 0x0a, 0x04, 0xec, 0x5d, 0x63, 0x16, 0xc5, 0x8e, 0xbd, 0x93, 0xf0, 0x01, 0x10,
	0x61, 0x32, 0x53, 0xb6, 0x47, 0x73, 0xf4, 0xee, 0x74, 0x8f, 0xcd, 0x22, 0x21, 0xf1, 0x0c, 0x3c,
	0x04, 0x4f, 0xc0, 0xc3, 0xc0, 0x9b, 0xf0, 0x11, 0xf5, 0x35, 0xdb, 0x3d, 0xd7, 0x2e, 0x48, 0xf9,
	0x66, 0x57, 0xfd, 0xeb, 0x57, 0xd5, 0x57, 0x75, 0xcf, 0xa2, 0xd9, 0x38, 0xdc, 0xa5, 0x90, 0x5e,
	0x05, 0x1e, 0xec, 0xf4, 0x53, 0xc2, 0x08, 0xbe, 0x11, 0x87, 0x2d, 0x1c, 0x87, 0xbb, 0xfd, 0x94,
	0xc4, 0x84, 0x05, 0x24, 0x91, 0xf6, 0xd6, 0x6a, 0x1c, 0xee, 0xa6, 0xe0, 0x7a, 0x97, 0x3d, 0xf0,
	0x33, 0x0f, 0x9e, 0xc7, 0x40, 0xa9, 0x7b, 0xa1, 0xa2, 0x5a, 0xf7, 0xe3, 0x70, 0x97, 0x05, 0x31,
	0x74, 0x02, 0xea, 0x91, 0x2c, 0x61, 0x05, 0xf7, 0xba, 0x08, 0xe6, 0x71, 0x1d, 0x88, 0x82, 0x2b,
	0x48, 0x87, 0x05, 0xc1, 0x86, 0x99, 0xf1, 0xb9, 0x47, 0x92, 0xf3, 0xe0, 0x22, 0x4b, 0x5d, 0x23,
	0xff, 0x9a, 0xa9, 0x60, 0x2e, 0x0d, 0x0b, 0x84, 0x99, 0x38, 0xdc, 0x8d, 0xdd, 0x34, 0x04, 0x26,
	0x0d, 0x7b, 0x7f, 0xdf, 0x46, 0xb3, 0xa7, 0x3a, 0xc0, 0x91, 0x63, 0xc4, 0x0f, 0xd1, 0x6b, 0x4e,
	0x16, 0xc7, 0x6e, 0x3a, 0xc4, 0xcb, 0x3b, 0x71, 0xb8, 0x93, 0x13, 0x95, 0xb5, 0x07, 0x83, 0x56,
	0x8d, 0x83, 0xe2, 0x63, 0xb4, 0x72, 0x96, 0x41, 0x3a, 0xcc, 0xa9, 0x1d, 0x60, 0x6e, 0x10, 0x1d,
	0x0c, 0xbb, 0x3e, 0x5e, 0xe0, 0x41, 0x03, 0xee, 0xe5, 0xff, 0xf6, 0x60, 0x90, 0x01, 0x65, 0xad,
	0x7b, 0x16, 0x4a, 0xca, 0x7b, 0x40, 0xfb, 0x24, 0xa1, 0x80, 0xbf, 0x47, 0x4b, 0x36, 0xee, 0x60,
	0xe8, 0x5c, 0x92, 0x7e, 0xd7, 0xc7, 0xf7, 0x79, 0x58, 0xb5, 0x8f, 0x17, 0xb8, 0x65, 0x51, 0xcf,
	0x64, 0x52, 0xed, 0x56, 0xf0, 0xe7, 0x15, 0xf0, 0x30, 0xeb, 0xfa, 0x14, 0x6f, 0x56, 0x46, 0x0b,
	0x9f, 0x2e, 0x7b, 0xab, 0x49, 0xa2, 0x12, 0x74, 0x11, 0x6e, 0xbb, 0x91, 0x97, 0xf3, 0x8f, 0x49,
	0x02, 0x43, 0xbc, 0x62, 0x45, 0x72, 0x81, 0x66, 0xde, 0xad, 0xf0, 0x28, 0x94, 0x87, 0x5a, 0x76,
	0xad, 0x4e, 0x98, 0xc9, 0x01, 0x9d, 0xc0, 0x75, 0x4d, 0xbd, 0x7a, 0x32, 0x26, 0xae, 0xf7, 0x08,
	0xdd, 0x69, 0xa7, 0xe0, 0x32, 0x38, 0x8d, 0xdc, 0xe1, 0x41, 0x10, 0x45, 0x58, 0x54, 0x64, 0xdb,
	0x34, 0xb0, 0x55, 0xe5, 0x52, 0xa0, 0x6f, 0xd0, 0xcc, 0x11, 0x30, 0x6d, 0x7e, 0x1c, 0x50, 0x86,
	0x85, 0xbc, 0x60, 0xb4, 0xb6, 0x40, 0xc9, 0xa7, 0x58, 0x3f, 0xa0, 0xc5, 0x23, 0x60, 0x4e, 0x90,
	0x5c, 0x44, 0x60, 0x11, 0x37, 0x54, 0x54, 0xd9, 0xa5, 0xb9, 0x9b, 0x0d, 0x8a, 0x7c, 0x89, 0x16,
	0x3a, 0x10, 0x01, 0x03, 0x5b, 0x83, 0xd7, 0x79, 0x68, 0x95, 0x47, 0xb3, 0x67, 0xb9, 0xe0, 0x85,
	0x4b, 0x21, 0x47, 0x1d, 0xa3, 0xd9, 0x0e, 0xb9, 0x4e, 0x22, 0xe2, 0xfa, 0x39, 0x46, 0x8c, 0xac,
	0x68, 0xd5, 0x88, 0xd5, 0x6a, 0xa7, 0xc2, 0x85, 0xa8, 0xc5, 0xa7, 0x44, 0xaf, 0xd8, 0x93, 0x3e,
	0xa4, 0x2e, 0x83, 0xc7, 0xe4, 0x42, 0x0c, 0xfe, 0x0d, 0x3d, 0x65, 0xd5, 0x7e, 0x9d, 0xe2, 0xcd,
	0x71, 0x32, 0x95, 0xec, 0x0c, 0xad, 0x38, 0x8c, 0xa4, 0x50, 0xa1, 0xc3, 0xdb, 0x9c, 0x51, 0xe7,
	0xad, 0x9d, 0x8e, 0xbd, 0xbf, 0x5e, 0x45, 0xb8, 0x37, 0x6a, 0x86, 0xba, 0xbb, 0xec, 0xa1, 0x9b,
	0xfb, 0xbe, 0x2f, 0x37, 0x96, 0xd1, 0x2b, 0xf7, 0x7d, 0xbf, 0x7e, 0x66, 0x3f, 0x41, 0xb7, 0x9e,
	0xf5, 0x7d, 0x97, 0x01, 0x5e, 0x2d, 0x84, 0x49, 0x73, 0x7d, 0xe4, 0xe7, 0xe8, 0x8e, 0x94, 0x74,
	0xe9, 0x61, 0xe2, 0xbe, 0x88, 0x40, 0x26, 0xce, 0x2c, 0x5b, 0x7d, 0xf8, 0xd7, 0x68, 0x59, 0xee,
	0x81, 0xd1, 0xb1, 0xbb, 0x24, 0x7d, 0xd1, 0xcc, 0xc4, 0xca, 0xfa, 0x86, 0x73, 0xe4, 0xa3, 0x15,
	0xa4, 0x33, 0xd4, 0xaa, 0x24, 0x89, 0x83, 0x28, 0xcf, 0xaf, 0x31, 0x2c, 0x4b, 0x24, 0x24, 0x15,
	0xc8, 0x73, 0x74, 0xaf, 0xd0, 0x12, 0x04, 0x51, 0x1e, 0x7b, 0x3a, 0x09, 0xf3, 0x41, 0x93, 0x24,
	0xcf, 0xf3, 0x08, 0xbd, 0x7e, 0xa6, 0x9b, 0x76, 0x53, 0x0f, 0x37, 0x40, 0xd2, 0xa7, 0xe2, 0x7f,
	0x42, 0x8b, 0x76, 0x9d, 0xa7, 0x29, 0xf1, 0x33, 0x8f, 0xe1, 0xad, 0x72, 0x7a, 0x6e, 0x17, 0x62,
	0x4d, 0xde, 0x6e, 0xd4, 0xa8, 0x0c, 0x09, 0xda, 0x16, 0x86, 0x27, 0x7d, 0x8e, 0x77, 0x23, 0x07,
	0x22, 0xf0, 0xd8, 0x4b, 0xcb, 0xb7, 0xf7, 0xfb, 0x6d, 0x34, 0xff, 0xd4, 0xb8, 0xc9, 0xf5, 0xde,
	0xfe, 0x4c, 0xee, 0xed, 0x6d, 0xab, 0xd5, 0x9a, 0xc2, 0xc6, 0x5d, 0xfe, 0x69, 0xbe, 0xcb, 0xc5,
	0xdd, 0x66, 0x3e, 0x15, 0xc6, 0x6d, 0xf3, 0x2f, 0xd1, 0xdd, 0xc2, 0xee, 0x92, 0x75, 0x8b, 0x25,
	0x9b, 0x1b, 0xed, 0xd4, 0x7a, 0xc2, 0x29, 0x5a, 0xad, 0x21, 0xc8, 0x1d, 0xba, 0x56, 0x2c, 0x49,
	0x4d, 0x4c, 0x3d, 0xf1, 0xc7, 0xe2, 0xf6, 0xfc, 0x6f, 0xc0, 0xf5, 0x5a, 0xbf, 0xe2, 0x7f, 0x31,
	0x7e, 0x5b, 0xae, 0x16, 0x19, 0xd6, 0xbe, 0x3c, 0x55, 0xfb, 0xf2, 0x71, 0x10, 0x07, 0xac, 0x2d,
	0xdd, 0x4e, 0x98, 0x51, 0x79, 0x55, 0x0d, 0x6c, 0x97, 0xb5, 0xd3, 0x4b, 0x3e, 0x45, 0xfc, 0x05,
	0xad, 0x9b, 0xbd, 0xb6, 0x6d, 0x3e, 0xd1, 0xe4, 0xc3, 0x06, 0xbf, 0x55, 0x6c, 0xc8, 0x15, 0x22,
	0x9d, 0xeb, 0xed, 0x89, 0xb4, 0x2a, 0xf7, 0xaf, 0x68, 0xa3, 0x5a, 0x27, 0xaf, 0xe8, 0x6e, 0x72,
	0x4e, 0xb0, 0x00, 0x8e, 0x53, 0xe9, 0xec, 0xef, 0x4c, 0x26, 0x1e, 0x97, 0x5e, 0xb5, 0xdf, 0x31,
	0xe9, 0x47, 0xaa, 0x09, 0xd2, 0x9b, 0x62, 0x95, 0xfe, 0xb7, 0x57, 0xd0, 0x66, 0xb5, 0xb8, 0x07,
	0xfd, 0xc8, 0xf5, 0x64, 0x01, 0x0d, 0x4c, 0x43, 0xa6, 0x2b, 0x78, 0x77, 0x42, 0xb5, 0x6a, 0x0a,
	0xff, 0xdc, 0x44, 0x8b, 0x3d, 0xeb, 0xfd, 0xae, 0xdb, 0xc2, 0x87, 0xb2, 0x2d, 0xa8, 0xbb, 0xcb,
	0x54, 0x34, 0xf6, 0x83, 0x87, 0x79, 0x3f, 0x58, 0x2f, 0x47, 0xbe, 0xe4, 0x8b, 0xef, 0x51, 0xfd,
	0xc5, 0x37, 0x51, 0x3b, 0x39, 0x6e, 0xbc, 0xee, 0xee, 0x97, 0xc7, 0x23, 0x6f, 0x9c, 0x3a, 0xdc,
	0x77, 0xe8, 0x6e, 0xd5, 0x55, 0x37, 0x11, 0x6d, 0xad, 0xce, 0xad, 0xd8, 0xfb, 0xe3, 0xfb, 0x48,
	0x05, 0xc2, 0xec, 0x24, 0x7b, 0x7f, 0xdc, 0x40, 0x0b, 0x79, 0x69, 0x4f, 0x5d, 0x1a, 0xea, 0x95,
	0x77, 0x10, 0xb6, 0xeb, 0xe6, 0x4e, 0x59, 0x70, 0xdf, 0x34, 0x59, 0x57, 0xd0, 0x5a, 0x9d, 0x5b,
	0x15, 0xdc, 0x2b, 0x7e, 0xb6, 0x70, 0x49, 0x43, 0xf5, 0x5b, 0x25, 0x1e, 0xf7, 0xda, 0x4c, 0x28,
	0x7e, 0xb6, 0x71, 0x99, 0x6a, 0x59, 0xdb, 0xa5, 0x78, 0xe9, 0xb0, 0x8a, 0x7e, 0xd0, 0x2c, 0x52,
	0x13, 0xf5, 0xe7, 0x34, 0x9a, 0x3e, 0x16, 0x1f, 0xa0, 0x7a, 0x86, 0xda, 0x68, 0x56, 0x48, 0xf6,
	0x3d, 0x16, 0x5c, 0x05, 0x8c, 0xbf, 0x9e, 0xf0, 0x12, 0x67, 0xb9, 0x23, 0x83, 0xce, 0xb1, 0x5c,
	0xb2, 0xab, 0xea, 0x77, 0xd0, 0x54, 0x07, 0x22, 0x8d, 0xc0, 0xd3, 0x5c, 0xd7, 0x6d, 0x38, 0x59,
	0x47, 0x68, 0xde, 0x61, 0x2e, 0xcb, 0xe8, 0x57, 0x29, 0xd0, 0xcb, 0x3c, 0x4e, 0x4c, 0x3c, 0x2d,
	0x3b, 0xea, 0x41, 0x1f, 0xa3, 0xa9, 0x13, 0xb8, 0xce, 0x01, 0xa2, 0xf0, 0x64, 0x64, 0xa8, 0x0f,
	0x7c, 0x8a, 0x16, 0xc4, 0xb0, 0x1d, 0x60, 0x2c, 0x48, 0x2e, 0x72, 0xc2, 0x7a, 0xbe, 0x82, 0x05,
	0x8f, 0x75, 0xff, 0xd0, 0xa2, 0x4f, 0x51, 0x0f, 0x10, 0x3e, 0x81, 0xeb, 0x22, 0xb3, 0x55, 0x19,
	0x52, 0xff, 0xd6, 0x9e, 0x72, 0x32, 0xcf, 0x23, 0x59, 0xfa, 0x8c, 0x42, 0x8a, 0xe7, 0x45, 0xd7,
	0xa0, 0x90, 0x9a, 0x5f, 0x15, 0x0b, 0xb6, 0x51, 0x45, 0x7e, 0x80, 0x66, 0x54, 0xe4, 0xa1, 0x1f,
	0x30, 0x11, 0x3d, 0xa3, 0x85, 0x56, 0x3e, 0x69, 0x50, 0x51, 0x1f, 0xa1, 0x69, 0x15, 0x75, 0x96,
	0x7a, 0xc4, 0x07, 0xd9, 0x5f, 0x06, 0xe2, 0x6f, 0x1d, 0x85, 0x4d, 0x53, 0x7e, 0xfd, 0xcf, 0x5a,
	0x71, 0xbc, 0xc3, 0x2e, 0x8c, 0x74, 0x46, 0x67, 0x5d, 0x2c, 0x58, 0x6b, 0x00, 0x0e, 0x30, 0x13,
	0xe0, 0x00, 0xab, 0x00, 0x08, 0x6b, 0x09, 0xe0, 0xb8, 0xd1, 0x95, 0xb8, 0x17, 0x24, 0x80, 0xea,
	0x7f, 0x2d, 0x80, 0x61, 0xcd, 0xbf, 0x2e, 0xe6, 0x8b, 0x00, 0x3e, 0x8a, 0x65, 0x4b, 0x6d, 0x0c,
	0x64, 0xa5, 0xec, 0xa8, 0x27, 0xf1, 0xe1, 0xd8, 0x24, 0x63, 0x44, 0x2b, 0x65, 0xc7, 0xa8, 0xd3,
	0x16, 0x49, 0x3d, 0xf0, 0x20, 0xb8, 0x0a, 0x92, 0x0b, 0xd9, 0xb8, 0x68, 0xc9, 0x6e, 0x35, 0xae,
	0x2a, 0x77, 0xfe, 0x1b, 0xc6, 0x6a, 0x2d, 0x9b, 0x0f, 0x7c, 0xa3, 0x3a, 0xde, 0x98, 0x81, 0xcd,
	0x06, 0x45, 0x69, 0x55, 0x3a, 0x24, 0x71, 0xd9, 0x68, 0x59, 0x7d, 0xfd, 0xaf, 0xb5, 0x2a, 0x86,
	0x55, 0x01, 0x0e, 0x11, 0xb6, 0xf6, 0x85, 0x78, 0xe7, 0xc9, 0xa3, 0x3d, 0x18, 0x19, 0xac, 0x9e,
	0x64, 0xd9, 0xf3, 0x0f, 0xbe, 0xa5, 0x32, 0x86, 0x1f, 0x0c, 0xf5, 0xbc, 0xb4, 0x8d, 0xf6, 0xf3,
	0xb2, 0xe8, 0x53, 0xc8, 0x36, 0x9a, 0x6b, 0x93, 0xac, 0x4f, 0x12, 0x7d, 0x8a, 0x4f, 0xe0, 0x5a,
	0xbe, 0x2a, 0x4a, 0xe6, 0xfa, 0xf3, 0xdd, 0x46, 0xd8, 0x56, 0x8b, 0x9f, 0x13, 0xc4, 0x5c, 0x78,
	0xc2, 0x6e, 0x1e, 0xf4, 0xa5, 0xa2, 0x39, 0x3f, 0xea, 0x73, 0x87, 0x89, 0x6f, 0x73, 0xc6, 0xb7,
	0xdd, 0x47, 0x68, 0xea, 0x5b, 0x92, 0x79, 0x97, 0xb2, 0x6f, 0xc8, 0x29, 0x35, 0x0c, 0xd6, 0x94,
	0x5a, 0x76, 0x15, 0xdf, 0x41, 0x73, 0x86, 0xf9, 0xf0, 0xe7, 0x3e, 0x49, 0xff, 0x07, 0xe5, 0x3d,
	0x74, 0x67, 0xdf, 0x57, 0xb5, 0x8b, 0xe7, 0xfb, 0xd8, 0xc2, 0x5f, 0xdc, 0x12, 0xbf, 0x96, 0xbe,
	0xff, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x90, 0x6c, 0xff, 0x9b, 0x0a, 0x16, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PromotionServiceClient is the client API for PromotionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PromotionServiceClient interface {
	// 活动概览
	Summary(ctx context.Context, in *PromotionSummaryReq, opts ...grpc.CallOption) (*PromotionSummaryRes, error)
	// 查询活动详情
	QueryPromotionDetailById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PromotionDetailResponse, error)
	// 根据店铺Id查询活动详情
	QueryPromotionByShopId(ctx context.Context, in *QueryPromotionByShopIdReq, opts ...grpc.CallOption) (*PromotionQueryByShopIdResponse, error)
	// 通过skuids 查询促销活动信息
	QueryPromotionBySkuIds(ctx context.Context, in *PromotionQueryBySkuIdsRequest, opts ...grpc.CallOption) (*PromotionQueryBySkuIdsResponse, error)
	// 计算减免金额
	CalcPromotionMoney(ctx context.Context, in *PromotionCalcRequest, opts ...grpc.CallOption) (*PromotionCalcResponse, error)
	// 通过shopId 查询符合促销活动的商品skuid  -新
	QueryPromotionSkuByShopNew(ctx context.Context, in *PromotionQueryByShopIdRequest, opts ...grpc.CallOption) (*PromotionQueryBySkuIdsResponse, error)
	//海报管理-创建海报
	CreatePlayBill(ctx context.Context, in *CreatePlayBillRequest, opts ...grpc.CallOption) (*CreatePlayBillResponse, error)
	//海报管理-多门店列表
	GetPlayBillList(ctx context.Context, in *GetPlayBillListRequest, opts ...grpc.CallOption) (*GetPlayBillListResponse, error)
	//海报管理-单门店门店列表
	GetSinglePlayBillList(ctx context.Context, in *GetSinglePlayBillListRequest, opts ...grpc.CallOption) (*GetSinglePlayBillListResponse, error)
	//海报管理-单门店门海报删除
	DeleteSinglePlayBill(ctx context.Context, in *DeleteSinglePlayBillRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//海报管理-下载海报
	DownloadPlayBill(ctx context.Context, in *DownloadPlayBillRequest, opts ...grpc.CallOption) (*DownloadPlayBillResponse, error)
	//活动操作日志列表
	GetPromotionOperateLogList(ctx context.Context, in *GetPromotionOperateLogListRequest, opts ...grpc.CallOption) (*GetPromotionOperateLogListResponse, error)
	//活动操作日志保存
	StorePromotionOperateLog(ctx context.Context, in *StorePromotionOperateLogRequest, opts ...grpc.CallOption) (*BaseResponse, error)
}

type promotionServiceClient struct {
	cc *grpc.ClientConn
}

func NewPromotionServiceClient(cc *grpc.ClientConn) PromotionServiceClient {
	return &promotionServiceClient{cc}
}

func (c *promotionServiceClient) Summary(ctx context.Context, in *PromotionSummaryReq, opts ...grpc.CallOption) (*PromotionSummaryRes, error) {
	out := new(PromotionSummaryRes)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/Summary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) QueryPromotionDetailById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PromotionDetailResponse, error) {
	out := new(PromotionDetailResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/QueryPromotionDetailById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) QueryPromotionByShopId(ctx context.Context, in *QueryPromotionByShopIdReq, opts ...grpc.CallOption) (*PromotionQueryByShopIdResponse, error) {
	out := new(PromotionQueryByShopIdResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/QueryPromotionByShopId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) QueryPromotionBySkuIds(ctx context.Context, in *PromotionQueryBySkuIdsRequest, opts ...grpc.CallOption) (*PromotionQueryBySkuIdsResponse, error) {
	out := new(PromotionQueryBySkuIdsResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/QueryPromotionBySkuIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) CalcPromotionMoney(ctx context.Context, in *PromotionCalcRequest, opts ...grpc.CallOption) (*PromotionCalcResponse, error) {
	out := new(PromotionCalcResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/CalcPromotionMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) QueryPromotionSkuByShopNew(ctx context.Context, in *PromotionQueryByShopIdRequest, opts ...grpc.CallOption) (*PromotionQueryBySkuIdsResponse, error) {
	out := new(PromotionQueryBySkuIdsResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/QueryPromotionSkuByShopNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) CreatePlayBill(ctx context.Context, in *CreatePlayBillRequest, opts ...grpc.CallOption) (*CreatePlayBillResponse, error) {
	out := new(CreatePlayBillResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/CreatePlayBill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) GetPlayBillList(ctx context.Context, in *GetPlayBillListRequest, opts ...grpc.CallOption) (*GetPlayBillListResponse, error) {
	out := new(GetPlayBillListResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/GetPlayBillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) GetSinglePlayBillList(ctx context.Context, in *GetSinglePlayBillListRequest, opts ...grpc.CallOption) (*GetSinglePlayBillListResponse, error) {
	out := new(GetSinglePlayBillListResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/GetSinglePlayBillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) DeleteSinglePlayBill(ctx context.Context, in *DeleteSinglePlayBillRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/DeleteSinglePlayBill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) DownloadPlayBill(ctx context.Context, in *DownloadPlayBillRequest, opts ...grpc.CallOption) (*DownloadPlayBillResponse, error) {
	out := new(DownloadPlayBillResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/DownloadPlayBill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) GetPromotionOperateLogList(ctx context.Context, in *GetPromotionOperateLogListRequest, opts ...grpc.CallOption) (*GetPromotionOperateLogListResponse, error) {
	out := new(GetPromotionOperateLogListResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/GetPromotionOperateLogList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionServiceClient) StorePromotionOperateLog(ctx context.Context, in *StorePromotionOperateLogRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionService/StorePromotionOperateLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionServiceServer is the server API for PromotionService service.
type PromotionServiceServer interface {
	// 活动概览
	Summary(context.Context, *PromotionSummaryReq) (*PromotionSummaryRes, error)
	// 查询活动详情
	QueryPromotionDetailById(context.Context, *QueryByIdRequest) (*PromotionDetailResponse, error)
	// 根据店铺Id查询活动详情
	QueryPromotionByShopId(context.Context, *QueryPromotionByShopIdReq) (*PromotionQueryByShopIdResponse, error)
	// 通过skuids 查询促销活动信息
	QueryPromotionBySkuIds(context.Context, *PromotionQueryBySkuIdsRequest) (*PromotionQueryBySkuIdsResponse, error)
	// 计算减免金额
	CalcPromotionMoney(context.Context, *PromotionCalcRequest) (*PromotionCalcResponse, error)
	// 通过shopId 查询符合促销活动的商品skuid  -新
	QueryPromotionSkuByShopNew(context.Context, *PromotionQueryByShopIdRequest) (*PromotionQueryBySkuIdsResponse, error)
	//海报管理-创建海报
	CreatePlayBill(context.Context, *CreatePlayBillRequest) (*CreatePlayBillResponse, error)
	//海报管理-多门店列表
	GetPlayBillList(context.Context, *GetPlayBillListRequest) (*GetPlayBillListResponse, error)
	//海报管理-单门店门店列表
	GetSinglePlayBillList(context.Context, *GetSinglePlayBillListRequest) (*GetSinglePlayBillListResponse, error)
	//海报管理-单门店门海报删除
	DeleteSinglePlayBill(context.Context, *DeleteSinglePlayBillRequest) (*BaseResponse, error)
	//海报管理-下载海报
	DownloadPlayBill(context.Context, *DownloadPlayBillRequest) (*DownloadPlayBillResponse, error)
	//活动操作日志列表
	GetPromotionOperateLogList(context.Context, *GetPromotionOperateLogListRequest) (*GetPromotionOperateLogListResponse, error)
	//活动操作日志保存
	StorePromotionOperateLog(context.Context, *StorePromotionOperateLogRequest) (*BaseResponse, error)
}

// UnimplementedPromotionServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPromotionServiceServer struct {
}

func (*UnimplementedPromotionServiceServer) Summary(ctx context.Context, req *PromotionSummaryReq) (*PromotionSummaryRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Summary not implemented")
}
func (*UnimplementedPromotionServiceServer) QueryPromotionDetailById(ctx context.Context, req *QueryByIdRequest) (*PromotionDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionDetailById not implemented")
}
func (*UnimplementedPromotionServiceServer) QueryPromotionByShopId(ctx context.Context, req *QueryPromotionByShopIdReq) (*PromotionQueryByShopIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionByShopId not implemented")
}
func (*UnimplementedPromotionServiceServer) QueryPromotionBySkuIds(ctx context.Context, req *PromotionQueryBySkuIdsRequest) (*PromotionQueryBySkuIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionBySkuIds not implemented")
}
func (*UnimplementedPromotionServiceServer) CalcPromotionMoney(ctx context.Context, req *PromotionCalcRequest) (*PromotionCalcResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalcPromotionMoney not implemented")
}
func (*UnimplementedPromotionServiceServer) QueryPromotionSkuByShopNew(ctx context.Context, req *PromotionQueryByShopIdRequest) (*PromotionQueryBySkuIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionSkuByShopNew not implemented")
}
func (*UnimplementedPromotionServiceServer) CreatePlayBill(ctx context.Context, req *CreatePlayBillRequest) (*CreatePlayBillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlayBill not implemented")
}
func (*UnimplementedPromotionServiceServer) GetPlayBillList(ctx context.Context, req *GetPlayBillListRequest) (*GetPlayBillListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayBillList not implemented")
}
func (*UnimplementedPromotionServiceServer) GetSinglePlayBillList(ctx context.Context, req *GetSinglePlayBillListRequest) (*GetSinglePlayBillListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSinglePlayBillList not implemented")
}
func (*UnimplementedPromotionServiceServer) DeleteSinglePlayBill(ctx context.Context, req *DeleteSinglePlayBillRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSinglePlayBill not implemented")
}
func (*UnimplementedPromotionServiceServer) DownloadPlayBill(ctx context.Context, req *DownloadPlayBillRequest) (*DownloadPlayBillResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadPlayBill not implemented")
}
func (*UnimplementedPromotionServiceServer) GetPromotionOperateLogList(ctx context.Context, req *GetPromotionOperateLogListRequest) (*GetPromotionOperateLogListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionOperateLogList not implemented")
}
func (*UnimplementedPromotionServiceServer) StorePromotionOperateLog(ctx context.Context, req *StorePromotionOperateLogRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StorePromotionOperateLog not implemented")
}

func RegisterPromotionServiceServer(s *grpc.Server, srv PromotionServiceServer) {
	s.RegisterService(&_PromotionService_serviceDesc, srv)
}

func _PromotionService_Summary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).Summary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/Summary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).Summary(ctx, req.(*PromotionSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_QueryPromotionDetailById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).QueryPromotionDetailById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/QueryPromotionDetailById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).QueryPromotionDetailById(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_QueryPromotionByShopId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPromotionByShopIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).QueryPromotionByShopId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/QueryPromotionByShopId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).QueryPromotionByShopId(ctx, req.(*QueryPromotionByShopIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_QueryPromotionBySkuIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionQueryBySkuIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).QueryPromotionBySkuIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/QueryPromotionBySkuIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).QueryPromotionBySkuIds(ctx, req.(*PromotionQueryBySkuIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_CalcPromotionMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionCalcRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).CalcPromotionMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/CalcPromotionMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).CalcPromotionMoney(ctx, req.(*PromotionCalcRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_QueryPromotionSkuByShopNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionQueryByShopIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).QueryPromotionSkuByShopNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/QueryPromotionSkuByShopNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).QueryPromotionSkuByShopNew(ctx, req.(*PromotionQueryByShopIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_CreatePlayBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlayBillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).CreatePlayBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/CreatePlayBill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).CreatePlayBill(ctx, req.(*CreatePlayBillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_GetPlayBillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayBillListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).GetPlayBillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/GetPlayBillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).GetPlayBillList(ctx, req.(*GetPlayBillListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_GetSinglePlayBillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSinglePlayBillListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).GetSinglePlayBillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/GetSinglePlayBillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).GetSinglePlayBillList(ctx, req.(*GetSinglePlayBillListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_DeleteSinglePlayBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSinglePlayBillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).DeleteSinglePlayBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/DeleteSinglePlayBill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).DeleteSinglePlayBill(ctx, req.(*DeleteSinglePlayBillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_DownloadPlayBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadPlayBillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).DownloadPlayBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/DownloadPlayBill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).DownloadPlayBill(ctx, req.(*DownloadPlayBillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_GetPromotionOperateLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPromotionOperateLogListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).GetPromotionOperateLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/GetPromotionOperateLogList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).GetPromotionOperateLogList(ctx, req.(*GetPromotionOperateLogListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionService_StorePromotionOperateLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StorePromotionOperateLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServiceServer).StorePromotionOperateLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionService/StorePromotionOperateLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServiceServer).StorePromotionOperateLog(ctx, req.(*StorePromotionOperateLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PromotionService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.PromotionService",
	HandlerType: (*PromotionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Summary",
			Handler:    _PromotionService_Summary_Handler,
		},
		{
			MethodName: "QueryPromotionDetailById",
			Handler:    _PromotionService_QueryPromotionDetailById_Handler,
		},
		{
			MethodName: "QueryPromotionByShopId",
			Handler:    _PromotionService_QueryPromotionByShopId_Handler,
		},
		{
			MethodName: "QueryPromotionBySkuIds",
			Handler:    _PromotionService_QueryPromotionBySkuIds_Handler,
		},
		{
			MethodName: "CalcPromotionMoney",
			Handler:    _PromotionService_CalcPromotionMoney_Handler,
		},
		{
			MethodName: "QueryPromotionSkuByShopNew",
			Handler:    _PromotionService_QueryPromotionSkuByShopNew_Handler,
		},
		{
			MethodName: "CreatePlayBill",
			Handler:    _PromotionService_CreatePlayBill_Handler,
		},
		{
			MethodName: "GetPlayBillList",
			Handler:    _PromotionService_GetPlayBillList_Handler,
		},
		{
			MethodName: "GetSinglePlayBillList",
			Handler:    _PromotionService_GetSinglePlayBillList_Handler,
		},
		{
			MethodName: "DeleteSinglePlayBill",
			Handler:    _PromotionService_DeleteSinglePlayBill_Handler,
		},
		{
			MethodName: "DownloadPlayBill",
			Handler:    _PromotionService_DownloadPlayBill_Handler,
		},
		{
			MethodName: "GetPromotionOperateLogList",
			Handler:    _PromotionService_GetPromotionOperateLogList_Handler,
		},
		{
			MethodName: "StorePromotionOperateLog",
			Handler:    _PromotionService_StorePromotionOperateLog_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}

// ReachReduceServiceClient is the client API for ReachReduceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ReachReduceServiceClient interface {
	// 添加
	Add(ctx context.Context, in *ReachReduceAddRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新
	Update(ctx context.Context, in *ReachReduceUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 更新是否启用
	UpdateIsEnable(ctx context.Context, in *UpdateIsEnableRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除多个店铺
	DeletePromotionShopById(ctx context.Context, in *DeletePromotonShopByIds, opts ...grpc.CallOption) (*BaseResponse, error)
	// 根据查询条件删除活动与店铺关联关系
	DeletePromotionShopByQuery(ctx context.Context, in *ReachReducePromotionShopQuery, opts ...grpc.CallOption) (*BaseResponse, error)
	// 根据查询条件查询活动列表
	QueryPromotionShopByShopIds(ctx context.Context, in *ReachReducePromotionShopQuery, opts ...grpc.CallOption) (*ReachReducePromotionShopResponse, error)
	// 根据满减优惠Id查询相信信息
	QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*ReachReduceByIdResponse, error)
	// 根据活动Id查询参与商品
	QueryPromotionProduct(ctx context.Context, in *ReachReduceProductQueryRequest, opts ...grpc.CallOption) (*ReachReduceProductQueryResponse, error)
	// 查询可以选择的商品列表(编辑满减活动时用来选择包含商品)
	QueryOptionalSelectPromotionProduct(ctx context.Context, in *ReachReduceProductQueryRequest, opts ...grpc.CallOption) (*ReachReduceProductQueryResponse, error)
}

type reachReduceServiceClient struct {
	cc *grpc.ClientConn
}

func NewReachReduceServiceClient(cc *grpc.ClientConn) ReachReduceServiceClient {
	return &reachReduceServiceClient{cc}
}

func (c *reachReduceServiceClient) Add(ctx context.Context, in *ReachReduceAddRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/Add", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) Update(ctx context.Context, in *ReachReduceUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) UpdateIsEnable(ctx context.Context, in *UpdateIsEnableRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/UpdateIsEnable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) DeletePromotionShopById(ctx context.Context, in *DeletePromotonShopByIds, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/DeletePromotionShopById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) DeletePromotionShopByQuery(ctx context.Context, in *ReachReducePromotionShopQuery, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/DeletePromotionShopByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) QueryPromotionShopByShopIds(ctx context.Context, in *ReachReducePromotionShopQuery, opts ...grpc.CallOption) (*ReachReducePromotionShopResponse, error) {
	out := new(ReachReducePromotionShopResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/QueryPromotionShopByShopIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*ReachReduceByIdResponse, error) {
	out := new(ReachReduceByIdResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/QueryById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) QueryPromotionProduct(ctx context.Context, in *ReachReduceProductQueryRequest, opts ...grpc.CallOption) (*ReachReduceProductQueryResponse, error) {
	out := new(ReachReduceProductQueryResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/QueryPromotionProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reachReduceServiceClient) QueryOptionalSelectPromotionProduct(ctx context.Context, in *ReachReduceProductQueryRequest, opts ...grpc.CallOption) (*ReachReduceProductQueryResponse, error) {
	out := new(ReachReduceProductQueryResponse)
	err := c.cc.Invoke(ctx, "/mk.ReachReduceService/QueryOptionalSelectPromotionProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReachReduceServiceServer is the server API for ReachReduceService service.
type ReachReduceServiceServer interface {
	// 添加
	Add(context.Context, *ReachReduceAddRequest) (*BaseResponse, error)
	// 更新
	Update(context.Context, *ReachReduceUpdateRequest) (*BaseResponse, error)
	// 更新是否启用
	UpdateIsEnable(context.Context, *UpdateIsEnableRequest) (*BaseResponse, error)
	// 删除多个店铺
	DeletePromotionShopById(context.Context, *DeletePromotonShopByIds) (*BaseResponse, error)
	// 根据查询条件删除活动与店铺关联关系
	DeletePromotionShopByQuery(context.Context, *ReachReducePromotionShopQuery) (*BaseResponse, error)
	// 根据查询条件查询活动列表
	QueryPromotionShopByShopIds(context.Context, *ReachReducePromotionShopQuery) (*ReachReducePromotionShopResponse, error)
	// 根据满减优惠Id查询相信信息
	QueryById(context.Context, *QueryByIdRequest) (*ReachReduceByIdResponse, error)
	// 根据活动Id查询参与商品
	QueryPromotionProduct(context.Context, *ReachReduceProductQueryRequest) (*ReachReduceProductQueryResponse, error)
	// 查询可以选择的商品列表(编辑满减活动时用来选择包含商品)
	QueryOptionalSelectPromotionProduct(context.Context, *ReachReduceProductQueryRequest) (*ReachReduceProductQueryResponse, error)
}

// UnimplementedReachReduceServiceServer can be embedded to have forward compatible implementations.
type UnimplementedReachReduceServiceServer struct {
}

func (*UnimplementedReachReduceServiceServer) Add(ctx context.Context, req *ReachReduceAddRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (*UnimplementedReachReduceServiceServer) Update(ctx context.Context, req *ReachReduceUpdateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedReachReduceServiceServer) UpdateIsEnable(ctx context.Context, req *UpdateIsEnableRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIsEnable not implemented")
}
func (*UnimplementedReachReduceServiceServer) DeletePromotionShopById(ctx context.Context, req *DeletePromotonShopByIds) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionShopById not implemented")
}
func (*UnimplementedReachReduceServiceServer) DeletePromotionShopByQuery(ctx context.Context, req *ReachReducePromotionShopQuery) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionShopByQuery not implemented")
}
func (*UnimplementedReachReduceServiceServer) QueryPromotionShopByShopIds(ctx context.Context, req *ReachReducePromotionShopQuery) (*ReachReducePromotionShopResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionShopByShopIds not implemented")
}
func (*UnimplementedReachReduceServiceServer) QueryById(ctx context.Context, req *QueryByIdRequest) (*ReachReduceByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryById not implemented")
}
func (*UnimplementedReachReduceServiceServer) QueryPromotionProduct(ctx context.Context, req *ReachReduceProductQueryRequest) (*ReachReduceProductQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionProduct not implemented")
}
func (*UnimplementedReachReduceServiceServer) QueryOptionalSelectPromotionProduct(ctx context.Context, req *ReachReduceProductQueryRequest) (*ReachReduceProductQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryOptionalSelectPromotionProduct not implemented")
}

func RegisterReachReduceServiceServer(s *grpc.Server, srv ReachReduceServiceServer) {
	s.RegisterService(&_ReachReduceService_serviceDesc, srv)
}

func _ReachReduceService_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReduceAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/Add",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).Add(ctx, req.(*ReachReduceAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReduceUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).Update(ctx, req.(*ReachReduceUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_UpdateIsEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIsEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).UpdateIsEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/UpdateIsEnable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).UpdateIsEnable(ctx, req.(*UpdateIsEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_DeletePromotionShopById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePromotonShopByIds)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).DeletePromotionShopById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/DeletePromotionShopById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).DeletePromotionShopById(ctx, req.(*DeletePromotonShopByIds))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_DeletePromotionShopByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReducePromotionShopQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).DeletePromotionShopByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/DeletePromotionShopByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).DeletePromotionShopByQuery(ctx, req.(*ReachReducePromotionShopQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_QueryPromotionShopByShopIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReducePromotionShopQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).QueryPromotionShopByShopIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/QueryPromotionShopByShopIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).QueryPromotionShopByShopIds(ctx, req.(*ReachReducePromotionShopQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_QueryById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).QueryById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/QueryById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).QueryById(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_QueryPromotionProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReduceProductQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).QueryPromotionProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/QueryPromotionProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).QueryPromotionProduct(ctx, req.(*ReachReduceProductQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReachReduceService_QueryOptionalSelectPromotionProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReachReduceProductQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReachReduceServiceServer).QueryOptionalSelectPromotionProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReachReduceService/QueryOptionalSelectPromotionProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReachReduceServiceServer).QueryOptionalSelectPromotionProduct(ctx, req.(*ReachReduceProductQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ReachReduceService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.ReachReduceService",
	HandlerType: (*ReachReduceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Add",
			Handler:    _ReachReduceService_Add_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _ReachReduceService_Update_Handler,
		},
		{
			MethodName: "UpdateIsEnable",
			Handler:    _ReachReduceService_UpdateIsEnable_Handler,
		},
		{
			MethodName: "DeletePromotionShopById",
			Handler:    _ReachReduceService_DeletePromotionShopById_Handler,
		},
		{
			MethodName: "DeletePromotionShopByQuery",
			Handler:    _ReachReduceService_DeletePromotionShopByQuery_Handler,
		},
		{
			MethodName: "QueryPromotionShopByShopIds",
			Handler:    _ReachReduceService_QueryPromotionShopByShopIds_Handler,
		},
		{
			MethodName: "QueryById",
			Handler:    _ReachReduceService_QueryById_Handler,
		},
		{
			MethodName: "QueryPromotionProduct",
			Handler:    _ReachReduceService_QueryPromotionProduct_Handler,
		},
		{
			MethodName: "QueryOptionalSelectPromotionProduct",
			Handler:    _ReachReduceService_QueryOptionalSelectPromotionProduct_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}

// TimeDiscountServiceClient is the client API for TimeDiscountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TimeDiscountServiceClient interface {
	// 添加
	Add(ctx context.Context, in *PromotionTimeDiscountAddRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 修改
	Update(ctx context.Context, in *TimeDiscountUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 根据ID删除限时折扣与商品的关联关系
	DeletePromotionProducById(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除限时折扣与商品的关联关系
	DeletePromotionProducByQuery(ctx context.Context, in *TimeDiscountProductRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 查询限时折扣与商品的关联关系
	QueryPromotionProducByQuery(ctx context.Context, in *TimeDiscountProductRequest, opts ...grpc.CallOption) (*TimeDiscountProductResponse, error)
	// 根据限时折扣Id查询相信信息
	QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*TimeDiscountByIdResponse, error)
	// 根据活动Id和sku查询限时折扣活动的单日库存
	QueryLimitCountBySkus(ctx context.Context, in *QueryLimitCountRequest, opts ...grpc.CallOption) (*QueryLimitCountResponse, error)
	//全局配置
	GetPromotionConfigurationDetail(ctx context.Context, in *GetPromotionConfigurationDetailRequest, opts ...grpc.CallOption) (*GetPromotionConfigurationDetailResponse, error)
	PromotionConfigurationCreateInfo(ctx context.Context, in *PromotionConfigurationCreateInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationCreateInfoResponse, error)
	PromotionConfigurationUpdateInfo(ctx context.Context, in *PromotionConfigurationUpdateInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationUpdateInfoResponse, error)
	PromotionConfigurationReplaceInfo(ctx context.Context, in *PromotionConfigurationReplaceInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationReplaceInfoResponse, error)
}

type timeDiscountServiceClient struct {
	cc *grpc.ClientConn
}

func NewTimeDiscountServiceClient(cc *grpc.ClientConn) TimeDiscountServiceClient {
	return &timeDiscountServiceClient{cc}
}

func (c *timeDiscountServiceClient) Add(ctx context.Context, in *PromotionTimeDiscountAddRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/Add", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) Update(ctx context.Context, in *TimeDiscountUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) DeletePromotionProducById(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/DeletePromotionProducById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) DeletePromotionProducByQuery(ctx context.Context, in *TimeDiscountProductRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/DeletePromotionProducByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) QueryPromotionProducByQuery(ctx context.Context, in *TimeDiscountProductRequest, opts ...grpc.CallOption) (*TimeDiscountProductResponse, error) {
	out := new(TimeDiscountProductResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/QueryPromotionProducByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*TimeDiscountByIdResponse, error) {
	out := new(TimeDiscountByIdResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/QueryById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) QueryLimitCountBySkus(ctx context.Context, in *QueryLimitCountRequest, opts ...grpc.CallOption) (*QueryLimitCountResponse, error) {
	out := new(QueryLimitCountResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/QueryLimitCountBySkus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) GetPromotionConfigurationDetail(ctx context.Context, in *GetPromotionConfigurationDetailRequest, opts ...grpc.CallOption) (*GetPromotionConfigurationDetailResponse, error) {
	out := new(GetPromotionConfigurationDetailResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/GetPromotionConfigurationDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) PromotionConfigurationCreateInfo(ctx context.Context, in *PromotionConfigurationCreateInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationCreateInfoResponse, error) {
	out := new(PromotionConfigurationCreateInfoResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/PromotionConfigurationCreateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) PromotionConfigurationUpdateInfo(ctx context.Context, in *PromotionConfigurationUpdateInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationUpdateInfoResponse, error) {
	out := new(PromotionConfigurationUpdateInfoResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/PromotionConfigurationUpdateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeDiscountServiceClient) PromotionConfigurationReplaceInfo(ctx context.Context, in *PromotionConfigurationReplaceInfoRequest, opts ...grpc.CallOption) (*PromotionConfigurationReplaceInfoResponse, error) {
	out := new(PromotionConfigurationReplaceInfoResponse)
	err := c.cc.Invoke(ctx, "/mk.TimeDiscountService/PromotionConfigurationReplaceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimeDiscountServiceServer is the server API for TimeDiscountService service.
type TimeDiscountServiceServer interface {
	// 添加
	Add(context.Context, *PromotionTimeDiscountAddRequest) (*BaseResponse, error)
	// 修改
	Update(context.Context, *TimeDiscountUpdateRequest) (*BaseResponse, error)
	// 根据ID删除限时折扣与商品的关联关系
	DeletePromotionProducById(context.Context, *DeleteRequest) (*BaseResponse, error)
	// 删除限时折扣与商品的关联关系
	DeletePromotionProducByQuery(context.Context, *TimeDiscountProductRequest) (*BaseResponse, error)
	// 查询限时折扣与商品的关联关系
	QueryPromotionProducByQuery(context.Context, *TimeDiscountProductRequest) (*TimeDiscountProductResponse, error)
	// 根据限时折扣Id查询相信信息
	QueryById(context.Context, *QueryByIdRequest) (*TimeDiscountByIdResponse, error)
	// 根据活动Id和sku查询限时折扣活动的单日库存
	QueryLimitCountBySkus(context.Context, *QueryLimitCountRequest) (*QueryLimitCountResponse, error)
	//全局配置
	GetPromotionConfigurationDetail(context.Context, *GetPromotionConfigurationDetailRequest) (*GetPromotionConfigurationDetailResponse, error)
	PromotionConfigurationCreateInfo(context.Context, *PromotionConfigurationCreateInfoRequest) (*PromotionConfigurationCreateInfoResponse, error)
	PromotionConfigurationUpdateInfo(context.Context, *PromotionConfigurationUpdateInfoRequest) (*PromotionConfigurationUpdateInfoResponse, error)
	PromotionConfigurationReplaceInfo(context.Context, *PromotionConfigurationReplaceInfoRequest) (*PromotionConfigurationReplaceInfoResponse, error)
}

// UnimplementedTimeDiscountServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTimeDiscountServiceServer struct {
}

func (*UnimplementedTimeDiscountServiceServer) Add(ctx context.Context, req *PromotionTimeDiscountAddRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) Update(ctx context.Context, req *TimeDiscountUpdateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) DeletePromotionProducById(ctx context.Context, req *DeleteRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionProducById not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) DeletePromotionProducByQuery(ctx context.Context, req *TimeDiscountProductRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionProducByQuery not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) QueryPromotionProducByQuery(ctx context.Context, req *TimeDiscountProductRequest) (*TimeDiscountProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionProducByQuery not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) QueryById(ctx context.Context, req *QueryByIdRequest) (*TimeDiscountByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryById not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) QueryLimitCountBySkus(ctx context.Context, req *QueryLimitCountRequest) (*QueryLimitCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryLimitCountBySkus not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) GetPromotionConfigurationDetail(ctx context.Context, req *GetPromotionConfigurationDetailRequest) (*GetPromotionConfigurationDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionConfigurationDetail not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) PromotionConfigurationCreateInfo(ctx context.Context, req *PromotionConfigurationCreateInfoRequest) (*PromotionConfigurationCreateInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromotionConfigurationCreateInfo not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) PromotionConfigurationUpdateInfo(ctx context.Context, req *PromotionConfigurationUpdateInfoRequest) (*PromotionConfigurationUpdateInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromotionConfigurationUpdateInfo not implemented")
}
func (*UnimplementedTimeDiscountServiceServer) PromotionConfigurationReplaceInfo(ctx context.Context, req *PromotionConfigurationReplaceInfoRequest) (*PromotionConfigurationReplaceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PromotionConfigurationReplaceInfo not implemented")
}

func RegisterTimeDiscountServiceServer(s *grpc.Server, srv TimeDiscountServiceServer) {
	s.RegisterService(&_TimeDiscountService_serviceDesc, srv)
}

func _TimeDiscountService_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionTimeDiscountAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/Add",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).Add(ctx, req.(*PromotionTimeDiscountAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeDiscountUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).Update(ctx, req.(*TimeDiscountUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_DeletePromotionProducById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).DeletePromotionProducById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/DeletePromotionProducById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).DeletePromotionProducById(ctx, req.(*DeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_DeletePromotionProducByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeDiscountProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).DeletePromotionProducByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/DeletePromotionProducByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).DeletePromotionProducByQuery(ctx, req.(*TimeDiscountProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_QueryPromotionProducByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeDiscountProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).QueryPromotionProducByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/QueryPromotionProducByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).QueryPromotionProducByQuery(ctx, req.(*TimeDiscountProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_QueryById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).QueryById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/QueryById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).QueryById(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_QueryLimitCountBySkus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryLimitCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).QueryLimitCountBySkus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/QueryLimitCountBySkus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).QueryLimitCountBySkus(ctx, req.(*QueryLimitCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_GetPromotionConfigurationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPromotionConfigurationDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).GetPromotionConfigurationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/GetPromotionConfigurationDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).GetPromotionConfigurationDetail(ctx, req.(*GetPromotionConfigurationDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_PromotionConfigurationCreateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionConfigurationCreateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationCreateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/PromotionConfigurationCreateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationCreateInfo(ctx, req.(*PromotionConfigurationCreateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_PromotionConfigurationUpdateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionConfigurationUpdateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationUpdateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/PromotionConfigurationUpdateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationUpdateInfo(ctx, req.(*PromotionConfigurationUpdateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeDiscountService_PromotionConfigurationReplaceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionConfigurationReplaceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationReplaceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.TimeDiscountService/PromotionConfigurationReplaceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeDiscountServiceServer).PromotionConfigurationReplaceInfo(ctx, req.(*PromotionConfigurationReplaceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TimeDiscountService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.TimeDiscountService",
	HandlerType: (*TimeDiscountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Add",
			Handler:    _TimeDiscountService_Add_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _TimeDiscountService_Update_Handler,
		},
		{
			MethodName: "DeletePromotionProducById",
			Handler:    _TimeDiscountService_DeletePromotionProducById_Handler,
		},
		{
			MethodName: "DeletePromotionProducByQuery",
			Handler:    _TimeDiscountService_DeletePromotionProducByQuery_Handler,
		},
		{
			MethodName: "QueryPromotionProducByQuery",
			Handler:    _TimeDiscountService_QueryPromotionProducByQuery_Handler,
		},
		{
			MethodName: "QueryById",
			Handler:    _TimeDiscountService_QueryById_Handler,
		},
		{
			MethodName: "QueryLimitCountBySkus",
			Handler:    _TimeDiscountService_QueryLimitCountBySkus_Handler,
		},
		{
			MethodName: "GetPromotionConfigurationDetail",
			Handler:    _TimeDiscountService_GetPromotionConfigurationDetail_Handler,
		},
		{
			MethodName: "PromotionConfigurationCreateInfo",
			Handler:    _TimeDiscountService_PromotionConfigurationCreateInfo_Handler,
		},
		{
			MethodName: "PromotionConfigurationUpdateInfo",
			Handler:    _TimeDiscountService_PromotionConfigurationUpdateInfo_Handler,
		},
		{
			MethodName: "PromotionConfigurationReplaceInfo",
			Handler:    _TimeDiscountService_PromotionConfigurationReplaceInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}

// ReduceDeliveryServiceClient is the client API for ReduceDeliveryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ReduceDeliveryServiceClient interface {
	// 添加
	Add(ctx context.Context, in *ReduceDeliveryAddRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 修改
	Update(ctx context.Context, in *ReduceDeliveryUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 启用与撤销满减运费活动
	UpdateIsEnable(ctx context.Context, in *UpdateIsEnableRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 根据ID删除单个满减运费与店铺的关联关系
	DeletePromotionShopById(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 删除满减运费与店铺的关联关系
	DeletePromotionShopByQuery(ctx context.Context, in *ReduceDeliveryShopRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 查询满减运费与店铺的关联关系
	QueryPromotionShopByQuery(ctx context.Context, in *ReduceDeliveryShopRequest, opts ...grpc.CallOption) (*ReduceDeliveryShopResponse, error)
	// 根据满减运费Id查询相信信息
	QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*ReduceDeliveryByIdResponse, error)
}

type reduceDeliveryServiceClient struct {
	cc *grpc.ClientConn
}

func NewReduceDeliveryServiceClient(cc *grpc.ClientConn) ReduceDeliveryServiceClient {
	return &reduceDeliveryServiceClient{cc}
}

func (c *reduceDeliveryServiceClient) Add(ctx context.Context, in *ReduceDeliveryAddRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/Add", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) Update(ctx context.Context, in *ReduceDeliveryUpdateRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) UpdateIsEnable(ctx context.Context, in *UpdateIsEnableRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/UpdateIsEnable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) DeletePromotionShopById(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/DeletePromotionShopById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) DeletePromotionShopByQuery(ctx context.Context, in *ReduceDeliveryShopRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/DeletePromotionShopByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) QueryPromotionShopByQuery(ctx context.Context, in *ReduceDeliveryShopRequest, opts ...grpc.CallOption) (*ReduceDeliveryShopResponse, error) {
	out := new(ReduceDeliveryShopResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/QueryPromotionShopByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reduceDeliveryServiceClient) QueryById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*ReduceDeliveryByIdResponse, error) {
	out := new(ReduceDeliveryByIdResponse)
	err := c.cc.Invoke(ctx, "/mk.ReduceDeliveryService/QueryById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReduceDeliveryServiceServer is the server API for ReduceDeliveryService service.
type ReduceDeliveryServiceServer interface {
	// 添加
	Add(context.Context, *ReduceDeliveryAddRequest) (*BaseResponse, error)
	// 修改
	Update(context.Context, *ReduceDeliveryUpdateRequest) (*BaseResponse, error)
	// 启用与撤销满减运费活动
	UpdateIsEnable(context.Context, *UpdateIsEnableRequest) (*BaseResponse, error)
	// 根据ID删除单个满减运费与店铺的关联关系
	DeletePromotionShopById(context.Context, *DeleteRequest) (*BaseResponse, error)
	// 删除满减运费与店铺的关联关系
	DeletePromotionShopByQuery(context.Context, *ReduceDeliveryShopRequest) (*BaseResponse, error)
	// 查询满减运费与店铺的关联关系
	QueryPromotionShopByQuery(context.Context, *ReduceDeliveryShopRequest) (*ReduceDeliveryShopResponse, error)
	// 根据满减运费Id查询相信信息
	QueryById(context.Context, *QueryByIdRequest) (*ReduceDeliveryByIdResponse, error)
}

// UnimplementedReduceDeliveryServiceServer can be embedded to have forward compatible implementations.
type UnimplementedReduceDeliveryServiceServer struct {
}

func (*UnimplementedReduceDeliveryServiceServer) Add(ctx context.Context, req *ReduceDeliveryAddRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) Update(ctx context.Context, req *ReduceDeliveryUpdateRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) UpdateIsEnable(ctx context.Context, req *UpdateIsEnableRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIsEnable not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) DeletePromotionShopById(ctx context.Context, req *DeleteRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionShopById not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) DeletePromotionShopByQuery(ctx context.Context, req *ReduceDeliveryShopRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotionShopByQuery not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) QueryPromotionShopByQuery(ctx context.Context, req *ReduceDeliveryShopRequest) (*ReduceDeliveryShopResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionShopByQuery not implemented")
}
func (*UnimplementedReduceDeliveryServiceServer) QueryById(ctx context.Context, req *QueryByIdRequest) (*ReduceDeliveryByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryById not implemented")
}

func RegisterReduceDeliveryServiceServer(s *grpc.Server, srv ReduceDeliveryServiceServer) {
	s.RegisterService(&_ReduceDeliveryService_serviceDesc, srv)
}

func _ReduceDeliveryService_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceDeliveryAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/Add",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).Add(ctx, req.(*ReduceDeliveryAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceDeliveryUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).Update(ctx, req.(*ReduceDeliveryUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_UpdateIsEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIsEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).UpdateIsEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/UpdateIsEnable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).UpdateIsEnable(ctx, req.(*UpdateIsEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_DeletePromotionShopById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).DeletePromotionShopById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/DeletePromotionShopById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).DeletePromotionShopById(ctx, req.(*DeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_DeletePromotionShopByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceDeliveryShopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).DeletePromotionShopByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/DeletePromotionShopByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).DeletePromotionShopByQuery(ctx, req.(*ReduceDeliveryShopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_QueryPromotionShopByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceDeliveryShopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).QueryPromotionShopByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/QueryPromotionShopByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).QueryPromotionShopByQuery(ctx, req.(*ReduceDeliveryShopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReduceDeliveryService_QueryById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReduceDeliveryServiceServer).QueryById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.ReduceDeliveryService/QueryById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReduceDeliveryServiceServer).QueryById(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ReduceDeliveryService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.ReduceDeliveryService",
	HandlerType: (*ReduceDeliveryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Add",
			Handler:    _ReduceDeliveryService_Add_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _ReduceDeliveryService_Update_Handler,
		},
		{
			MethodName: "UpdateIsEnable",
			Handler:    _ReduceDeliveryService_UpdateIsEnable_Handler,
		},
		{
			MethodName: "DeletePromotionShopById",
			Handler:    _ReduceDeliveryService_DeletePromotionShopById_Handler,
		},
		{
			MethodName: "DeletePromotionShopByQuery",
			Handler:    _ReduceDeliveryService_DeletePromotionShopByQuery_Handler,
		},
		{
			MethodName: "QueryPromotionShopByQuery",
			Handler:    _ReduceDeliveryService_QueryPromotionShopByQuery_Handler,
		},
		{
			MethodName: "QueryById",
			Handler:    _ReduceDeliveryService_QueryById_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}

// PromotionTaskServiceClient is the client API for PromotionTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PromotionTaskServiceClient interface {
	// 查询活动任务创建列表
	QueryPromotionTask(ctx context.Context, in *PromotionTaskQueryRequest, opts ...grpc.CallOption) (*PromotionTaskQueryResponse, error)
	// 根据Id 查询任务明细
	QueryPromotionTaskById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PromotionTaskByIdQueryResponse, error)
	// 根据活动Id查询明细
	QueryPromotionTaskDetail(ctx context.Context, in *PromotionTaskDetailQueryRequest, opts ...grpc.CallOption) (*PromotionTaskDetailQueryResponse, error)
}

type promotionTaskServiceClient struct {
	cc *grpc.ClientConn
}

func NewPromotionTaskServiceClient(cc *grpc.ClientConn) PromotionTaskServiceClient {
	return &promotionTaskServiceClient{cc}
}

func (c *promotionTaskServiceClient) QueryPromotionTask(ctx context.Context, in *PromotionTaskQueryRequest, opts ...grpc.CallOption) (*PromotionTaskQueryResponse, error) {
	out := new(PromotionTaskQueryResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionTaskService/QueryPromotionTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionTaskServiceClient) QueryPromotionTaskById(ctx context.Context, in *QueryByIdRequest, opts ...grpc.CallOption) (*PromotionTaskByIdQueryResponse, error) {
	out := new(PromotionTaskByIdQueryResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionTaskService/QueryPromotionTaskById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionTaskServiceClient) QueryPromotionTaskDetail(ctx context.Context, in *PromotionTaskDetailQueryRequest, opts ...grpc.CallOption) (*PromotionTaskDetailQueryResponse, error) {
	out := new(PromotionTaskDetailQueryResponse)
	err := c.cc.Invoke(ctx, "/mk.PromotionTaskService/QueryPromotionTaskDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionTaskServiceServer is the server API for PromotionTaskService service.
type PromotionTaskServiceServer interface {
	// 查询活动任务创建列表
	QueryPromotionTask(context.Context, *PromotionTaskQueryRequest) (*PromotionTaskQueryResponse, error)
	// 根据Id 查询任务明细
	QueryPromotionTaskById(context.Context, *QueryByIdRequest) (*PromotionTaskByIdQueryResponse, error)
	// 根据活动Id查询明细
	QueryPromotionTaskDetail(context.Context, *PromotionTaskDetailQueryRequest) (*PromotionTaskDetailQueryResponse, error)
}

// UnimplementedPromotionTaskServiceServer can be embedded to have forward compatible implementations.
type UnimplementedPromotionTaskServiceServer struct {
}

func (*UnimplementedPromotionTaskServiceServer) QueryPromotionTask(ctx context.Context, req *PromotionTaskQueryRequest) (*PromotionTaskQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionTask not implemented")
}
func (*UnimplementedPromotionTaskServiceServer) QueryPromotionTaskById(ctx context.Context, req *QueryByIdRequest) (*PromotionTaskByIdQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionTaskById not implemented")
}
func (*UnimplementedPromotionTaskServiceServer) QueryPromotionTaskDetail(ctx context.Context, req *PromotionTaskDetailQueryRequest) (*PromotionTaskDetailQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPromotionTaskDetail not implemented")
}

func RegisterPromotionTaskServiceServer(s *grpc.Server, srv PromotionTaskServiceServer) {
	s.RegisterService(&_PromotionTaskService_serviceDesc, srv)
}

func _PromotionTaskService_QueryPromotionTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionTaskQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionTaskServiceServer).QueryPromotionTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionTaskService/QueryPromotionTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionTaskServiceServer).QueryPromotionTask(ctx, req.(*PromotionTaskQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionTaskService_QueryPromotionTaskById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionTaskServiceServer).QueryPromotionTaskById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionTaskService/QueryPromotionTaskById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionTaskServiceServer).QueryPromotionTaskById(ctx, req.(*QueryByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionTaskService_QueryPromotionTaskDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionTaskDetailQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionTaskServiceServer).QueryPromotionTaskDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.PromotionTaskService/QueryPromotionTaskDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionTaskServiceServer).QueryPromotionTaskDetail(ctx, req.(*PromotionTaskDetailQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PromotionTaskService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.PromotionTaskService",
	HandlerType: (*PromotionTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryPromotionTask",
			Handler:    _PromotionTaskService_QueryPromotionTask_Handler,
		},
		{
			MethodName: "QueryPromotionTaskById",
			Handler:    _PromotionTaskService_QueryPromotionTaskById_Handler,
		},
		{
			MethodName: "QueryPromotionTaskDetail",
			Handler:    _PromotionTaskService_QueryPromotionTaskDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}

// MarketServiceClient is the client API for MarketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MarketServiceClient interface {
	// 肋力活动列表
	QueryActivityIds(ctx context.Context, in *ActivityIdsRequest, opts ...grpc.CallOption) (*ActivityIdsResponse, error)
	// 肋力活动删除
	DelActivity(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 肋力活动状态更新
	StatusFreshActivity(ctx context.Context, in *StatusFreshActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 肋力活动新增/编辑
	NewActivity(ctx context.Context, in *NewActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 肋力活动配置查询
	QuerySettingActivity(ctx context.Context, in *QuerySettingActivityRequest, opts ...grpc.CallOption) (*SettingActivityResponse, error)
	// 肋力活动配置保存
	NewSettingActivity(ctx context.Context, in *SettingActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	//流浪救助参与用户列表
	SuccourUser(ctx context.Context, in *UserListRequest, opts ...grpc.CallOption) (*UserListResponse, error)
	//流浪救助参与用户编辑
	SuccourEditUser(ctx context.Context, in *UserRequest, opts ...grpc.CallOption) (*UserResponse, error)
	//二维码列表
	SuccourQrcode(ctx context.Context, in *QrcodeRequest, opts ...grpc.CallOption) (*QrcodeResponse, error)
	//二维码新增
	SuccourQrcodeAdd(ctx context.Context, in *QrcodeAddRequest, opts ...grpc.CallOption) (*QrcodeAddResponse, error)
	//二维码新增
	SuccourQrcodeSet(ctx context.Context, in *QrcodeSetRequest, opts ...grpc.CallOption) (*QrcodeSetResponse, error)
	//救助站列表
	SuccourSalvation(ctx context.Context, in *SalvationRequest, opts ...grpc.CallOption) (*SalvationResponse, error)
	//新增，编辑
	SuccourSalvationAdd(ctx context.Context, in *SalvationAddRequest, opts ...grpc.CallOption) (*SalvationAddResponse, error)
	//设置停止
	SuccourSalvationSet(ctx context.Context, in *SalvationSetRequest, opts ...grpc.CallOption) (*SalvationSetResponse, error)
	//查看凭证
	SuccourSalvationReceiving(ctx context.Context, in *SalvationReceivingRequest, opts ...grpc.CallOption) (*SalvationReceivingResponse, error)
	//上传凭证
	SuccourSalvationReceivingAdd(ctx context.Context, in *SalvationReceivingAddRequest, opts ...grpc.CallOption) (*SalvationReceivingAddResponse, error)
	//设置捐赠限制
	SuccourDonateSet(ctx context.Context, in *DonateSetRequest, opts ...grpc.CallOption) (*DonateSetResponse, error)
	//访问限制
	SuccourQrcodeLimit(ctx context.Context, in *QrcodeLimitRequest, opts ...grpc.CallOption) (*QrcodeLimitResponse, error)
	//修改访问限制
	SuccourQrcodeLimitEdit(ctx context.Context, in *QrcodeLimitEditRequest, opts ...grpc.CallOption) (*QrcodeLimitEditResponse, error)
	// 优惠券活动新增/编辑
	CouponActivityNew(ctx context.Context, in *CouponActivityNewRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 优惠券列表
	CouponActivityList(ctx context.Context, in *CouponListRequest, opts ...grpc.CallOption) (*CouponListResponse, error)
	// 终止优惠券活动
	EndCouponActivity(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
	// 领取优惠券列表
	VoucherList(ctx context.Context, in *VoucherListRequest, opts ...grpc.CallOption) (*VoucherListResponse, error)
	// 领取优惠券列表导出
	VoucherListExport(ctx context.Context, in *VoucherListRequest, opts ...grpc.CallOption) (*VoucherListResponse, error)
	// 增加优惠券领取数量
	AddCouponCount(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error)
}

type marketServiceClient struct {
	cc *grpc.ClientConn
}

func NewMarketServiceClient(cc *grpc.ClientConn) MarketServiceClient {
	return &marketServiceClient{cc}
}

func (c *marketServiceClient) QueryActivityIds(ctx context.Context, in *ActivityIdsRequest, opts ...grpc.CallOption) (*ActivityIdsResponse, error) {
	out := new(ActivityIdsResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/QueryActivityIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) DelActivity(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/DelActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) StatusFreshActivity(ctx context.Context, in *StatusFreshActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/StatusFreshActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) NewActivity(ctx context.Context, in *NewActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/NewActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) QuerySettingActivity(ctx context.Context, in *QuerySettingActivityRequest, opts ...grpc.CallOption) (*SettingActivityResponse, error) {
	out := new(SettingActivityResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/QuerySettingActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) NewSettingActivity(ctx context.Context, in *SettingActivityRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/NewSettingActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourUser(ctx context.Context, in *UserListRequest, opts ...grpc.CallOption) (*UserListResponse, error) {
	out := new(UserListResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourEditUser(ctx context.Context, in *UserRequest, opts ...grpc.CallOption) (*UserResponse, error) {
	out := new(UserResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourEditUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourQrcode(ctx context.Context, in *QrcodeRequest, opts ...grpc.CallOption) (*QrcodeResponse, error) {
	out := new(QrcodeResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourQrcode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourQrcodeAdd(ctx context.Context, in *QrcodeAddRequest, opts ...grpc.CallOption) (*QrcodeAddResponse, error) {
	out := new(QrcodeAddResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourQrcodeAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourQrcodeSet(ctx context.Context, in *QrcodeSetRequest, opts ...grpc.CallOption) (*QrcodeSetResponse, error) {
	out := new(QrcodeSetResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourQrcodeSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourSalvation(ctx context.Context, in *SalvationRequest, opts ...grpc.CallOption) (*SalvationResponse, error) {
	out := new(SalvationResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourSalvation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourSalvationAdd(ctx context.Context, in *SalvationAddRequest, opts ...grpc.CallOption) (*SalvationAddResponse, error) {
	out := new(SalvationAddResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourSalvationAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourSalvationSet(ctx context.Context, in *SalvationSetRequest, opts ...grpc.CallOption) (*SalvationSetResponse, error) {
	out := new(SalvationSetResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourSalvationSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourSalvationReceiving(ctx context.Context, in *SalvationReceivingRequest, opts ...grpc.CallOption) (*SalvationReceivingResponse, error) {
	out := new(SalvationReceivingResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourSalvationReceiving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourSalvationReceivingAdd(ctx context.Context, in *SalvationReceivingAddRequest, opts ...grpc.CallOption) (*SalvationReceivingAddResponse, error) {
	out := new(SalvationReceivingAddResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourSalvationReceivingAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourDonateSet(ctx context.Context, in *DonateSetRequest, opts ...grpc.CallOption) (*DonateSetResponse, error) {
	out := new(DonateSetResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourDonateSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourQrcodeLimit(ctx context.Context, in *QrcodeLimitRequest, opts ...grpc.CallOption) (*QrcodeLimitResponse, error) {
	out := new(QrcodeLimitResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourQrcodeLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SuccourQrcodeLimitEdit(ctx context.Context, in *QrcodeLimitEditRequest, opts ...grpc.CallOption) (*QrcodeLimitEditResponse, error) {
	out := new(QrcodeLimitEditResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/SuccourQrcodeLimitEdit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) CouponActivityNew(ctx context.Context, in *CouponActivityNewRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/CouponActivityNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) CouponActivityList(ctx context.Context, in *CouponListRequest, opts ...grpc.CallOption) (*CouponListResponse, error) {
	out := new(CouponListResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/CouponActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) EndCouponActivity(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/EndCouponActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) VoucherList(ctx context.Context, in *VoucherListRequest, opts ...grpc.CallOption) (*VoucherListResponse, error) {
	out := new(VoucherListResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/VoucherList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) VoucherListExport(ctx context.Context, in *VoucherListRequest, opts ...grpc.CallOption) (*VoucherListResponse, error) {
	out := new(VoucherListResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/VoucherListExport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) AddCouponCount(ctx context.Context, in *IdRequest, opts ...grpc.CallOption) (*BaseResponse, error) {
	out := new(BaseResponse)
	err := c.cc.Invoke(ctx, "/mk.MarketService/AddCouponCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketServiceServer is the server API for MarketService service.
type MarketServiceServer interface {
	// 肋力活动列表
	QueryActivityIds(context.Context, *ActivityIdsRequest) (*ActivityIdsResponse, error)
	// 肋力活动删除
	DelActivity(context.Context, *IdRequest) (*BaseResponse, error)
	// 肋力活动状态更新
	StatusFreshActivity(context.Context, *StatusFreshActivityRequest) (*BaseResponse, error)
	// 肋力活动新增/编辑
	NewActivity(context.Context, *NewActivityRequest) (*BaseResponse, error)
	// 肋力活动配置查询
	QuerySettingActivity(context.Context, *QuerySettingActivityRequest) (*SettingActivityResponse, error)
	// 肋力活动配置保存
	NewSettingActivity(context.Context, *SettingActivityRequest) (*BaseResponse, error)
	//流浪救助参与用户列表
	SuccourUser(context.Context, *UserListRequest) (*UserListResponse, error)
	//流浪救助参与用户编辑
	SuccourEditUser(context.Context, *UserRequest) (*UserResponse, error)
	//二维码列表
	SuccourQrcode(context.Context, *QrcodeRequest) (*QrcodeResponse, error)
	//二维码新增
	SuccourQrcodeAdd(context.Context, *QrcodeAddRequest) (*QrcodeAddResponse, error)
	//二维码新增
	SuccourQrcodeSet(context.Context, *QrcodeSetRequest) (*QrcodeSetResponse, error)
	//救助站列表
	SuccourSalvation(context.Context, *SalvationRequest) (*SalvationResponse, error)
	//新增，编辑
	SuccourSalvationAdd(context.Context, *SalvationAddRequest) (*SalvationAddResponse, error)
	//设置停止
	SuccourSalvationSet(context.Context, *SalvationSetRequest) (*SalvationSetResponse, error)
	//查看凭证
	SuccourSalvationReceiving(context.Context, *SalvationReceivingRequest) (*SalvationReceivingResponse, error)
	//上传凭证
	SuccourSalvationReceivingAdd(context.Context, *SalvationReceivingAddRequest) (*SalvationReceivingAddResponse, error)
	//设置捐赠限制
	SuccourDonateSet(context.Context, *DonateSetRequest) (*DonateSetResponse, error)
	//访问限制
	SuccourQrcodeLimit(context.Context, *QrcodeLimitRequest) (*QrcodeLimitResponse, error)
	//修改访问限制
	SuccourQrcodeLimitEdit(context.Context, *QrcodeLimitEditRequest) (*QrcodeLimitEditResponse, error)
	// 优惠券活动新增/编辑
	CouponActivityNew(context.Context, *CouponActivityNewRequest) (*BaseResponse, error)
	// 优惠券列表
	CouponActivityList(context.Context, *CouponListRequest) (*CouponListResponse, error)
	// 终止优惠券活动
	EndCouponActivity(context.Context, *IdRequest) (*BaseResponse, error)
	// 领取优惠券列表
	VoucherList(context.Context, *VoucherListRequest) (*VoucherListResponse, error)
	// 领取优惠券列表导出
	VoucherListExport(context.Context, *VoucherListRequest) (*VoucherListResponse, error)
	// 增加优惠券领取数量
	AddCouponCount(context.Context, *IdRequest) (*BaseResponse, error)
}

// UnimplementedMarketServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMarketServiceServer struct {
}

func (*UnimplementedMarketServiceServer) QueryActivityIds(ctx context.Context, req *ActivityIdsRequest) (*ActivityIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryActivityIds not implemented")
}
func (*UnimplementedMarketServiceServer) DelActivity(ctx context.Context, req *IdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelActivity not implemented")
}
func (*UnimplementedMarketServiceServer) StatusFreshActivity(ctx context.Context, req *StatusFreshActivityRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatusFreshActivity not implemented")
}
func (*UnimplementedMarketServiceServer) NewActivity(ctx context.Context, req *NewActivityRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewActivity not implemented")
}
func (*UnimplementedMarketServiceServer) QuerySettingActivity(ctx context.Context, req *QuerySettingActivityRequest) (*SettingActivityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySettingActivity not implemented")
}
func (*UnimplementedMarketServiceServer) NewSettingActivity(ctx context.Context, req *SettingActivityRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewSettingActivity not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourUser(ctx context.Context, req *UserListRequest) (*UserListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourUser not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourEditUser(ctx context.Context, req *UserRequest) (*UserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourEditUser not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourQrcode(ctx context.Context, req *QrcodeRequest) (*QrcodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourQrcode not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourQrcodeAdd(ctx context.Context, req *QrcodeAddRequest) (*QrcodeAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourQrcodeAdd not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourQrcodeSet(ctx context.Context, req *QrcodeSetRequest) (*QrcodeSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourQrcodeSet not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourSalvation(ctx context.Context, req *SalvationRequest) (*SalvationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourSalvation not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourSalvationAdd(ctx context.Context, req *SalvationAddRequest) (*SalvationAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourSalvationAdd not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourSalvationSet(ctx context.Context, req *SalvationSetRequest) (*SalvationSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourSalvationSet not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourSalvationReceiving(ctx context.Context, req *SalvationReceivingRequest) (*SalvationReceivingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourSalvationReceiving not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourSalvationReceivingAdd(ctx context.Context, req *SalvationReceivingAddRequest) (*SalvationReceivingAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourSalvationReceivingAdd not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourDonateSet(ctx context.Context, req *DonateSetRequest) (*DonateSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourDonateSet not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourQrcodeLimit(ctx context.Context, req *QrcodeLimitRequest) (*QrcodeLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourQrcodeLimit not implemented")
}
func (*UnimplementedMarketServiceServer) SuccourQrcodeLimitEdit(ctx context.Context, req *QrcodeLimitEditRequest) (*QrcodeLimitEditResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuccourQrcodeLimitEdit not implemented")
}
func (*UnimplementedMarketServiceServer) CouponActivityNew(ctx context.Context, req *CouponActivityNewRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CouponActivityNew not implemented")
}
func (*UnimplementedMarketServiceServer) CouponActivityList(ctx context.Context, req *CouponListRequest) (*CouponListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CouponActivityList not implemented")
}
func (*UnimplementedMarketServiceServer) EndCouponActivity(ctx context.Context, req *IdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EndCouponActivity not implemented")
}
func (*UnimplementedMarketServiceServer) VoucherList(ctx context.Context, req *VoucherListRequest) (*VoucherListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VoucherList not implemented")
}
func (*UnimplementedMarketServiceServer) VoucherListExport(ctx context.Context, req *VoucherListRequest) (*VoucherListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VoucherListExport not implemented")
}
func (*UnimplementedMarketServiceServer) AddCouponCount(ctx context.Context, req *IdRequest) (*BaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCouponCount not implemented")
}

func RegisterMarketServiceServer(s *grpc.Server, srv MarketServiceServer) {
	s.RegisterService(&_MarketService_serviceDesc, srv)
}

func _MarketService_QueryActivityIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).QueryActivityIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/QueryActivityIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).QueryActivityIds(ctx, req.(*ActivityIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_DelActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).DelActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/DelActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).DelActivity(ctx, req.(*IdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_StatusFreshActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatusFreshActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).StatusFreshActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/StatusFreshActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).StatusFreshActivity(ctx, req.(*StatusFreshActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_NewActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).NewActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/NewActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).NewActivity(ctx, req.(*NewActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_QuerySettingActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySettingActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).QuerySettingActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/QuerySettingActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).QuerySettingActivity(ctx, req.(*QuerySettingActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_NewSettingActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettingActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).NewSettingActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/NewSettingActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).NewSettingActivity(ctx, req.(*SettingActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourUser(ctx, req.(*UserListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourEditUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourEditUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourEditUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourEditUser(ctx, req.(*UserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourQrcode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrcodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourQrcode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourQrcode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourQrcode(ctx, req.(*QrcodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourQrcodeAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrcodeAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourQrcodeAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourQrcodeAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourQrcodeAdd(ctx, req.(*QrcodeAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourQrcodeSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrcodeSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourQrcodeSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourQrcodeSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourQrcodeSet(ctx, req.(*QrcodeSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourSalvation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalvationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourSalvation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourSalvation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourSalvation(ctx, req.(*SalvationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourSalvationAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalvationAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourSalvationAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourSalvationAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourSalvationAdd(ctx, req.(*SalvationAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourSalvationSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalvationSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourSalvationSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourSalvationSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourSalvationSet(ctx, req.(*SalvationSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourSalvationReceiving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalvationReceivingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourSalvationReceiving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourSalvationReceiving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourSalvationReceiving(ctx, req.(*SalvationReceivingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourSalvationReceivingAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalvationReceivingAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourSalvationReceivingAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourSalvationReceivingAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourSalvationReceivingAdd(ctx, req.(*SalvationReceivingAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourDonateSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DonateSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourDonateSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourDonateSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourDonateSet(ctx, req.(*DonateSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourQrcodeLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrcodeLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourQrcodeLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourQrcodeLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourQrcodeLimit(ctx, req.(*QrcodeLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SuccourQrcodeLimitEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrcodeLimitEditRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SuccourQrcodeLimitEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/SuccourQrcodeLimitEdit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SuccourQrcodeLimitEdit(ctx, req.(*QrcodeLimitEditRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_CouponActivityNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CouponActivityNewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).CouponActivityNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/CouponActivityNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).CouponActivityNew(ctx, req.(*CouponActivityNewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_CouponActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CouponListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).CouponActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/CouponActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).CouponActivityList(ctx, req.(*CouponListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_EndCouponActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).EndCouponActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/EndCouponActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).EndCouponActivity(ctx, req.(*IdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_VoucherList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoucherListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).VoucherList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/VoucherList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).VoucherList(ctx, req.(*VoucherListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_VoucherListExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoucherListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).VoucherListExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/VoucherListExport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).VoucherListExport(ctx, req.(*VoucherListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_AddCouponCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).AddCouponCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mk.MarketService/AddCouponCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).AddCouponCount(ctx, req.(*IdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MarketService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mk.MarketService",
	HandlerType: (*MarketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryActivityIds",
			Handler:    _MarketService_QueryActivityIds_Handler,
		},
		{
			MethodName: "DelActivity",
			Handler:    _MarketService_DelActivity_Handler,
		},
		{
			MethodName: "StatusFreshActivity",
			Handler:    _MarketService_StatusFreshActivity_Handler,
		},
		{
			MethodName: "NewActivity",
			Handler:    _MarketService_NewActivity_Handler,
		},
		{
			MethodName: "QuerySettingActivity",
			Handler:    _MarketService_QuerySettingActivity_Handler,
		},
		{
			MethodName: "NewSettingActivity",
			Handler:    _MarketService_NewSettingActivity_Handler,
		},
		{
			MethodName: "SuccourUser",
			Handler:    _MarketService_SuccourUser_Handler,
		},
		{
			MethodName: "SuccourEditUser",
			Handler:    _MarketService_SuccourEditUser_Handler,
		},
		{
			MethodName: "SuccourQrcode",
			Handler:    _MarketService_SuccourQrcode_Handler,
		},
		{
			MethodName: "SuccourQrcodeAdd",
			Handler:    _MarketService_SuccourQrcodeAdd_Handler,
		},
		{
			MethodName: "SuccourQrcodeSet",
			Handler:    _MarketService_SuccourQrcodeSet_Handler,
		},
		{
			MethodName: "SuccourSalvation",
			Handler:    _MarketService_SuccourSalvation_Handler,
		},
		{
			MethodName: "SuccourSalvationAdd",
			Handler:    _MarketService_SuccourSalvationAdd_Handler,
		},
		{
			MethodName: "SuccourSalvationSet",
			Handler:    _MarketService_SuccourSalvationSet_Handler,
		},
		{
			MethodName: "SuccourSalvationReceiving",
			Handler:    _MarketService_SuccourSalvationReceiving_Handler,
		},
		{
			MethodName: "SuccourSalvationReceivingAdd",
			Handler:    _MarketService_SuccourSalvationReceivingAdd_Handler,
		},
		{
			MethodName: "SuccourDonateSet",
			Handler:    _MarketService_SuccourDonateSet_Handler,
		},
		{
			MethodName: "SuccourQrcodeLimit",
			Handler:    _MarketService_SuccourQrcodeLimit_Handler,
		},
		{
			MethodName: "SuccourQrcodeLimitEdit",
			Handler:    _MarketService_SuccourQrcodeLimitEdit_Handler,
		},
		{
			MethodName: "CouponActivityNew",
			Handler:    _MarketService_CouponActivityNew_Handler,
		},
		{
			MethodName: "CouponActivityList",
			Handler:    _MarketService_CouponActivityList_Handler,
		},
		{
			MethodName: "EndCouponActivity",
			Handler:    _MarketService_EndCouponActivity_Handler,
		},
		{
			MethodName: "VoucherList",
			Handler:    _MarketService_VoucherList_Handler,
		},
		{
			MethodName: "VoucherListExport",
			Handler:    _MarketService_VoucherListExport_Handler,
		},
		{
			MethodName: "AddCouponCount",
			Handler:    _MarketService_AddCouponCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mk/service.proto",
}
