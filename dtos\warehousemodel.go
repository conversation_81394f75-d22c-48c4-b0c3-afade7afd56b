package dtos

import "sort"

type WarehouseModel struct {
	Id          int    `xorm:"not null pk autoincr comment('自增，仓库id') INT(11)"`
	Thirdid     string `xorm:"not null default '''' comment('第三方仓库ID 例如a8id,管易id') VARCHAR(32)"`
	Code        string `xorm:"not null default '''' comment('仓库编号') VARCHAR(32)"`
	Name        string `xorm:"not null default '''' comment('仓库名称') VARCHAR(32)"`
	Comefrom    int    `xorm:"not null default 1 comment('仓库归属(1-A8,2-管易)') INT(11)"`
	Level       int    `xorm:"not null default 1 comment('仓库等级(1-1级，2-2级，3-3级，4-4级，5-5级)') INT(11)"`
	Category    int    `xorm:"not null default 1 comment('仓库类型(1-中心仓，2-区域仓，3-门店仓，4-前置仓)') INT(11)"`
	Arealevel   int    `xorm:"not null default 1 comment('配送区域等级(1-一级，2-二级，3-3级)') INT(11)"`
	Areaid      int    `xorm:"not null default 0 comment('区域id') INT(11)"`
	Enough      bool
	Goodsnumber int
	GoodsType   int
}

type Wrapper struct {
	warehouse []*WarehouseModel
	by        func(p, q *WarehouseModel) bool
}

type SortBy func(p, q *WarehouseModel) bool

func (pw Wrapper) Len() int { // 重写 Len() 方法
	return len(pw.warehouse)
}
func (pw Wrapper) Swap(i, j int) { // 重写 Swap() 方法
	pw.warehouse[i], pw.warehouse[j] = pw.warehouse[j], pw.warehouse[i]
}
func (pw Wrapper) Less(i, j int) bool { // 重写 Less() 方法
	return pw.by(pw.warehouse[i], pw.warehouse[j])
}

// 封装成 SortLog 方法
func SortLog(warehouse []*WarehouseModel, by SortBy) {
	sort.Sort(Wrapper{warehouse, by})
}
